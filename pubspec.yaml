name: text_generation_video
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.4+19

environment:
  sdk: '>=3.3.2 <4.0.0'

#anythink_sdk:
#  android:
#    baidu: false
#    gdt: false
#    csj: true
#    ks: false
#  ios:
#    baidu: false
#    gdt: false
#    csj: true
#    ks: false

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  go_router: ^13.1.0

  ui_widgets:
    git:
      url: https://gitee.com/franky_lee/ms_widgets.git

  ms_http:
    git:
      url: https://gitee.com/franky_lee/ms_http.git

  # shared preferences
  shared_preferences: ^2.0.13

  # riverpod
  # 生成代码运行： flutter pub run build_runner watch
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # screen adapting
  flutter_screenutil: ^5.9.0

  # dio network
  dio: ^5.1.2

  # smart dialog
  flutter_smart_dialog: ^4.9.0+6

  # json generator
  # 生成代码运行：flutter pub run build_runner build
  json_annotation: ^4.8.1

  # webview
  flutter_inappwebview: ^6.1.5

  # url launcher
  url_launcher: ^6.3.1

  # 文件路径
  path_provider: ^2.1.1
  path: ^1.9.1

  # 视频播放
  video_player: ^2.8.5

  # 视频图片保存
  gal: ^2.3.0

  # 获取app包信息
  package_info_plus: ^8.3.0

  # app内购
  in_app_purchase: 3.2.0
  in_app_purchase_storekit: 0.3.18+1

  # 轮播
  carousel_slider: ^5.1.1
  smooth_page_indicator: ^1.2.1

  # markdown
  markdown_widget: ^2.3.2+8

  # 图片文件选择
  image_picker: ^1.1.2
  file_picker: ^10.2.0

  # 图片缓存编辑
  extended_image: ^10.0.1
  cached_network_image: ^3.4.1

  # 网络连接状态
  connectivity_plus: ^6.1.4

  # 摄像头
  camera: ^0.11.1

  # 权限和设备信息
  permission_handler: ^12.0.1
  device_info_plus: ^11.5.0

  # 瀑布流
  waterfall_flow: ^3.1.1

  # 音频录制
  record: ^6.1.1

  # ad sdk
#  anythink_sdk:
#    git:
#      url: https://gitee.com/payneg/anything_sdk.git
#      ref: '1.4'

dependency_overrides:
  in_app_purchase_storekit: 0.3.18+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

  in_app_purchase_platform_interface: ^1.4.0

  # riverpod module
  build_runner:
  custom_lint:
  riverpod_generator: ^2.4.0
  riverpod_lint: ^2.3.10
  json_serializable: ^6.7.0

flutter:

  # 启用 generate 标志
  generate: true

  uses-material-design: true

  assets:
    - assets/images/
    - assets/fashion_shoot/
    - assets/poster_design/
    - assets/images/base/
    - assets/images/restoration/
    - assets/images/power/
