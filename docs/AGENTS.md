## Project Structure & Module Organization
- `lib/main.dart` wires the Flutter entrypoint, loads global providers, and configures routing.
- `lib/app/` contains feature flows (pages, view models, routing); mirror route names when adding screens.
- `lib/common/` holds shared UI widgets and mixins; reuse components here before adding new helpers.
- `lib/config/` stores environment constants, remote endpoints, and feature toggles; keep per-flavor overrides here.
- `lib/utils/` houses pure helpers (formatters, storage adapters); avoid long-lived state in this layer.
- `assets/images/` and subfolders host static art referenced via `pubspec.yaml`; run `flutter pub get` after adding assets.
- `test/` mirrors `lib/`; group widget tests under matching feature folders to keep intent clear.
- Platform folders (`android/`, `ios/`, `macos/`, `linux/`, `windows/`, `web/`) should change only when platform-specific code is required.

## Build, Test, and Development Commands
- `flutter pub get` resolves dependencies and updates generated plugin registries.
- `flutter pub run build_runner build --delete-conflicting-outputs` regenerates Riverpod and JSON adapters after model or provider changes.
- `dart run custom_lint` executes repository lint rules; fix or suppress findings before review.
- `flutter analyze` runs static checks; the CI expects a clean report.
- `flutter test` executes unit and widget tests; add `--coverage` when validating coverage locally.
- `flutter run -d <device>` launches the app on a connected device or emulator for smoke testing.

## Coding Style & Naming Conventions
- Follow `flutter_lints`: 2-space indentation, trailing commas in widget trees, and `dart format` before committing.
- Use PascalCase for widgets/providers, lowerCamelCase for methods and variables, SCREAMING_SNAKE_CASE only for constants in `config`.
- Name Riverpod providers `<Feature><Capability>Provider` and place them alongside their widgets for discoverability.
- Keep widget files focused; split reusable pieces into `lib/common/` once shared beyond one screen.
- Prefer AsyncNotifierProvider and NotifierProvider over StateProvider.Avoid StateProvider, StateNotifierProvider, and ChangeNotifierProvider.

## Testing Guidelines
- Add companion tests for new logic in `test/<feature>/<name>_test.dart` to mirror production code structure.
- Prefer Riverpod overrides and fake repositories over real network calls in tests.
- Add widget tests for UI regressions; store golden baselines under `test/goldens/` when visual drift matters.
- Maintain or improve coverage; document temporary gaps in the PR description.

## Commit & Pull Request Guidelines
- Use conventional messages (`feat:`, `fix:`, `refactor:`) as seen in history; keep subjects concise (<70 chars) and bilingual only when helpful.
- Squash commits locally when possible; force pushes are acceptable on feature branches only.
- PRs must include: purpose summary, screenshots for UI tweaks, a bullet list of verification steps (commands or devices), and linked issue IDs.
- Request reviews from module owners for files under `lib/app/...` and ensure CI (analyze + test) completes before assigning reviewers.

## Configuration & Secrets
- Never hardcode API keys; load them via secure storage and document setup steps under `lib/config/`.
- Update platform-specific plist/manifest files only when introducing new capabilities and capture the change in the PR checklist.

