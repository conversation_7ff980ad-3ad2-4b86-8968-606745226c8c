import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_example.dart';

part 'same_example_list.g.dart';

@JsonSerializable()
class SameExampleList {
  bool? hasNextPage;
  List<SameExample>? list;
  int? total;

  SameExampleList();

  factory SameExampleList.fromJson(Map<String, dynamic> json) =>
      _$SameExampleListFromJson(json);

  Map<String, dynamic> toJson() => _$SameExampleListToJson(this);
}
