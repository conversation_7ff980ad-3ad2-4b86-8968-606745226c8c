// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'same_example.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SameExample _$SameExampleFromJson(Map<String, dynamic> json) => SameExample()
  ..authorHeadImg = json['authorHeadImg'] as String?
  ..authorName = json['authorName'] as String?
  ..caseId = (json['caseId'] as num?)?.toInt()
  ..categoryType = (json['categoryType'] as num?)?.toInt()
  ..collectionCount = (json['collectionCount'] as num?)?.toInt()
  ..createTime = json['createTime'] as String?
  ..dataType = (json['dataType'] as num?)?.toInt()
  ..exampleData = json['exampleData'] as String?
  ..exampleName = json['exampleName'] as String?
  ..functionConfig = json['functionConfig'] as String?
  ..functionType = (json['functionType'] as num?)?.toInt()
  ..id = (json['id'] as num?)?.toInt()
  ..previewImgUrl = json['previewImgUrl'] as String?
  ..updateTime = json['updateTime'] as String?;

Map<String, dynamic> _$SameExampleToJson(SameExample instance) =>
    <String, dynamic>{
      'authorHeadImg': instance.authorHeadImg,
      'authorName': instance.authorName,
      'caseId': instance.caseId,
      'categoryType': instance.categoryType,
      'collectionCount': instance.collectionCount,
      'createTime': instance.createTime,
      'dataType': instance.dataType,
      'exampleData': instance.exampleData,
      'exampleName': instance.exampleName,
      'functionConfig': instance.functionConfig,
      'functionType': instance.functionType,
      'id': instance.id,
      'previewImgUrl': instance.previewImgUrl,
      'updateTime': instance.updateTime,
    };
