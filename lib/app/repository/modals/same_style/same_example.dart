import 'package:json_annotation/json_annotation.dart';

part 'same_example.g.dart';

@JsonSerializable()
class SameExample {
  String? authorHeadImg;
  String? authorName;
  int? caseId;
  int? categoryType;
  int? collectionCount;
  String? createTime;
  int? dataType;
  String? exampleData;
  String? exampleName;
  String? functionConfig;
  int? functionType;
  int? id;
  String? previewImgUrl;
  String? updateTime;

  SameExample();

  factory SameExample.fromJson(Map<String, dynamic> json) =>
      _$SameExampleFromJson(json);

  Map<String, dynamic> toJson() => _$SameExampleToJson(this);
}