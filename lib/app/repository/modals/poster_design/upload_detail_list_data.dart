import 'package:json_annotation/json_annotation.dart';

part 'upload_detail_list_data.g.dart';

@JsonSerializable()
class UploadDetailListData {
  String? createTime;
  int? id;
  String? updateTime;
  String? uploadContent;
  int? uploadType;
  int? workRecordId;

  UploadDetailListData();

  factory UploadDetailListData.fromJson(Map<String, dynamic> json) =>
      _$UploadDetailListDataFromJson(json);

  Map<String, dynamic> toJson() => _$UploadDetailListDataToJson(this);
}
