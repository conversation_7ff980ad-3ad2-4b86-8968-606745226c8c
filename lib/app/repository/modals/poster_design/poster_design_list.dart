import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/poster_design_data.dart';

part 'poster_design_list.g.dart';

@JsonSerializable()
class PosterDesignList {
  List<PosterDesignData>? list;

  PosterDesignList();

  factory PosterDesignList.fromJson(Map<String, dynamic> json) =>
      _$PosterDesignListFromJson(json);

  Map<String, dynamic> toJson() => _$PosterDesignListToJson(this);
}
