// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_work_record_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AiWorkRecordData _$AiWorkRecordDataFromJson(Map<String, dynamic> json) =>
    AiWorkRecordData()
      ..categoryType = (json['categoryType'] as num?)?.toInt()
      ..createTime = json['createTime'] as String?
      ..expireTime = json['expireTime'] as String?
      ..functionType = (json['functionType'] as num?)?.toInt()
      ..id = (json['id'] as num?)?.toInt()
      ..previewImgUrl = json['previewImgUrl'] as String?
      ..state = (json['state'] as num?)?.toInt()
      ..taskId = json['taskId'] as String?
      ..updateTime = json['updateTime'] as String?
      ..userId = (json['userId'] as num?)?.toInt()
      ..workName = json['workName'] as String?
      ..uploadDetailList = (json['uploadDetailList'] as List<dynamic>?)
          ?.map((e) => UploadDetailListData.fromJson(e as Map<String, dynamic>))
          .toList()
      ..recordDetailList = (json['recordDetailList'] as List<dynamic>?)
          ?.map((e) => RecordDetailListData.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$AiWorkRecordDataToJson(AiWorkRecordData instance) =>
    <String, dynamic>{
      'categoryType': instance.categoryType,
      'createTime': instance.createTime,
      'expireTime': instance.expireTime,
      'functionType': instance.functionType,
      'id': instance.id,
      'previewImgUrl': instance.previewImgUrl,
      'state': instance.state,
      'taskId': instance.taskId,
      'updateTime': instance.updateTime,
      'userId': instance.userId,
      'workName': instance.workName,
      'uploadDetailList': instance.uploadDetailList,
      'recordDetailList': instance.recordDetailList,
    };
