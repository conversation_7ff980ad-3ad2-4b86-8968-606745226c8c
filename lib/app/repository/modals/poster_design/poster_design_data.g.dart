// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_design_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PosterDesignData _$PosterDesignDataFromJson(Map<String, dynamic> json) =>
    PosterDesignData()
      ..caseImage = json['caseImage'] as String?
      ..caseName = json['caseName'] as String?
      ..casePrompt = json['casePrompt'] as String?
      ..createTime = json['createTime'] as String?
      ..functionType = (json['functionType'] as num?)?.toInt()
      ..id = (json['id'] as num?)?.toInt()
      ..sort = (json['sort'] as num?)?.toInt()
      ..state = (json['state'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?;

Map<String, dynamic> _$PosterDesignDataToJson(PosterDesignData instance) =>
    <String, dynamic>{
      'caseImage': instance.caseImage,
      'caseName': instance.caseName,
      'casePrompt': instance.casePrompt,
      'createTime': instance.createTime,
      'functionType': instance.functionType,
      'id': instance.id,
      'sort': instance.sort,
      'state': instance.state,
      'updateTime': instance.updateTime,
    };
