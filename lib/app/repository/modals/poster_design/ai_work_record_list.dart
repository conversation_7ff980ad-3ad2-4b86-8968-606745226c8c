import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/ai_work_record_data.dart';

part 'ai_work_record_list.g.dart';

@JsonSerializable()
class AiWorkRecordList {
  List<AiWorkRecordData>? list;
  bool? hasNextPage;

  AiWorkRecordList();

  factory AiWorkRecordList.fromJson(Map<String, dynamic> json) =>
      _$AiWorkRecordListFromJson(json);

  Map<String, dynamic> toJson() => _$AiWorkRecordListToJson(this);
}
