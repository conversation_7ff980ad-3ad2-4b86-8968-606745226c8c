// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_work_record_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AiWorkRecordList _$AiWorkRecordListFromJson(Map<String, dynamic> json) =>
    AiWorkRecordList()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => AiWorkRecordData.fromJson(e as Map<String, dynamic>))
          .toList()
      ..hasNextPage = json['hasNextPage'] as bool?;

Map<String, dynamic> _$AiWorkRecordListTo<PERSON><PERSON>(AiWorkRecordList instance) =>
    <String, dynamic>{
      'list': instance.list,
      'hasNextPage': instance.hasNextPage,
    };
