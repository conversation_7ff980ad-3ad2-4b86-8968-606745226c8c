import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/record_detail_list_data.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/upload_detail_list_data.dart';

part 'ai_work_record_data.g.dart';

@JsonSerializable()
class AiWorkRecordData {
  int? categoryType;
  String? createTime;
  String? expireTime;
  int? functionType;
  int? id;
  String? previewImgUrl;
  int? state;
  String? taskId;
  String? updateTime;
  int? userId;
  String? workName;
  List<UploadDetailListData>? uploadDetailList;
  List<RecordDetailListData>? recordDetailList;

  AiWorkRecordData();

  factory AiWorkRecordData.fromJson(Map<String, dynamic> json) =>
      _$AiWorkRecordDataFromJson(json);

  Map<String, dynamic> toJson() => _$AiWorkRecordDataToJson(this);
}
