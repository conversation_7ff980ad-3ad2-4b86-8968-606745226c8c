import 'package:json_annotation/json_annotation.dart';

part 'poster_design_data.g.dart';

@JsonSerializable()
class PosterDesignData {
  String? caseImage;
  String? caseName;
  String? casePrompt;
  String? createTime;
  int? functionType;
  int? id;
  int? sort;
  int? state;
  String? updateTime;

  PosterDesignData();

  factory PosterDesignData.fromJson(Map<String, dynamic> json) =>
      _$PosterDesignDataFromJson(json);

  Map<String, dynamic> toJson() => _$PosterDesignDataToJson(this);
}
