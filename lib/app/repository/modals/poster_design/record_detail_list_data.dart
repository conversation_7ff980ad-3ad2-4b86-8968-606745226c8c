import 'package:json_annotation/json_annotation.dart';

part 'record_detail_list_data.g.dart';

@JsonSerializable()
class RecordDetailListData {
  int? id;
  String? createTime;
  String? updateTime;
  String? workContent;
  int? workRecordId;
  int? workType;

  RecordDetailListData();

  factory RecordDetailListData.fromJson(Map<String, dynamic> json) =>
      _$RecordDetailListDataFromJson(json);

  Map<String, dynamic> toJson() => _$RecordDetailListDataToJson(this);
}
