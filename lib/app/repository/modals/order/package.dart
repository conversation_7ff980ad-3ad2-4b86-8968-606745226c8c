import 'package:json_annotation/json_annotation.dart';

part 'package.g.dart';

@JsonSerializable()
class Package {
  String? appleProductId;
  String? additionName;
  int? additionType;
  num? buyPrice;
  int? durationDayNum;
  int? id;
  String? memberName;
  int? memberType;
  num? originalPrice;
  int? powerNum;

  Package();

  factory Package.fromJson(Map<String, dynamic> json) =>
      _$PackageFromJson(json);

  Map<String, dynamic> toJson() => _$PackageToJson(this);
}