// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'al_drawing_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AlDrawingData _$AlDrawingDataFromJson(Map<String, dynamic> json) =>
    AlDrawingData()
      ..caseTitle = json['caseTitle'] as String?
      ..createTime = json['createTime'] as String?
      ..id = (json['id'] as num?)?.toInt()
      ..imgUrl = json['imgUrl'] as String?
      ..prompt = json['prompt'] as String?
      ..sort = (json['sort'] as num?)?.toInt()
      ..updateTime = json['updateTime'] as String?;

Map<String, dynamic> _$AlDrawingDataToJson(AlDrawingData instance) =>
    <String, dynamic>{
      'caseTitle': instance.caseTitle,
      'createTime': instance.createTime,
      'id': instance.id,
      'imgUrl': instance.imgUrl,
      'prompt': instance.prompt,
      'sort': instance.sort,
      'updateTime': instance.updateTime,
    };
