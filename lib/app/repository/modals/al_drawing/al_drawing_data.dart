import 'package:json_annotation/json_annotation.dart';

part 'al_drawing_data.g.dart';

@JsonSerializable()
class AlDrawingData {
  String? caseTitle;
  String? createTime;
  int? id;
  String? imgUrl;
  String? prompt;
  int? sort;
  String? updateTime;

  AlDrawingData();

  factory AlDrawingData.fromJson(Map<String, dynamic> json) =>
      _$AlDrawingDataFromJson(json);

  Map<String, dynamic> toJson() => _$AlDrawingDataToJson(this);
}
