// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AudioItem _$AudioItemFromJson(Map<String, dynamic> json) => AudioItem(
      id: json['id'] as String,
      title: json['title'] as String,
      audioUrl: json['audioUrl'] as String,
      duration: json['duration'] == null
          ? null
          : Duration(microseconds: (json['duration'] as num).toInt()),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      fileSize: (json['fileSize'] as num?)?.toInt(),
      audioFormat: json['audioFormat'] as String?,
    );

Map<String, dynamic> _$AudioItemToJson(AudioItem instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'audioUrl': instance.audioUrl,
      'duration': instance.duration?.inMicroseconds,
      'createdAt': instance.createdAt?.toIso8601String(),
      'fileSize': instance.fileSize,
      'audioFormat': instance.audioFormat,
    };
