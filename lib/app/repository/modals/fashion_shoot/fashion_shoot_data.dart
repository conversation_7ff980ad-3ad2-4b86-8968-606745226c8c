import 'package:json_annotation/json_annotation.dart';

part 'fashion_shoot_data.g.dart';

@JsonSerializable()
class FashionShootData {
  String? caseName;
  int? caseType;
  String? cover;
  String? imageUrl;
  int? sort;
  String? tag;

  FashionShootData();

  factory FashionShootData.fromJson(Map<String, dynamic> json) =>
      _$FashionShootDataFromJson(json);

  Map<String, dynamic> toJson() => _$FashionShootDataToJson(this);
}
