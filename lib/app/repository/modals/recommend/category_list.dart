import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/recommend/function_item.dart';

part 'category_list.g.dart';

@JsonSerializable()
class CategoryList {
  String? categoryName;
  int? categoryType;
  String? createTime;
  List<FunctionItem?>? functionInfoList;
  int? id;

  CategoryList();

  factory CategoryList.fromJson(Map<String, dynamic> json) =>
      _$CategoryListFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryListToJson(this);
}