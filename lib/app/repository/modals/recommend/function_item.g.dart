// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'function_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunctionItem _$FunctionItemFromJson(Map<String, dynamic> json) => FunctionItem()
  ..categoryType = (json['categoryType'] as num?)?.toInt()
  ..createTime = json['createTime'] as String?
  ..functionDesc = json['functionDesc'] as String?
  ..functionImgUrl = json['functionImgUrl'] as String?
  ..functionName = json['functionName'] as String?
  ..functionTag = (json['functionTag'] as num?)?.toInt()
  ..functionType = (json['functionType'] as num?)?.toInt()
  ..homeRecommend = (json['homeRecommend'] as num?)?.toInt()
  ..iconUrl = json['iconUrl'] as String?
  ..id = (json['id'] as num?)?.toInt()
  ..sort = (json['sort'] as num?)?.toInt()
  ..updateTime = json['updateTime'] as String?;

Map<String, dynamic> _$FunctionItemToJson(FunctionItem instance) =>
    <String, dynamic>{
      'categoryType': instance.categoryType,
      'createTime': instance.createTime,
      'functionDesc': instance.functionDesc,
      'functionImgUrl': instance.functionImgUrl,
      'functionName': instance.functionName,
      'functionTag': instance.functionTag,
      'functionType': instance.functionType,
      'homeRecommend': instance.homeRecommend,
      'iconUrl': instance.iconUrl,
      'id': instance.id,
      'sort': instance.sort,
      'updateTime': instance.updateTime,
    };
