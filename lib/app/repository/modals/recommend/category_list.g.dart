// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryList _$CategoryListFromJson(Map<String, dynamic> json) => CategoryList()
  ..categoryName = json['categoryName'] as String?
  ..categoryType = (json['categoryType'] as num?)?.toInt()
  ..createTime = json['createTime'] as String?
  ..functionInfoList = (json['functionInfoList'] as List<dynamic>?)
      ?.map((e) =>
          e == null ? null : FunctionItem.fromJson(e as Map<String, dynamic>))
      .toList()
  ..id = (json['id'] as num?)?.toInt();

Map<String, dynamic> _$CategoryListToJson(CategoryList instance) =>
    <String, dynamic>{
      'categoryName': instance.categoryName,
      'categoryType': instance.categoryType,
      'createTime': instance.createTime,
      'functionInfoList': instance.functionInfoList,
      'id': instance.id,
    };
