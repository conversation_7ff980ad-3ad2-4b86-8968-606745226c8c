// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_function.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryFunction _$CategoryFunctionFromJson(Map<String, dynamic> json) =>
    CategoryFunction()
      ..categoryList = (json['categoryList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : CategoryList.fromJson(e as Map<String, dynamic>))
          .toList()
      ..homeRecommendList = (json['homeRecommendList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : FunctionItem.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$CategoryFunctionToJson(CategoryFunction instance) =>
    <String, dynamic>{
      'categoryList': instance.categoryList,
      'homeRecommendList': instance.homeRecommendList,
    };
