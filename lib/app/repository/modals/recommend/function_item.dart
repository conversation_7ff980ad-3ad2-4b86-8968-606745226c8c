import 'package:json_annotation/json_annotation.dart';

part 'function_item.g.dart';

@JsonSerializable()
class FunctionItem {
  int? categoryType;
  String? createTime;
  String? functionDesc;
  String? functionImgUrl;
  String? functionName;
  int? functionTag;
  int? functionType;
  int? homeRecommend;
  String? iconUrl;
  int? id;
  int? sort;
  String? updateTime;

  FunctionItem();

  factory FunctionItem.fromJson(Map<String, dynamic> json) =>
      _$FunctionItemFromJson(json);

  Map<String, dynamic> toJson() => _$FunctionItemToJson(this);
}