import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/recommend/category_list.dart';
import 'package:text_generation_video/app/repository/modals/recommend/function_item.dart';

part 'category_function.g.dart';

@JsonSerializable()
class CategoryFunction {
  List<CategoryList?>? categoryList;
  List<FunctionItem?>? homeRecommendList;

  CategoryFunction();

  factory CategoryFunction.fromJson(Map<String, dynamic> json) =>
      _$CategoryFunctionFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryFunctionToJson(this);
}