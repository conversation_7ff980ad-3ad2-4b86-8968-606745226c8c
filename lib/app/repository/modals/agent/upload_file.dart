import 'package:json_annotation/json_annotation.dart';

part 'upload_file.g.dart';

@JsonSerializable()
class UploadFile {
  UploadFileInfo? fileInfo;
  String? logID;

  UploadFile();

  factory UploadFile.fromJson(Map<String, dynamic> json) =>
      _$UploadFileFromJson(json);

  Map<String, dynamic> toJson() => _$UploadFileToJson(this);
}

@JsonSerializable()
class UploadFileInfo {
  int? bytes;
  // ignore: non_constant_identifier_names
  int? created_at;
  // ignore: non_constant_identifier_names
  String? file_name;
  String? id;

  UploadFileInfo();

  factory UploadFileInfo.fromJson(Map<String, dynamic> json) =>
      _$UploadFileInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UploadFileInfoToJson(this);
}