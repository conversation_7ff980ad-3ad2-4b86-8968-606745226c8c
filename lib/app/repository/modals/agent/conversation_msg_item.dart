import 'package:json_annotation/json_annotation.dart';

part 'conversation_msg_item.g.dart';

@JsonSerializable()
class ConversationMsgItem {
  String? audio;
  // ignore: non_constant_identifier_names
  String? bot_id;
  // ignore: non_constant_identifier_names
  String? chat_id;
  String? content;
  // ignore: non_constant_identifier_names
  String? content_type;
  // ignore: non_constant_identifier_names
  String? conversation_id;
  // ignore: non_constant_identifier_names
  int? created_at;
  String? id;
  // ignore: non_constant_identifier_names
  dynamic meta_data;
  // ignore: non_constant_identifier_names
  String? reasoning_content;
  String? role;
  // ignore: non_constant_identifier_names
  String? section_id;
  // ignore: non_constant_identifier_names
  int? updated_at;

  ConversationMsgItem();

  factory ConversationMsgItem.fromJson(Map<String, dynamic> json) =>
      _$ConversationMsgItemFromJson(json);

  Map<String, dynamic> toJson() => _$ConversationMsgItemToJson(this);
}