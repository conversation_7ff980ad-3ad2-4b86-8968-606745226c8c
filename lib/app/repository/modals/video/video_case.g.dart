// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_case.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VideoCase _$VideoCaseFromJson(Map<String, dynamic> json) => VideoCase()
  ..caseImg1 = json['caseImg1'] as String?
  ..caseImg2 = json['caseImg2'] as String?
  ..caseImg3 = json['caseImg3'] as String?
  ..caseImg4 = json['caseImg4'] as String?
  ..caseName = json['caseName'] as String?
  ..casePrompt = json['casePrompt'] as String?
  ..caseType = (json['caseType'] as num?)?.toInt()
  ..caseVideoUrl = json['caseVideoUrl'] as String?
  ..cover = json['cover'] as String?
  ..duration = (json['duration'] as num?)?.toInt()
  ..fps = (json['fps'] as num?)?.toInt()
  ..id = (json['id'] as num?)?.toInt()
  ..ratio = json['ratio'] as String?
  ..resolution = json['resolution'] as String?;

Map<String, dynamic> _$VideoCaseToJson(VideoCase instance) => <String, dynamic>{
      'caseImg1': instance.caseImg1,
      'caseImg2': instance.caseImg2,
      'caseImg3': instance.caseImg3,
      'caseImg4': instance.caseImg4,
      'caseName': instance.caseName,
      'casePrompt': instance.casePrompt,
      'caseType': instance.caseType,
      'caseVideoUrl': instance.caseVideoUrl,
      'cover': instance.cover,
      'duration': instance.duration,
      'fps': instance.fps,
      'id': instance.id,
      'ratio': instance.ratio,
      'resolution': instance.resolution,
    };
