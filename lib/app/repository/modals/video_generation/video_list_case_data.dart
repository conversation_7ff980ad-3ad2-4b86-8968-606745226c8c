import 'package:json_annotation/json_annotation.dart';

part 'video_list_case_data.g.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.repository.modals.voice
/// @ClassName: creation_result_list
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/10 14:25
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/10 14:25
/// @UpdateRemark: 更新说明
@JsonSerializable()
class VideoListCaseData {
  String? caseName;
  String? cover;
  String? templateId;
  String? videoUrl;

  VideoListCaseData();

  factory VideoListCaseData.fromJson(Map<String, dynamic> json) =>
      _$VideoListCaseDataFromJson(json);

  Map<String, dynamic> toJson() => _$VideoListCaseDataToJson(this);
}
