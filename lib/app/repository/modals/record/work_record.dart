import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/record/record_detail.dart';
import 'package:text_generation_video/app/repository/modals/record/upload_detail.dart';

part 'work_record.g.dart';

@JsonSerializable()
class WorkRecord {
  int? categoryType;
  String? createTime;
  String? expireTime;
  int? functionType;
  int? id;
  String? previewImgUrl;
  List<RecordDetail>? recordDetailList;
  int? state;
  String? taskId;
  String? updateTime;
  List<UploadDetail>? uploadDetailList;
  int? userId;
  String? workName;

  WorkRecord();

  factory WorkRecord.fromJson(Map<String, dynamic> json) =>
      _$WorkRecordFromJson(json);

  Map<String, dynamic> toJson() => _$WorkRecordToJson(this);
}