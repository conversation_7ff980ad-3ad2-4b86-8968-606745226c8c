// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_record_list.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkRecordList _$WorkRecordListFromJson(Map<String, dynamic> json) =>
    WorkRecordList()
      ..hasNextPage = json['hasNextPage'] as bool?
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => WorkRecord.fromJson(e as Map<String, dynamic>))
          .toList()
      ..total = (json['total'] as num?)?.toInt();

Map<String, dynamic> _$WorkRecordListToJson(WorkRecordList instance) =>
    <String, dynamic>{
      'hasNextPage': instance.hasNextPage,
      'list': instance.list,
      'total': instance.total,
    };
