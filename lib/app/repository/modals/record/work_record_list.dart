import 'package:json_annotation/json_annotation.dart';
import 'package:text_generation_video/app/repository/modals/record/work_record.dart';

part 'work_record_list.g.dart';

@JsonSerializable()
class WorkRecordList {
  bool? hasNextPage;
  List<WorkRecord>? list;
  int? total;

  WorkRecordList();

  factory WorkRecordList.fromJson(Map<String, dynamic> json) =>
      _$WorkRecordListFromJson(json);

  Map<String, dynamic> toJson() => _$WorkRecordListToJson(this);
}