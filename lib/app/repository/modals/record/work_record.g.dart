// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'work_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkRecord _$WorkRecordFromJson(Map<String, dynamic> json) => WorkRecord()
  ..categoryType = (json['categoryType'] as num?)?.toInt()
  ..createTime = json['createTime'] as String?
  ..expireTime = json['expireTime'] as String?
  ..functionType = (json['functionType'] as num?)?.toInt()
  ..id = (json['id'] as num?)?.toInt()
  ..previewImgUrl = json['previewImgUrl'] as String?
  ..recordDetailList = (json['recordDetailList'] as List<dynamic>?)
      ?.map((e) => RecordDetail.fromJson(e as Map<String, dynamic>))
      .toList()
  ..state = (json['state'] as num?)?.toInt()
  ..taskId = json['taskId'] as String?
  ..updateTime = json['updateTime'] as String?
  ..uploadDetailList = (json['uploadDetailList'] as List<dynamic>?)
      ?.map((e) => UploadDetail.fromJson(e as Map<String, dynamic>))
      .toList()
  ..userId = (json['userId'] as num?)?.toInt()
  ..workName = json['workName'] as String?;

Map<String, dynamic> _$WorkRecordToJson(WorkRecord instance) =>
    <String, dynamic>{
      'categoryType': instance.categoryType,
      'createTime': instance.createTime,
      'expireTime': instance.expireTime,
      'functionType': instance.functionType,
      'id': instance.id,
      'previewImgUrl': instance.previewImgUrl,
      'recordDetailList': instance.recordDetailList,
      'state': instance.state,
      'taskId': instance.taskId,
      'updateTime': instance.updateTime,
      'uploadDetailList': instance.uploadDetailList,
      'userId': instance.userId,
      'workName': instance.workName,
    };
