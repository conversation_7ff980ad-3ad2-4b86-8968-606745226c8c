import 'package:json_annotation/json_annotation.dart';

part 'record_detail.g.dart';

@JsonSerializable()
class RecordDetail {
  String? createTime;
  int? id;
  String? updateTime;
  String? workContent;
  int? workRecordId;
  int? workType;

  RecordDetail();

  factory RecordDetail.fromJson(Map<String, dynamic> json) =>
      _$RecordDetailFromJson(json);

  Map<String, dynamic> toJson() => _$RecordDetailToJson(this);
}