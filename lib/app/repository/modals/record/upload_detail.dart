import 'package:json_annotation/json_annotation.dart';

part 'upload_detail.g.dart';

@JsonSerializable()
class UploadDetail {
  String? createTime;
  int? id;
  String? updateTime;
  String? uploadContent;
  int? uploadType;
  int? workRecordId;

  UploadDetail();

  factory UploadDetail.fromJson(Map<String, dynamic> json) =>
      _$UploadDetailFromJson(json);

  Map<String, dynamic> toJson() => _$UploadDetailToJson(this);
}