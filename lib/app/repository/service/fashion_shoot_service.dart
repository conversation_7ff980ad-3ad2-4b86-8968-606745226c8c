import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/api.dart';
import 'package:text_generation_video/app/repository/modals/fashion_shoot/fashion_shoot_data.dart';

class FashionShootService {
  static Future<ApiResponse> clothesListCase(
    String caseType,
    String tag,
  ) async {
    try {
      var response = await HttpUtils.get(Api.clothesListCase, params: {
        "tag": tag,
        "caseType": caseType,
      });

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];

          return List.generate(
            list.length,
            (index) => FashionShootData.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取标签列表
  static Future<ApiResponse> clothesListCaseTag(
    String caseType,
  ) async {
    try {
      var response = await HttpUtils.get(
        Api.clothesListCaseTag,
        params: {
          "caseType": caseType,
        },
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];

          return List.generate(
            list.length,
            (index) => list[index],
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 换装生成图片
  static Future<ApiResponse> imageSubmit(data) async {
    try {
      var response = await HttpUtils.post(
        Api.imageSubmit,
        data: data,
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
