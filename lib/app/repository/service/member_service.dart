import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/member/member_banner.dart';
import 'package:text_generation_video/app/repository/modals/member/member_benefits.dart';
import 'package:text_generation_video/app/repository/modals/order/order.dart';
import 'package:text_generation_video/app/repository/modals/order/package.dart';
import 'package:text_generation_video/app/repository/modals/order/package_list.dart';

import '../api.dart';

class MemberService {
  /// 获取所有会员包
  static Future<ApiResponse<List<Package>>> getAllPackage() async {
    try {
      var response = await HttpUtils.get(Api.getAllPackage);

      BaseResponse<PackageList> result = BaseResponse.fromJson(
        response,
        (json) => PackageList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取所有算力充值包
  static Future<ApiResponse<List<Package>>> getAllPowerPackage() async {
    try {
      var response = await HttpUtils.get(Api.getAllPowerPackage);

      BaseResponse<PackageList> result = BaseResponse.fromJson(
        response,
        (json) => PackageList.fromJson(response),
      );

      return ApiResponse.completed(result.data?.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 创建订单
  static Future<ApiResponse<Order>> buyPackage(
    int orderType,
    int payType,
    int? subType,
  ) async {
    try {
      var data = {
        "orderType": orderType,
        "payType": payType,
        "subType": subType,
      };

      var response = await HttpUtils.post(Api.buyPackage, data: data);

      BaseResponse<Order> result = BaseResponse.fromJson(
        response,
        (json) => Order.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 验证
  static Future<ApiResponse<Order>> applePayVerify(
    String environment,
    String? orderNo,
    String paymentData,
  ) async {
    try {
      var data = {
        "environment": environment,
        "orderNo": orderNo,
        "paymentData": paymentData,
      };

      var response = await HttpUtils.post(Api.applePayVerify, data: data);

      BaseResponse<Order> result = BaseResponse.fromJson(
        response,
        (json) => Order.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 算力充值验证
  static Future<ApiResponse<Order>> applePayPowerVerify(
    String environment,
    String? orderNo,
    String paymentData,
  ) async {
    try {
      var data = {
        "environment": environment,
        "orderNo": orderNo,
        "paymentData": paymentData,
      };

      var response = await HttpUtils.post(Api.applePayPowerVerify, data: data);

      BaseResponse<Order> result = BaseResponse.fromJson(
        response,
        (json) => Order.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取会员页Banner
  static Future<ApiResponse<List<MemberBanner>>> memberBannerList() async {
    try {
      var response = await HttpUtils.get(Api.memberBannerList);

      BaseResponse<List<MemberBanner>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => MemberBanner.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取会员页权益显示列表
  static Future<ApiResponse<List<MemberBenefits>>> memberBenefitsList() async {
    try {
      var response = await HttpUtils.get(Api.memberBenefitsList);

      BaseResponse<List<MemberBenefits>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => MemberBenefits.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
