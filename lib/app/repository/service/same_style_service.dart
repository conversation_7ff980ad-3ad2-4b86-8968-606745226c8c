import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/recommend/category_function.dart';
import 'package:text_generation_video/app/repository/modals/recommend/home_banner.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_category.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_example_list.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_style_banner.dart';

import '../api.dart';

class SameStyleService {
  /// 同款类型列表
  static Future<ApiResponse<List<SameCategory>>> sameCategoryList() async {
    try {
      var response = await HttpUtils.get(Api.sameCategoryList);

      BaseResponse<List<SameCategory>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => SameCategory.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 同款事例列表
  static Future<ApiResponse<SameExampleList>> sameExampleList(
    int categoryType,
    int pageNum,
    int pageSize,
  ) async {
    try {
      var data = {
        "categoryType": categoryType,
        "pageNum": pageNum,
        "pageSize": pageSize,
      };

      var response = await HttpUtils.get(Api.sameExampleList, params: data);

      BaseResponse<SameExampleList> result = BaseResponse.fromJson(
        response,
        (json) => SameExampleList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 同款事例banner列表
  static Future<ApiResponse<List<SameStyleBanner>?>> sameExampleBanner() async {
    try {
      var response = await HttpUtils.get(Api.sameExampleBanner);

      BaseResponse<List<SameStyleBanner>?> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => SameStyleBanner.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取所有功能分类
  static Future<ApiResponse<CategoryFunction?>> homeFunctionCategory() async {
    try {
      var response = await HttpUtils.get(Api.homeFunctionCategory);

      BaseResponse<CategoryFunction> result = BaseResponse.fromJson(
        response,
        (json) => CategoryFunction.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取首页banner
  static Future<ApiResponse<List<HomeBanner>?>> homeBanner() async {
    try {
      var response = await HttpUtils.get(Api.homeBanner);

      BaseResponse<List<HomeBanner>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => HomeBanner.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
