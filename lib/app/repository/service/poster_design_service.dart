import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/api.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/ai_work_record_list.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/poster_design_list.dart';

class PosterDesignService {
  static Future<ApiResponse<PosterDesignList>> posterCaseList(
    int functionType,
  ) async {
    try {
      var response = await HttpUtils.get(Api.posterCaseList, params: {
        "functionType": functionType,
        "pageNum": 1,
        "pageSize": 100,
      });

      BaseResponse<PosterDesignList> result = BaseResponse.fromJson(
        response,
        (json) {
          return PosterDesignList.fromJson(json);
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI工作记录列表
  static Future<ApiResponse<AiWorkRecordList>> aiWorkRecordList(
    int functionType,
    pageNum,
    pageSize, {
    int? categoryType,
  }) async {
    var params = {
      "functionType": functionType,
      "pageNum": pageNum,
      "pageSize": pageSize,
    };

    if (categoryType != null) {
      params['categoryType'] = categoryType;
    }

    try {
      var response = await HttpUtils.get(Api.aiWorkRecordList, params: params);

      BaseResponse<AiWorkRecordList> result = BaseResponse.fromJson(
        response,
        (json) {
          return AiWorkRecordList.fromJson(json);
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 海报设计生成
  static Future<ApiResponse> posterDesign(data) async {
    try {
      var response = await HttpUtils.post(
        Api.posterDesign,
        data: data,
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 商品图包装生成
  static Future<ApiResponse> imgPackaging(data) async {
    try {
      var response = await HttpUtils.post(
        Api.imgPackaging,
        data: data,
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 根据id集合批量删除作品
  static Future<ApiResponse> deleteRecord(
    List<int> idArray,
  ) async {
    try {
      var response = await HttpUtils.post(
        Api.deleteRecord,
        data: idArray,
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
