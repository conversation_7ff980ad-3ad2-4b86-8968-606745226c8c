import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/api.dart';
import 'package:text_generation_video/app/repository/modals/video_generation/video_list_case_data.dart';

class PictureDancingService {
  /// 跳舞案例标签列表
  static Future<ApiResponse> videoListCase() async {
    try {
      var response = await HttpUtils.get(Api.listCaseTag);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];

          return List.generate(
            list.length,
            (index) => list[index],
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  static Future<ApiResponse> listCase(String tag) async {
    try {
      var response = await HttpUtils.get(Api.listCase, params: {"tag": tag});

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];

          return List.generate(
            list.length,
            (index) => VideoListCaseData.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
