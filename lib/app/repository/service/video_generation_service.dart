import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/api.dart';
import 'package:text_generation_video/app/repository/modals/video_generation/video_list_case_data.dart';

class VideoGenerationService {
  // Add your methods and properties here
  /// 上传图片
  static Future<ApiResponse> uploadImage(
    Uint8List filePath,
    String name,
  ) async {
    try {
      var partFile = MultipartFile.fromBytes(
        filePath,
        filename: name,
      );

      FormData data = FormData.fromMap({
        "file": partFile,
      });

      var response = await HttpUtils.post(Api.uploadImage, data: data);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 图片转视频
  static Future<ApiResponse> imgToVideoByFirstFrame(
    String img1,
    String text,
  ) async {
    try {
      var data = {
        "img1": img1,
        "text": text,
      };

      var response =
          await HttpUtils.post(Api.imgToVideoByFirstFrame, data: data);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 图片转视频（两张图）
  static Future<ApiResponse> imgToVideoByFirstAndLastFrame(
    String img1,
    String img2,
    String text,
  ) async {
    try {
      var data = {
        "img1": img1,
        "img2": img2,
        "text": text,
      };

      var response =
          await HttpUtils.post(Api.imgToVideoByFirstAndLastFrame, data: data);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 图片转视频（四张图）
  static Future<ApiResponse> referenceImageToVideo(
    String img1,
    String img2,
    String img3,
    String img4,
    String text,
  ) async {
    try {
      var data = {
        "img1": img1,
        "img2": img2,
        "img3": img3,
        "img4": img4,
        "text": text,
      };

      var response = await HttpUtils.post(Api.imgToVideoByFirstAndLastFrame, data: data);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 视频特效生成
  static Future<ApiResponse> videoSpecialEffects(
    List imageList,
    String templateId,
  ) async {
    try {
      var data = {
        "imageList": imageList,
        "templateId": templateId,
      };

      var response = await HttpUtils.post(
        Api.videoSpecialEffects,
        data: data,
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 视频跳舞特效生成
  static Future<ApiResponse> videoDancingEffects(
    String imageUrl,
    String videoUrl,
  ) async {
    try {
      var data = {
        "imageUrl": imageUrl,
        "videoUrl": videoUrl,
      };

      var response = await HttpUtils.post(
        Api.videoDancingEffects,
        data: data,
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 特效案例列表
  static Future<ApiResponse> videoListCase() async {
    try {
      var response = await HttpUtils.get(Api.videoListCase);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];

          return List.generate(
            list.length,
            (index) => VideoListCaseData.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
