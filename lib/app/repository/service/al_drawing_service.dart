import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/api.dart';
import 'package:text_generation_video/app/repository/modals/al_drawing/al_drawing_data.dart';

class AlDrawingService {
  /// AI绘图案例列表
  static Future<ApiResponse> aiCaseList(
    int functionType,
  ) async {
    try {
      var response = await HttpUtils.get(Api.aiCaseList);

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];

          return List.generate(
            list.length,
            (index) => AlDrawingData.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI绘图s生成
  static Future<ApiResponse> textToImage(data) async {
    try {
      var response = await HttpUtils.post(
        Api.textToImage,
        data: data,
      );

      BaseResponse result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
