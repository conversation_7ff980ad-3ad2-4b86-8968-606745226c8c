import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_modification.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_muscle.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_repair.dart';
import 'package:text_generation_video/app/repository/modals/video/video_case.dart';

import '../api.dart';

class TextToVideoService {
  /// 开始生成视频
  static Future<ApiResponse<bool>> textToVideoPage({
    String? img1,
    String? img2,
    String? text,
  }) async {
    try {
      var data = {};
      if (img1 != null && img1.isNotEmpty) {
        data["img1"] = img1;
      }

      if (img2 != null && img2.isNotEmpty) {
        data["img2"] = img2;
      }

      if (text != null && text.isNotEmpty) {
        data["text"] = text;
      }

      var response = await HttpUtils.post(Api.textToVideo, data: data);

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 获取热门案例列表
  static Future<ApiResponse<List<VideoCase>>> listVideoCase(
      int caseType) async {
    try {
      var data = {"caseType": caseType};

      var response = await HttpUtils.get(Api.listVideoCase, params: data);

      BaseResponse<List<VideoCase>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => VideoCase.fromJson(list[index]),
          ).toList();
        },
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 老照片修改
  static Future<ApiResponse<PhotoRepair>> oldPhotoRepair(
    String imgUrl,
  ) async {
    try {
      var data = {"imgUrl": imgUrl};

      var response = await HttpUtils.post(Api.oldPhotoRepair, data: data);

      BaseResponse<PhotoRepair> result = BaseResponse.fromJson(
        response,
        (json) => PhotoRepair.fromJson(json),
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 图片上传
  static Future<ApiResponse<String?>> repairUploadFile(String filePath) async {
    try {
      var data = FormData.fromMap({
        "file": await MultipartFile.fromFile(filePath),
      });

      var response = await HttpUtils.post(Api.imgUpload, data: data);

      BaseResponse<String> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 照片画质修复
  static Future<ApiResponse<PhotoRepair>> qualityPhotoRepair(
    String imgUrl,
  ) async {
    try {
      var data = {"imgUrl": imgUrl};

      var response = await HttpUtils.post(Api.qualityPhotoRepair, data: data);

      BaseResponse<PhotoRepair> result = BaseResponse.fromJson(
        response,
        (json) => PhotoRepair.fromJson(json),
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI抠图
  static Future<ApiResponse<bool>> saliencySegSubmitTask(
    String imgUrl,
  ) async {
    try {
      var data = {"imgUrl": imgUrl};

      var response = await HttpUtils.post(
        Api.saliencySegSubmitTask,
        data: data,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI改图
  static Future<ApiResponse<bool>> imageToImage(
    String prompt,
    String imgUrl,
  ) async {
    try {
      var data = {"imgUrl": imgUrl, "prompt": prompt};

      var response = await HttpUtils.post(
        Api.imageToImage,
        data: data,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );
      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI改图案例列表
  static Future<ApiResponse<List<PhotoModification>>>
      modificationCaseList() async {
    try {
      var response = await HttpUtils.get(Api.modificationCaseList);

      BaseResponse<List<PhotoModification>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => PhotoModification.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI写真分类列表
  static Future<ApiResponse<List<PhotoPortraitCategory>>>photoPortraitCategory() async {
    try {
      var response = await HttpUtils.get(Api.photoPortraitCategory);

      BaseResponse<List<PhotoPortraitCategory>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => PhotoPortraitCategory.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI写真分类banner列表
  static Future<ApiResponse<List<PhotoPortraitBanner>>>
      photoPortraitBanner() async {
    try {
      var response = await HttpUtils.get(Api.photoPortraitBanner);

      BaseResponse<List<PhotoPortraitBanner>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => PhotoPortraitBanner.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// AI写真分类详情  
  static Future<ApiResponse<PhotoPortraitCategoryDetail>>
      photoPortraitDetail(int caseDetailId) async {
    try {
      var data = {"caseDetailId": caseDetailId};
      var response = await HttpUtils.get(Api.photoPortraitDetail, params: data);

      BaseResponse<PhotoPortraitCategoryDetail> result = BaseResponse.fromJson(
        response,
        (json) => PhotoPortraitCategoryDetail.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
  
  /// AI照片增肌案例列表  
  static Future<ApiResponse<List<PhotoMuscleCaseItem>>> photoMuscleCaseList(int gender) async {
    try {
      var data = {"gender": gender};
      var response = await HttpUtils.get(Api.photoMuscleCaseList, params: data);

      BaseResponse<List<PhotoMuscleCaseItem>> result = BaseResponse.fromJson(
        response,
        (json) {
          var list = (json as List?) ?? [];
          return List.generate(
            list.length,
            (index) => PhotoMuscleCaseItem.fromJson(list[index]),
          ).toList();
        },
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
