import 'package:dio/dio.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/modals/record/work_record_list.dart';

import '../api.dart';

class RecordService {
  /// 作品记录列表
  static Future<ApiResponse<WorkRecordList>> workRecordList(
    int pageNum,
    int pageSize, {
    int? categoryType,
    int? functionType,
  }) async {
    var data = {
      "pageNum": pageNum,
      "pageSize": pageSize,
    };

    if (categoryType != null) {
      data["categoryType"] = categoryType;
    }

    if (functionType != null) {
      data["functionType"] = functionType;
    }

    try {
      var response = await HttpUtils.get(Api.workRecordList, params: data);

      BaseResponse<WorkRecordList> result = BaseResponse.fromJson(
        response,
        (json) => WorkRecordList.fromJson(json),
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }

  /// 根据id列表删除作品
  static Future<ApiResponse<bool>> deleteWorkRecordList(List<int?> ids) async {

    try {
      var response = await HttpUtils.post(
        Api.deleteWorkRecordByIds,
        data: ids,
      );

      BaseResponse<bool> result = BaseResponse.fromJson(
        response,
        (json) => json,
      );

      return ApiResponse.completed(result.data);
    } on DioException catch (e) {
      return ApiResponse.error(e.error as AppException?);
    }
  }
}
