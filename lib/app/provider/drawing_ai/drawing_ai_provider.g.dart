// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'drawing_ai_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$drawingAiHash() => r'467f5b42aab40b80c58ff3773839710eec8ecfe8';

/// See also [DrawingAi].
@ProviderFor(DrawingAi)
final drawingAiProvider =
    AutoDisposeNotifierProvider<DrawingAi, AidrawingDatas>.internal(
  DrawingAi.new,
  name: r'drawingAiProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$drawingAiHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DrawingAi = AutoDisposeNotifier<AidrawingDatas>;
String _$drawingAiRecordListHash() =>
    r'defe0ffea2ed5844487b83dbe4dd04d25d4675c9';

/// See also [DrawingAiRecordList].
@ProviderFor(DrawingAiRecordList)
final drawingAiRecordListProvider = AutoDisposeNotifierProvider<
    DrawingAiRecordList, AiDrawingRecordData?>.internal(
  DrawingAiRecordList.new,
  name: r'drawingAiRecordListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$drawingAiRecordListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DrawingAiRecordList = AutoDisposeNotifier<AiDrawingRecordData?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
