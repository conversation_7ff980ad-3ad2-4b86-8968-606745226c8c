import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/recommend/category_function.dart';
import 'package:text_generation_video/app/repository/modals/recommend/home_banner.dart';
import 'package:text_generation_video/app/repository/service/same_style_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

part 'home_recommend_provider.g.dart';

// 首页banner列表
@riverpod
Future<List<HomeBanner>?> fetchHomeBannerList(Ref ref) async {
  var result = await SameStyleService.homeBanner();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

// 当前首页推荐列表
@riverpod
class HomeRecommendFunction extends _$HomeRecommendFunction {
  @override
  CategoryFunction? build() {
    loadCategory();
    return null;
  }

  void loadCategory() async {
    var result = await SameStyleService.homeFunctionCategory();
    if (result.status == Status.completed) {
      state = result.data ?? CategoryFunction();
    } else {
      state = CategoryFunction();
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}
