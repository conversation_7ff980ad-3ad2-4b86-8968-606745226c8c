// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_recommend_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchHomeBannerListHash() =>
    r'480d4230579287efe5ea685042482e991be873ea';

/// See also [fetchHomeBannerList].
@ProviderFor(fetchHomeBannerList)
final fetchHomeBannerListProvider =
    AutoDisposeFutureProvider<List<HomeBanner>?>.internal(
  fetchHomeBannerList,
  name: r'fetchHomeBannerListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchHomeBannerListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchHomeBannerListRef
    = AutoDisposeFutureProviderRef<List<HomeBanner>?>;
String _$homeRecommendFunctionHash() =>
    r'65b182256be59497e9a29dd5ccb1765366c4eeaa';

/// See also [HomeRecommendFunction].
@ProviderFor(HomeRecommendFunction)
final homeRecommendFunctionProvider = AutoDisposeNotifierProvider<
    HomeRecommendFunction, CategoryFunction?>.internal(
  HomeRecommendFunction.new,
  name: r'homeRecommendFunctionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$homeRecommendFunctionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$HomeRecommendFunction = AutoDisposeNotifier<CategoryFunction?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
