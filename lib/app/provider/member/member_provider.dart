import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/member/member_banner.dart';
import 'package:text_generation_video/app/repository/modals/member/member_benefits.dart';
import 'package:text_generation_video/app/repository/modals/power/power_item_type.dart';
import 'package:text_generation_video/app/repository/service/account_service.dart';
import 'package:text_generation_video/app/repository/service/member_service.dart';

import '../../../utils/toast_util.dart';
import '../../repository/modals/account/member.dart';
import '../../repository/service/power_service.dart';
import '../../view/creation/dialog/confirm_commit_dialog.dart';
import '../../view/creation/dialog/member_check_dialog.dart';
import '../account/auth_provider.dart';

part 'member_provider.g.dart';

@riverpod
class MemberInfo extends _$MemberInfo {
  @override
  Member? build() {
    getMember();
    return null;
  }

  void getMember() async {
    var userData = ref.watch(authProvider);
    if (userData != null) {
      var memberResult = await AccountService.getMemberInfo();
      if (memberResult.status == Status.completed) {
        state = memberResult.data;
      }
    }
  }
}

// 获取会员页Banner
@riverpod
Future<List<MemberBanner>?> fetchMemberBannerList(Ref ref) async {
  var result = await MemberService.memberBannerList();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

// 获取会员页权益显示列表
@riverpod
Future<List<MemberBenefits>?> fetchMemberBenefitsList(Ref ref) async {
  var result = await MemberService.memberBenefitsList();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

// 根据type获取消耗算力值
@riverpod
Future<PowerItemType?> fetchConsumerPower(
  Ref ref,
  int? type,
) async {
  var result = await PowerService.getPowerItemType(type ?? 1);
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

// 会员检查
@riverpod
class CheckMember extends _$CheckMember {
  @override
  void build() {
    return;
  }

  // 检查会员状态和算力余额
  // 参数type对应不同功能
  // type: 1 智能体对话
  Future<bool> checkMemberAndPower({int? type, int? consumerPowerItem}) async {
    if (type == null && consumerPowerItem == null) {
      debugPrint("会员检查时type和consumerPowerItem必须传一个");
      return false;
    }
    SmartDialog.showLoading(msg: "加载中...");
    // 判断是否是会员
    var memberResult = await AccountService.getMemberInfo();
    if (memberResult.status == Status.error) {
      SmartDialog.dismiss();
      ToastUtil.showToast(memberResult.exception!.getMessage());
      return false;
    }
    if (memberResult.data == null ||
        (memberResult.data != null && memberResult.data?.hasExpire == true)) {
      SmartDialog.dismiss();
      // 非会员或者会员过期
      bool state = memberResult.data == null;
      MemberCheckDialog.showCheckDialog(state);
      return false;
    }

    // 判断算力是否充足
    // 消耗值
    int? powerItem;
    if (consumerPowerItem != null) {
      powerItem = consumerPowerItem;
    } else {
      var powerItemTypeResult = await PowerService.getPowerItemType(type ?? 1);
      powerItem = powerItemTypeResult.data?.powerNum;
    }
    // 剩余值
    var accountPowerResult = await PowerService.getAccountPower();
    var powerBalance = accountPowerResult.data?.powerBalance;
    debugPrint("power-item: $powerItem");
    debugPrint("power-balance: $powerBalance");
    if (powerItem == null || powerBalance == null) {
      SmartDialog.dismiss();
      ToastUtil.showToast("灵感值获取失败");
      return false;
    }
    // 算力值不足
    if (powerBalance < powerItem) {
      SmartDialog.dismiss();
      ConfirmCommitDialog.powerLessCommit(powerItem);
      return false;
    }
    SmartDialog.dismiss();
    return true;
  }
}
