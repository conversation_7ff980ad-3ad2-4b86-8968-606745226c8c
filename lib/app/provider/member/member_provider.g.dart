// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'member_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchMemberBannerListHash() =>
    r'9db8c9c8ad3abe6937d1edc7c88d313120124134';

/// See also [fetchMemberBannerList].
@ProviderFor(fetchMemberBannerList)
final fetchMemberBannerListProvider =
    AutoDisposeFutureProvider<List<MemberBanner>?>.internal(
  fetchMemberBannerList,
  name: r'fetchMemberBannerListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMemberBannerListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchMemberBannerListRef
    = AutoDisposeFutureProviderRef<List<MemberBanner>?>;
String _$fetchMemberBenefitsListHash() =>
    r'f0a5a5644370cbe2f1de3874ade277749ebad284';

/// See also [fetchMemberBenefitsList].
@ProviderFor(fetchMemberBenefitsList)
final fetchMemberBenefitsListProvider =
    AutoDisposeFutureProvider<List<MemberBenefits>?>.internal(
  fetchMemberBenefitsList,
  name: r'fetchMemberBenefitsListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchMemberBenefitsListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchMemberBenefitsListRef
    = AutoDisposeFutureProviderRef<List<MemberBenefits>?>;
String _$fetchConsumerPowerHash() =>
    r'5db77fbde2f9433b0bd3e70bb6c34ad83c32fa3d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchConsumerPower].
@ProviderFor(fetchConsumerPower)
const fetchConsumerPowerProvider = FetchConsumerPowerFamily();

/// See also [fetchConsumerPower].
class FetchConsumerPowerFamily extends Family<AsyncValue<PowerItemType?>> {
  /// See also [fetchConsumerPower].
  const FetchConsumerPowerFamily();

  /// See also [fetchConsumerPower].
  FetchConsumerPowerProvider call(
    int? type,
  ) {
    return FetchConsumerPowerProvider(
      type,
    );
  }

  @override
  FetchConsumerPowerProvider getProviderOverride(
    covariant FetchConsumerPowerProvider provider,
  ) {
    return call(
      provider.type,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchConsumerPowerProvider';
}

/// See also [fetchConsumerPower].
class FetchConsumerPowerProvider
    extends AutoDisposeFutureProvider<PowerItemType?> {
  /// See also [fetchConsumerPower].
  FetchConsumerPowerProvider(
    int? type,
  ) : this._internal(
          (ref) => fetchConsumerPower(
            ref as FetchConsumerPowerRef,
            type,
          ),
          from: fetchConsumerPowerProvider,
          name: r'fetchConsumerPowerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchConsumerPowerHash,
          dependencies: FetchConsumerPowerFamily._dependencies,
          allTransitiveDependencies:
              FetchConsumerPowerFamily._allTransitiveDependencies,
          type: type,
        );

  FetchConsumerPowerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.type,
  }) : super.internal();

  final int? type;

  @override
  Override overrideWith(
    FutureOr<PowerItemType?> Function(FetchConsumerPowerRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchConsumerPowerProvider._internal(
        (ref) => create(ref as FetchConsumerPowerRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        type: type,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<PowerItemType?> createElement() {
    return _FetchConsumerPowerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchConsumerPowerProvider && other.type == type;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchConsumerPowerRef on AutoDisposeFutureProviderRef<PowerItemType?> {
  /// The parameter `type` of this provider.
  int? get type;
}

class _FetchConsumerPowerProviderElement
    extends AutoDisposeFutureProviderElement<PowerItemType?>
    with FetchConsumerPowerRef {
  _FetchConsumerPowerProviderElement(super.provider);

  @override
  int? get type => (origin as FetchConsumerPowerProvider).type;
}

String _$memberInfoHash() => r'c646a8559265aa56f5341f63fbe7e3050fca2b18';

/// See also [MemberInfo].
@ProviderFor(MemberInfo)
final memberInfoProvider =
    AutoDisposeNotifierProvider<MemberInfo, Member?>.internal(
  MemberInfo.new,
  name: r'memberInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$memberInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MemberInfo = AutoDisposeNotifier<Member?>;
String _$checkMemberHash() => r'aafb7f5f57a78079f38418339044e1330bdbecb5';

/// See also [CheckMember].
@ProviderFor(CheckMember)
final checkMemberProvider =
    AutoDisposeNotifierProvider<CheckMember, void>.internal(
  CheckMember.new,
  name: r'checkMemberProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$checkMemberHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CheckMember = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
