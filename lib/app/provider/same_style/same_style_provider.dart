import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_category.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_example.dart';
import 'package:text_generation_video/app/repository/service/same_style_service.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../navigation/router.dart';
import '../../repository/modals/same_style/same_style_banner.dart';
import '../agent/conversation_provider.dart';

part 'same_style_provider.g.dart';

// 同款示例banner列表
@riverpod
Future<List<SameStyleBanner>?> fetchSameStyleBannerList(Ref ref) async {
  var result = await SameStyleService.sameExampleBanner();
  if (result.status == Status.completed) {
    return result.data;
  }
  return null;
}

// 同款类型列表
@riverpod
class SameStyleCategoryList extends _$SameStyleCategoryList {
  @override
  List<SameCategory>? build() {
    getCategoryList();
    return null;
  }

  void getCategoryList() async {
    var result = await SameStyleService.sameCategoryList();
    if (result.status == Status.completed) {
      state = result.data;
      if (result.data != null && result.data!.isNotEmpty) {
        ref
            .read(currentSameStyleCategoryProvider.notifier)
            .setCategory(result.data?.first);
      }
    }
  }
}

/// 当前选中的同款分类类型
@riverpod
class CurrentSameStyleCategory extends _$CurrentSameStyleCategory {
  @override
  SameCategory? build() {
    return null;
  }

  void setCategory(SameCategory? sameCategory) async {
    state = sameCategory;
    ref.read(sameStyleExampleListProvider.notifier).loadData();
  }
}

/// 同款示例列表
/// 页长
const int pageSize = 10;

/// 同款示例列表结果
class SameStyleListResult {
  final int pageNo;
  final List<SameExample?>? exampleList;
  final LoadState? loadState;

  const SameStyleListResult({
    this.pageNo = 1,
    this.exampleList,
    this.loadState,
  });

  SameStyleListResult copyWith({
    int? page,
    List<SameExample?>? exampleList,
    LoadState? loadState,
  }) {
    return SameStyleListResult(
      pageNo: page ?? pageNo,
      exampleList: exampleList ?? this.exampleList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class SameStyleExampleList extends _$SameStyleExampleList {
  @override
  SameStyleListResult build() {
    state = const SameStyleListResult();
    return state;
  }

  /// 加载数据
  Future<void> loadData() async {
    var categoryType = ref.read(currentSameStyleCategoryProvider);
    if (categoryType == null || categoryType.categoryType == null) return;
    state = state.copyWith(
      page: 1,
      loadState: null,
    );
    var result = await SameStyleService.sameExampleList(
      categoryType.categoryType!,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      state = state.copyWith(
        exampleList: result.data?.list ?? [],
        loadState: result.data?.hasNextPage == true
            ? LoadState.idle
            : LoadState.noMore,
      );
    }
  }

  /// 加载更多
  Future<void> loadMore() async {
    var categoryType = ref.read(currentSameStyleCategoryProvider);
    if (categoryType == null || categoryType.categoryType == null) return;
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await SameStyleService.sameExampleList(
      categoryType.categoryType!,
      state.pageNo,
      pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          exampleList: [...?state.exampleList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

// 跳转指定功能页，可能会带默认参数
@riverpod
class SameStyleAction extends _$SameStyleAction {
  @override
  void build() {
    return;
  }

  void sameAction(int? functionType, {String? functionConfig}) {
    try {
      var context = navigatorKey.currentContext;

      var funType = FunctionType.fromType(functionType);
      debugPrint("sameAction: ${funType?.label}");
      debugPrint("sameAction-functionConfig: $functionConfig");
      if (funType == null) return;

      switch (funType) {
        case FunctionType.agent:
          // 智能体
          if (functionConfig != null) {
            var config = jsonDecode(functionConfig);
            String id = config["id"];
            String? title = config["title"];
            bool needLogin = config["needLogin"] ?? false;
            ref
                .read(currentConversationProvider.notifier)
                .jumpConversation(id, title, needLogin);
          }
          break;
        case FunctionType.web:
          // h5
          if (functionConfig != null) {
            var config = jsonDecode(functionConfig);
            String? url = config["url"];
            String? title = config["title"];
            bool needLogin = config["needLogin"] ?? false;
            if (context != null) {
              if (needLogin) {
                RouterUtil.checkLogin(context, call: () async {
                  var userInfo =
                      await ref.read(userInfoProvider.notifier).getUserInfo();
                  url = "$url?phone=${userInfo?.phone}&userid=${userInfo?.id}";
                  if (context.mounted) {
                    context.push(
                      "/$webPage",
                      extra: {"url": url, "title": title},
                    );
                  }
                });
              } else {
                context.push(
                  "/$webPage",
                  extra: {"url": url, "title": title},
                );
              }
            }
          }
          break;
        case FunctionType.portraitPhoto:
          // 人像写真
          if (context != null) {
            if (functionConfig != null) {
              var config = jsonDecode(functionConfig);
              int? caseId = config["case_id"];
              if (caseId != null) {
                // 案例详情页
                RouterUtil.checkLogin(context, call: () {
                  context.push("/$photoPortraitDetailPage", extra: caseId);
                });
              } else {
                RouterUtil.checkLogin(context, call: () {
                  context.push("/${funType.label}");
                });
              }
            } else {
              RouterUtil.checkLogin(context, call: () {
                context.push("/${funType.label}");
              });
            }
          }
          break;
        case FunctionType.inner:
          // 内部其他页面
          if (context != null && functionConfig != null) {
            var config = jsonDecode(functionConfig);
            var path = config["path"];
            var needLogin = config["needLogin"] ?? false;
            if (needLogin) {
              RouterUtil.checkLogin(context, call: () {
                context.push('/$path');
              });
            } else {
              context.push('/$path');
            }
          }
          break;
        default:
          // 普通AI功能
          if (context != null) {
            if (functionConfig != null) {
              var config = jsonDecode(functionConfig);
              int? caseId = config["case_id"];
              bool needLogin = config["needLogin"];
              if (needLogin) {
                RouterUtil.checkLogin(context, call: () {
                  context.push("/${funType.label}", extra: caseId);
                });
              } else {
                context.push("/${funType.label}", extra: caseId);
              }
            } else {
              RouterUtil.checkLogin(context, call: () {
                context.push("/${funType.label}");
              });
            }
          }
          break;
      }
    } catch (e) {
      debugPrint("action convert error: $e");
    }
  }
}

// 跳转功能枚举
enum FunctionType {
  //"文生视频"
  textToVideo(1, textToVideoPage),

  //"首帧图片生视频"
  imageToVideo(2, videoGenerationPage),

  //"老照片修复"
  oldPhotoRepair(3, oldPhotoRestorationPage),

  //"画质变清晰"
  qualityEnhance(4, qualityRestorationPage),

  //"智能抠图"
  smartMatting(5, photoMattingPage),

  //"创意特效视频"
  creativeEffectVideo(6, "创意特效视频"),

  //"照片跳舞视频"
  photoDance(7, pictureDancingPage),

  //"AI绘图"
  aiDrawing(8, aiDrawingPage),

  //"AI改图"
  aiEditImage(9, photoModificationPage),

  //"人像写真"
  portraitPhoto(10, photoPortraitPage),

  //"服装商拍"
  clothingShoot(11, fashionShootPage),

  //"融合生视频"
  fusionVideo(12, referenceImageToVideoPage),

  // "照片增肌"
  muscleEnhance(13, photoMusclePage),

  // "对口型唱歌"
  lipSync(14, syncSingPage),

  // "商品图包装"
  productPackaging(15, figurePackagingPage),

  // "卡通数字人"
  cartoonDigitalHuman(16, cartoonDigimonPage),

  // "宠物唱歌"
  petSinging(17, petSingPage),

  // "海报设计"
  posterDesign(18, posterDesignPage),

  // "口播数字人"
  digitalHumanSpeech(19, mouthDigitalExamplePage),

  // "首尾帧生视频"
  frameGenerationVideo(20, "首尾帧生视频"),

  // 智能体
  agent(999, "智能体"),

  // h5
  web(888, "h5"),

  // 内部页面
  inner(111, "内部页面");

  final int type;
  final String label;

  const FunctionType(this.type, this.label);

  /// 根据 type 获取枚举
  static FunctionType? fromType(int? type) {
    try {
      return FunctionType.values.firstWhere((e) => e.type == type);
    } catch (_) {
      return null;
    }
  }
}
