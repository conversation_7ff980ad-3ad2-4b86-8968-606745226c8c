// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'same_style_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchSameStyleBannerListHash() =>
    r'5bf4c85f3a4f49dfee73710593f3dc40dc39af90';

/// See also [fetchSameStyleBannerList].
@ProviderFor(fetchSameStyleBannerList)
final fetchSameStyleBannerListProvider =
    AutoDisposeFutureProvider<List<SameStyleBanner>?>.internal(
  fetchSameStyleBannerList,
  name: r'fetchSameStyleBannerListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchSameStyleBannerListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchSameStyleBannerListRef
    = AutoDisposeFutureProviderRef<List<SameStyleBanner>?>;
String _$sameStyleCategoryListHash() =>
    r'8a27c0710c3fb5dafb96fff27f595f16232deb16';

/// See also [SameStyleCategoryList].
@ProviderFor(SameStyleCategoryList)
final sameStyleCategoryListProvider = AutoDisposeNotifierProvider<
    SameStyleCategoryList, List<SameCategory>?>.internal(
  SameStyleCategoryList.new,
  name: r'sameStyleCategoryListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sameStyleCategoryListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SameStyleCategoryList = AutoDisposeNotifier<List<SameCategory>?>;
String _$currentSameStyleCategoryHash() =>
    r'76146a817964c16bdf7372ad1a4a8a53b86b15d0';

/// 当前选中的同款分类类型
///
/// Copied from [CurrentSameStyleCategory].
@ProviderFor(CurrentSameStyleCategory)
final currentSameStyleCategoryProvider = AutoDisposeNotifierProvider<
    CurrentSameStyleCategory, SameCategory?>.internal(
  CurrentSameStyleCategory.new,
  name: r'currentSameStyleCategoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentSameStyleCategoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentSameStyleCategory = AutoDisposeNotifier<SameCategory?>;
String _$sameStyleExampleListHash() =>
    r'2e18bb6031ca8c904b9f1f0048f304c17031c870';

/// See also [SameStyleExampleList].
@ProviderFor(SameStyleExampleList)
final sameStyleExampleListProvider = AutoDisposeNotifierProvider<
    SameStyleExampleList, SameStyleListResult>.internal(
  SameStyleExampleList.new,
  name: r'sameStyleExampleListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sameStyleExampleListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SameStyleExampleList = AutoDisposeNotifier<SameStyleListResult>;
String _$sameStyleActionHash() => r'51abfb30d3d0409242ee2c429ec77f0f5d9dd4ec';

/// See also [SameStyleAction].
@ProviderFor(SameStyleAction)
final sameStyleActionProvider =
    AutoDisposeNotifierProvider<SameStyleAction, void>.internal(
  SameStyleAction.new,
  name: r'sameStyleActionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$sameStyleActionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SameStyleAction = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
