// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ai_drawing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$aidrawingHash() => r'7928cc54fa2c7aa23a3c4a239b58de8f01bf2a64';

/// See also [Aidrawing].
@ProviderFor(Aidrawing)
final aidrawingProvider =
    AutoDisposeNotifierProvider<Aidrawing, AidrawingDatas>.internal(
  Aidrawing.new,
  name: r'aidrawingProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$aidrawingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Aidrawing = AutoDisposeNotifier<AidrawingDatas>;
String _$aiDrawingRecordListHash() =>
    r'16d383509646a495d6e12f91c296fcb9595cb52f';

/// See also [AiDrawingRecordList].
@ProviderFor(AiDrawingRecordList)
final aiDrawingRecordListProvider = AutoDisposeNotifierProvider<
    AiDrawingRecordList, AiDrawingRecordData?>.internal(
  AiDrawingRecordList.new,
  name: r'aiDrawingRecordListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$aiDrawingRecordListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AiDrawingRecordList = AutoDisposeNotifier<AiDrawingRecordData?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
