// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poster_design_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$posterDesignHash() => r'ac5ff9acbc459a835db0c89abc92ea97ee540fa7';

/// See also [PosterDesign].
@ProviderFor(PosterDesign)
final posterDesignProvider =
    AutoDisposeNotifierProvider<PosterDesign, PosterDesignDatas>.internal(
  PosterDesign.new,
  name: r'posterDesignProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$posterDesignHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PosterDesign = AutoDisposeNotifier<PosterDesignDatas>;
String _$recordListHash() => r'08d9b60c63921304d6688d6c9addf39d08191b88';

/// See also [RecordList].
@ProviderFor(RecordList)
final recordListProvider =
    AutoDisposeNotifierProvider<RecordList, RecordData?>.internal(
  RecordList.new,
  name: r'recordListProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$recordListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RecordList = AutoDisposeNotifier<RecordData?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
