import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/poster_design_data.dart';
import 'package:text_generation_video/app/repository/modals/record/work_record.dart';
import 'package:text_generation_video/app/repository/service/poster_design_service.dart';
import 'package:text_generation_video/app/widgets/refresh/refresh_container.dart';

part 'poster_design_provider.g.dart';

class PosterDesignDatas {
  String? inputValue;
  List<PosterDesignData> posterCaseList;

  PosterDesignDatas({
    this.inputValue,
    this.posterCaseList = const [],
  });

  PosterDesignDatas copyWith({
    String? inputValue,
    List<PosterDesignData>? posterCaseList,
  }) {
    return PosterDesignDatas(
      inputValue: inputValue ?? this.inputValue,
      posterCaseList: posterCaseList ?? this.posterCaseList,
    );
  }
}

@riverpod
class PosterDesign extends _$PosterDesign {
  @override
  PosterDesignDatas build() {
    state = PosterDesignDatas();
    posterCaseList();
    return state;
  }

  posterCaseList() async {
    var result = await PosterDesignService.posterCaseList(18);
    debugPrint('怎么回事？$result');
    if (result.status == Status.completed) {
      state = state.copyWith(posterCaseList: result.data?.list ?? []);
    }
  }

  // 输入框的值变化时，更新state中的inputValue值
  getInputValue(String? value) {
    state = state.copyWith(inputValue: value);
  }

  posterDesign(String proportion) async {
    List size;
    switch (proportion) {
      case '1:1':
        size = [1328, 1328];
        break;
      case '4:3':
        size = [1472, 1104];
        break;
      case '3:2':
        size = [1584, 1056];
        break;
      case '16:9':
        size = [1664, 936];
        break;
      case '21:9':
        size = [2016, 864];
        break;
      default:
        size = [1328, 1328];
        break;
    }

    if (state.inputValue == null || state.inputValue == "") {
      return;
    }

    var params = {
      "prompt": state.inputValue,
      "width": size[0],
      "height": size[1],
    };

    SmartDialog.showLoading(msg: "上传中...");
    var result = await PosterDesignService.posterDesign(params);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      state = state.copyWith(inputValue: "");
      ref.read(recordListProvider.notifier).loadRecord();
      SmartDialog.showToast("上传成功");
    } else {
      SmartDialog.showToast("上传失败，请重试");
    }
  }
}

class RecordData {
  final int pageNum;
  final int pageSize;
  final List<WorkRecord>? records;
  final LoadState? loadState;

  const RecordData({
    this.pageNum = 1,
    this.pageSize = 20,
    this.records,
    this.loadState = LoadState.idle,
  });

  RecordData copyWith({
    int? page,
    int? size,
    List<WorkRecord>? records,
    LoadState? loadState,
  }) {
    return RecordData(
      pageNum: page ?? pageNum,
      pageSize: size ?? pageSize,
      records: records ?? this.records,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class RecordList extends _$RecordList {
  @override
  RecordData? build() {
    loadRecord();
    return null;
  }

  deleteRecord(List<int> idArray) async {
    SmartDialog.showLoading(msg: "删除中...");
    var result = await PosterDesignService.deleteRecord(idArray);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      SmartDialog.showToast("删除成功");
      loadRecord();
    }
  }

  /// 加载数据
  void loadRecord() async {
    state = const RecordData(loadState: LoadState.loading);
    var result = await PosterDesignService.aiWorkRecordList(
      18,
      state!.pageNum,
      state!.pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null && result.data!.list != null) {
        state = RecordData(
          records: result.data?.list,
          loadState: result.data!.hasNextPage ?? false
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMoreRecord() async {
    state = state?.copyWith(
      loadState: LoadState.loading,
      page: (state?.pageNum ?? 1) + 1,
    );
    var result = await PosterDesignService.aiWorkRecordList(
      18,
      state!.pageNum,
      state!.pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null && result.data!.list != null) {
        state = state?.copyWith(
          records: [...?state?.records, ...?result.data?.list],
          loadState: result.data!.hasNextPage ?? false
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }
}
