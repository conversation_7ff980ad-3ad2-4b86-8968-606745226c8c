// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_case_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchLipSyncCaseHash() => r'19b6ef1e73705370bb896182ef41b9df9c554506';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// 获取对口型案例列表
/// caseType
/// 1:卡通数字人
/// 2:对口型唱歌
/// 3:宠物唱歌
///
/// Copied from [fetchLipSyncCase].
@ProviderFor(fetchLipSyncCase)
const fetchLipSyncCaseProvider = FetchLipSyncCaseFamily();

/// 获取对口型案例列表
/// caseType
/// 1:卡通数字人
/// 2:对口型唱歌
/// 3:宠物唱歌
///
/// Copied from [fetchLipSyncCase].
class FetchLipSyncCaseFamily
    extends Family<AsyncValue<List<AudioImgToVideo>?>> {
  /// 获取对口型案例列表
  /// caseType
  /// 1:卡通数字人
  /// 2:对口型唱歌
  /// 3:宠物唱歌
  ///
  /// Copied from [fetchLipSyncCase].
  const FetchLipSyncCaseFamily();

  /// 获取对口型案例列表
  /// caseType
  /// 1:卡通数字人
  /// 2:对口型唱歌
  /// 3:宠物唱歌
  ///
  /// Copied from [fetchLipSyncCase].
  FetchLipSyncCaseProvider call(
    PageType caseType,
  ) {
    return FetchLipSyncCaseProvider(
      caseType,
    );
  }

  @override
  FetchLipSyncCaseProvider getProviderOverride(
    covariant FetchLipSyncCaseProvider provider,
  ) {
    return call(
      provider.caseType,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchLipSyncCaseProvider';
}

/// 获取对口型案例列表
/// caseType
/// 1:卡通数字人
/// 2:对口型唱歌
/// 3:宠物唱歌
///
/// Copied from [fetchLipSyncCase].
class FetchLipSyncCaseProvider
    extends AutoDisposeFutureProvider<List<AudioImgToVideo>?> {
  /// 获取对口型案例列表
  /// caseType
  /// 1:卡通数字人
  /// 2:对口型唱歌
  /// 3:宠物唱歌
  ///
  /// Copied from [fetchLipSyncCase].
  FetchLipSyncCaseProvider(
    PageType caseType,
  ) : this._internal(
          (ref) => fetchLipSyncCase(
            ref as FetchLipSyncCaseRef,
            caseType,
          ),
          from: fetchLipSyncCaseProvider,
          name: r'fetchLipSyncCaseProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchLipSyncCaseHash,
          dependencies: FetchLipSyncCaseFamily._dependencies,
          allTransitiveDependencies:
              FetchLipSyncCaseFamily._allTransitiveDependencies,
          caseType: caseType,
        );

  FetchLipSyncCaseProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caseType,
  }) : super.internal();

  final PageType caseType;

  @override
  Override overrideWith(
    FutureOr<List<AudioImgToVideo>?> Function(FetchLipSyncCaseRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchLipSyncCaseProvider._internal(
        (ref) => create(ref as FetchLipSyncCaseRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caseType: caseType,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<AudioImgToVideo>?> createElement() {
    return _FetchLipSyncCaseProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchLipSyncCaseProvider && other.caseType == caseType;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caseType.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchLipSyncCaseRef
    on AutoDisposeFutureProviderRef<List<AudioImgToVideo>?> {
  /// The parameter `caseType` of this provider.
  PageType get caseType;
}

class _FetchLipSyncCaseProviderElement
    extends AutoDisposeFutureProviderElement<List<AudioImgToVideo>?>
    with FetchLipSyncCaseRef {
  _FetchLipSyncCaseProviderElement(super.provider);

  @override
  PageType get caseType => (origin as FetchLipSyncCaseProvider).caseType;
}

String _$audioCaseToVideoCaseHash() =>
    r'a3e6e9c076b0b11172d75b0d1940a448db306b15';

/// See also [AudioCaseToVideoCase].
@ProviderFor(AudioCaseToVideoCase)
final audioCaseToVideoCaseProvider = AutoDisposeNotifierProvider<
    AudioCaseToVideoCase, AudioImgToVideo?>.internal(
  AudioCaseToVideoCase.new,
  name: r'audioCaseToVideoCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$audioCaseToVideoCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AudioCaseToVideoCase = AutoDisposeNotifier<AudioImgToVideo?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
