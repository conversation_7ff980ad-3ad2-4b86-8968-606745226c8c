// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentRecordTabHash() => r'9e7fd6aeb5e20dae8015a7d4f6167a950fcafa7b';

/// See also [CurrentRecordTab].
@ProviderFor(CurrentRecordTab)
final currentRecordTabProvider =
    AutoDisposeNotifierProvider<CurrentRecordTab, WorkRecordType>.internal(
  CurrentRecordTab.new,
  name: r'currentRecordTabProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentRecordTabHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentRecordTab = AutoDisposeNotifier<WorkRecordType>;
String _$recordPortfolioListHash() =>
    r'bd2fe721d5928ecc93151ab9678c64c276cc0741';

/// See also [RecordPortfolioList].
@ProviderFor(RecordPortfolioList)
final recordPortfolioListProvider =
    AutoDisposeNotifierProvider<RecordPortfolioList, RecordListResult>.internal(
  RecordPortfolioList.new,
  name: r'recordPortfolioListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$recordPortfolioListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$RecordPortfolioList = AutoDisposeNotifier<RecordListResult>;
String _$currentRecordEditStateHash() =>
    r'40957d4b46687cbf9647aec00148779b98e28a1f';

/// See also [CurrentRecordEditState].
@ProviderFor(CurrentRecordEditState)
final currentRecordEditStateProvider =
    AutoDisposeNotifierProvider<CurrentRecordEditState, bool>.internal(
  CurrentRecordEditState.new,
  name: r'currentRecordEditStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentRecordEditStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentRecordEditState = AutoDisposeNotifier<bool>;
String _$deleteRecordListHash() => r'6d090f7d5eb3cbf409a3558b00c25657ef47a8f7';

/// See also [DeleteRecordList].
@ProviderFor(DeleteRecordList)
final deleteRecordListProvider =
    AutoDisposeNotifierProvider<DeleteRecordList, List<WorkRecord>>.internal(
  DeleteRecordList.new,
  name: r'deleteRecordListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$deleteRecordListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeleteRecordList = AutoDisposeNotifier<List<WorkRecord>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
