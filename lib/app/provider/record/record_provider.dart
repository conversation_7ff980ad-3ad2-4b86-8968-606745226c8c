import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/repository/modals/record/work_record.dart';
import 'package:text_generation_video/app/repository/service/record_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

part 'record_provider.g.dart';

// 作品记录类型枚举
enum WorkRecordType {
  recent(-1, "最近生成"),
  video(1, "AI视频"),
  image(2, "AI图片"),
  lipSync(3, "AI对口型"),
  lifeOffice(4, "AI生活办公"),
  ecommerce(5, "AI电商");

  final int type;
  final String label;

  const WorkRecordType(this.type, this.label);
}

// 当前选中的tab
@riverpod
class CurrentRecordTab extends _$CurrentRecordTab {
  @override
  WorkRecordType build() {
    return WorkRecordType.recent;
  }

  void setCurrentTab(WorkRecordType tab) {
    state = tab;
    ref.read(recordPortfolioListProvider.notifier).loadData();
  }
}

/// 作品记录列表
/// 页长
const int pageSize = 20;

/// 作品记录列表结果
class RecordListResult {
  final int pageNo;
  final List<WorkRecord?>? workRecordList;
  final LoadState? loadState;

  const RecordListResult({
    this.pageNo = 1,
    this.workRecordList,
    this.loadState,
  });

  RecordListResult copyWith({
    int? page,
    List<WorkRecord?>? workRecordList,
    LoadState? loadState,
  }) {
    return RecordListResult(
      pageNo: page ?? pageNo,
      workRecordList: workRecordList ?? this.workRecordList,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class RecordPortfolioList extends _$RecordPortfolioList {
  @override
  RecordListResult build() {
    state = const RecordListResult();
    loadData();
    return state;
  }

  /// 加载数据
  Future<void> loadData() async {
    var userData = ref.watch(authProvider);
    var currentTab = ref.read(currentRecordTabProvider);
    if (userData != null) {
      state = state.copyWith(
        page: 1,
        loadState: null,
      );
      var result = await RecordService.workRecordList(
        state.pageNo,
        pageSize,
        categoryType: currentTab.type == -1 ? null : currentTab.type,
      );
      if (result.status == Status.completed) {
        state = state.copyWith(
          workRecordList: result.data?.list ?? [],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  Future<void> loadMore() async {
    var currentTab = ref.read(currentRecordTabProvider);
    state = state.copyWith(
      loadState: LoadState.loading,
      page: state.pageNo + 1,
    );
    var result = await RecordService.workRecordList(
      state.pageNo,
      pageSize,
      categoryType: currentTab.type == -1 ? null : currentTab.type,
    );
    if (result.status == Status.completed) {
      if (result.data != null) {
        state = state.copyWith(
          workRecordList: [...?state.workRecordList, ...?result.data?.list],
          loadState: result.data?.hasNextPage == true
              ? LoadState.idle
              : LoadState.noMore,
        );
      } else {
        state = state.copyWith(
          loadState: LoadState.noMore,
        );
      }
    }
  }
}

// 当前编辑状态
@riverpod
class CurrentRecordEditState extends _$CurrentRecordEditState {
  @override
  bool build() {
    return false;
  }

  void setEditState(bool value) {
    state = value;
  }
}

// 删除的列表
@riverpod
class DeleteRecordList extends _$DeleteRecordList {
  @override
  List<WorkRecord> build() {
    return [];
  }

  // 添加获取移除删除列表
  void addOrRemove(WorkRecord workRecord) {
    bool inDelete = state.any((e) => e.id == workRecord.id);
    if (inDelete) {
      // 移除删除列表
      state.removeWhere((e) => e.id == workRecord.id);
    } else {
      // 添加删除列表
      state.add(workRecord);
    }
    state = [...state];
  }

  // 删除
  void deleteRecord() async {
    var ids = state.map((e) => e.id).toList();
    if (ids.isEmpty) {
      ToastUtil.showToast("请选择要删除的作品");
      return;
    }
    SmartDialog.showLoading(msg: "删除中...");
    var result = await RecordService.deleteWorkRecordList(ids);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      // 刷新列表
      ref.read(recordPortfolioListProvider.notifier).loadData();
      // 取消编辑状态
      ref.read(currentRecordEditStateProvider.notifier).setEditState(false);
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}
