import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/text_to_video/text_to_video_provider.dart';
import 'package:text_generation_video/app/repository/modals/video/video_case.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';

part 'video_case_provider.g.dart';

// 获取热门案例列表
@riverpod
Future<List<VideoCase>?> fetchPopularVideoCase(
  Ref ref,
  int caseType,
  int? initialCase,
) async {
  var result = await TextToVideoService.listVideoCase(caseType);
  if (result.status == Status.completed) {
    if (initialCase != null && result.data != null && result.data!.isNotEmpty) {
      // 设置默认值
      var firstCase = result.data!.firstWhere((e) => e.id == initialCase);
      ref.read(videoEditTextProvider.notifier).setText(firstCase.casePrompt);
      ref.read(videoScaleParamProvider.notifier).caseValue(firstCase);
      ref.read(videoDurationParamProvider.notifier).caseValue(firstCase);
      ref.read(videoClarityParamProvider.notifier).caseValue(firstCase);
    }
    return result.data;
  }
  return null;
}
