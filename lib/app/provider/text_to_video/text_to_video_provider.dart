import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/repository/modals/video/video_case.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

import '../member/member_provider.dart';

part 'text_to_video_provider.g.dart';

@riverpod
class VideoEditText extends _$VideoEditText {
  final TextEditingController textEditingController = TextEditingController();

  @override
  TextEditingController build() {
    ref.onDispose(() => textEditingController.dispose());
    return textEditingController;
  }

  void setText(String? value) {
    textEditingController.text = value ?? "";
  }

  void clean() {
    textEditingController.clear();
  }
}

// 视频比例选择
class VideoScaleParamData {
  String scale;
  double width;
  double height;
  double margin;

  VideoScaleParamData(this.scale, this.width, this.height, this.margin);

  @override
  String toString() {
    return "Scale: $scale";
  }
}

final List<VideoScaleParamData> scaleParamList = [
  VideoScaleParamData("3:4", 24.75, 33, 13),
  VideoScaleParamData("4:3", 33, 24.75, 17),
  VideoScaleParamData("9:16", 24.19, 43, 8),
  VideoScaleParamData("16:9", 43, 24.19, 18),
];

@riverpod
class VideoScaleParam extends _$VideoScaleParam {
  @override
  VideoScaleParamData build() {
    return scaleParamList.first;
  }

  void select(VideoScaleParamData value) {
    state = value;
  }

  void caseValue(VideoCase videoCase) {
    var param = scaleParamList.firstWhere(
      (e) => e.scale == videoCase.ratio,
      orElse: () => scaleParamList.first,
    );
    state = param;
  }
}

// 视频时长选择
class VideoDurationParamData {
  String duration;
  int value;

  VideoDurationParamData(this.duration, this.value);

  @override
  String toString() {
    return "Duration: $value";
  }
}

final List<VideoDurationParamData> durationParamList = [
  VideoDurationParamData("5秒", 5),
  VideoDurationParamData("10秒", 10),
];

@riverpod
class VideoDurationParam extends _$VideoDurationParam {
  @override
  VideoDurationParamData build() {
    return durationParamList.first;
  }

  void select(VideoDurationParamData value) {
    state = value;
  }

  void caseValue(VideoCase videoCase) {
    var param = durationParamList.firstWhere(
      (e) => e.value == videoCase.duration,
      orElse: () => durationParamList.first,
    );
    state = param;
  }
}

// 视频模式选择
class VideoClarityParamData {
  String clarity;
  String value;

  VideoClarityParamData(this.clarity, this.value);

  @override
  String toString() {
    return "Clarity: $value";
  }
}

final List<VideoClarityParamData> clarityParamList = [
  VideoClarityParamData("标准", "480p"),
  VideoClarityParamData("高清", "720p"),
  VideoClarityParamData("超清", "1080p"),
];

@riverpod
class VideoClarityParam extends _$VideoClarityParam {
  @override
  VideoClarityParamData build() {
    return clarityParamList.first;
  }

  void select(VideoClarityParamData value) {
    state = value;
  }

  void caseValue(VideoCase videoCase) {
    var param = clarityParamList.firstWhere(
      (e) => e.value == videoCase.resolution,
      orElse: () => clarityParamList.first,
    );
    state = param;
  }
}

// 开始文本生成视频
@riverpod
class TextToVideoAction extends _$TextToVideoAction {
  @override
  void build() {
    return;
  }

  void textToVideo() async {
    var text = ref.read(videoEditTextProvider).text;
    if (text.isEmpty) {
      ToastUtil.showToast("请输入提示内容");
      return;
    }

    // 查询会员状态和剩余算力
    // 非会员和算力不足无法发送
    var checkMember =
        await ref.read(checkMemberProvider.notifier).checkMemberAndPower(
              consumerPowerItem: 100,
            );
    if (!checkMember) return;

    // 视频比例
    var scale = ref.read(videoScaleParamProvider);
    // 视频时长
    var duration = ref.read(videoDurationParamProvider);
    // 视频模式
    var clarity = ref.read(videoClarityParamProvider);

    SmartDialog.showLoading(msg: "提交中...");
    String content =
        "$text --rs ${clarity.value} --rt ${scale.scale} --dur ${duration.value}";
    var result = await TextToVideoService.textToVideoPage(text: content);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      navigatorKey.currentContext?.push("/$workRecordPage");
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}
