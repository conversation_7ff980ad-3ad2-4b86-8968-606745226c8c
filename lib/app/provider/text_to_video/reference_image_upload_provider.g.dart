// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reference_image_upload_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$referenceImageUploadHash() =>
    r'f9324e0952d4b3984d40db9b37be65531d081d96';

/// See also [ReferenceImageUpload].
@ProviderFor(ReferenceImageUpload)
final referenceImageUploadProvider = AutoDisposeNotifierProvider<
    ReferenceImageUpload, List<Modification>>.internal(
  ReferenceImageUpload.new,
  name: r'referenceImageUploadProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$referenceImageUploadHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ReferenceImageUpload = AutoDisposeNotifier<List<Modification>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
