import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/provider/text_to_video/text_to_video_provider.dart';
import 'package:text_generation_video/app/provider/video_generation/video_generation_provider.dart';
import 'package:text_generation_video/app/repository/modals/video/video_case.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

part 'reference_image_upload_provider.g.dart';

/// 最大支持的参考图数量
const int kReferenceImageMaxCount = 4;

@riverpod
class ReferenceImageUpload extends _$ReferenceImageUpload {
  ReferenceImageUpload();

  @override
  List<Modification> build() {
    return List.generate(kReferenceImageMaxCount, (_) => const Modification());
  }

  void removeImage(int index) {
    if (index < 0 || index >= kReferenceImageMaxCount) {
      return;
    }
    _setSlot(index, const Modification());
  }

  void caseValue(VideoCase videoCase) {
    final images = [
      videoCase.caseImg1,
      videoCase.caseImg2,
      videoCase.caseImg3,
      videoCase.caseImg4,
    ]
        .where((url) => url != null && url.trim().isNotEmpty)
        .map((url) => url!.trim())
        .toList();

    final List<Modification> next = List.generate(
      kReferenceImageMaxCount,
      (index) {
        if (index < images.length) {
          return Modification(
            state: 2,
            remoteUrl: images[index],
          );
        }
        return const Modification();
      },
    );

    state = next;

    ref
        .read(videoGenerationProvider.notifier)
        .setReferenceImageList(images);
  }

  // 上传图片
  Future<void> uploadImage(int index, String croppedImagePath) async {
    if (index < 0 || index >= kReferenceImageMaxCount) {
      return;
    }

    _setSlot(
      index,
      state[index].copyWith(
        state: 1,
        localUrl: croppedImagePath,
        remoteUrl: null,
      ),
    );

    final result = await TextToVideoService.repairUploadFile(croppedImagePath);
    if (result.status == Status.completed &&
        result.data != null &&
        result.data!.isNotEmpty) {
      _setSlot(
        index,
        state[index].copyWith(
          state: 2,
          localUrl: croppedImagePath,
          remoteUrl: result.data,
        ),
      );
    } else {
      ToastUtil.showToast(result.exception?.getMessage() ?? "图片上传失败");
      _setSlot(
        index,
        state[index].copyWith(state: 3),
      );
    }
  }

  List<String> resolvedRemoteUrls() {
    return state
        .where((item) => item.state == 2 && item.remoteUrl != null)
        .map((item) => item.remoteUrl!)
        .toList();
  }

  void _setSlot(int index, Modification value) {
    final List<Modification> next = List.of(state);
    next[index] = value;
    state = next;
  }

  Future<void> handleGenerate() async {
    final referenceImageList = resolvedRemoteUrls();
    if (referenceImageList.isEmpty) {
      ToastUtil.showToast("请至少上传一张参考图");
      return;
    }

    final editController = ref.read(videoEditTextProvider);
    final rawText = editController.text.trim();
    if (rawText.isEmpty) {
      ToastUtil.showToast("请输入提示内容");
      return;
    }

    final clarity = ref.read(videoClarityParamProvider);
    final scale = ref.read(videoScaleParamProvider);
    final duration = ref.read(videoDurationParamProvider);

    final payloadText =
        "$rawText --rs ${clarity.value} --rt ${scale.scale} --dur ${duration.value}";

    await ref.read(videoGenerationProvider.notifier).referenceImageToVideo(
          referenceImageList: referenceImageList,
          text: payloadText,
        );
  }
}
