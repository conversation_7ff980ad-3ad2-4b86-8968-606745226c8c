import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

/// 最大支持的参考图数量
const int kReferenceImageMaxCount = 4;

final referenceImageUploadProvider =
    NotifierProvider<ReferenceImageUploadNotifier, List<Modification>>(
  ReferenceImageUploadNotifier.new,
);

class ReferenceImageUploadNotifier extends Notifier<List<Modification>> {
  ReferenceImageUploadNotifier();

  final ImagePicker _picker = ImagePicker();

  @override
  List<Modification> build() {
    return List.generate(kReferenceImageMaxCount, (_) => const Modification());
  }

  Future<void> selectImage(int index) async {
    if (index < 0 || index >= kReferenceImageMaxCount) {
      return;
    }
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image == null) {
      return;
    }

    _setSlot(
      index,
      state[index].copyWith(
        state: 1,
        localUrl: image.path,
        remoteUrl: null,
      ),
    );

    final result = await TextToVideoService.repairUploadFile(image.path);
    if (result.status == Status.completed &&
        result.data != null &&
        result.data!.isNotEmpty) {
      _setSlot(
        index,
        state[index].copyWith(
          state: 2,
          localUrl: image.path,
          remoteUrl: result.data,
        ),
      );
    } else {
      ToastUtil.showToast(result.exception?.getMessage() ?? "图片上传失败");
      _setSlot(
        index,
        state[index].copyWith(state: 3),
      );
    }
  }

  void removeImage(int index) {
    if (index < 0 || index >= kReferenceImageMaxCount) {
      return;
    }
    _setSlot(index, const Modification());
  }

  List<String> resolvedRemoteUrls() {
    return state
        .where((item) => item.state == 2 && item.remoteUrl != null)
        .map((item) => item.remoteUrl!)
        .toList();
  }

  void _setSlot(int index, Modification value) {
    final List<Modification> next = List.of(state);
    next[index] = value;
    state = next;
  }
}
