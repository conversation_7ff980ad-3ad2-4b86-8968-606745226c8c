// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_case_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchPopularVideoCaseHash() =>
    r'3aba28518907c8d29afff011e9449deb955b9e3e';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchPopularVideoCase].
@ProviderFor(fetchPopularVideoCase)
const fetchPopularVideoCaseProvider = FetchPopularVideoCaseFamily();

/// See also [fetchPopularVideoCase].
class FetchPopularVideoCaseFamily extends Family<AsyncValue<List<VideoCase>?>> {
  /// See also [fetchPopularVideoCase].
  const FetchPopularVideoCaseFamily();

  /// See also [fetchPopularVideoCase].
  FetchPopularVideoCaseProvider call(
    int caseType,
    int? initialCase,
  ) {
    return FetchPopularVideoCaseProvider(
      caseType,
      initialCase,
    );
  }

  @override
  FetchPopularVideoCaseProvider getProviderOverride(
    covariant FetchPopularVideoCaseProvider provider,
  ) {
    return call(
      provider.caseType,
      provider.initialCase,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchPopularVideoCaseProvider';
}

/// See also [fetchPopularVideoCase].
class FetchPopularVideoCaseProvider
    extends AutoDisposeFutureProvider<List<VideoCase>?> {
  /// See also [fetchPopularVideoCase].
  FetchPopularVideoCaseProvider(
    int caseType,
    int? initialCase,
  ) : this._internal(
          (ref) => fetchPopularVideoCase(
            ref as FetchPopularVideoCaseRef,
            caseType,
            initialCase,
          ),
          from: fetchPopularVideoCaseProvider,
          name: r'fetchPopularVideoCaseProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchPopularVideoCaseHash,
          dependencies: FetchPopularVideoCaseFamily._dependencies,
          allTransitiveDependencies:
              FetchPopularVideoCaseFamily._allTransitiveDependencies,
          caseType: caseType,
          initialCase: initialCase,
        );

  FetchPopularVideoCaseProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caseType,
    required this.initialCase,
  }) : super.internal();

  final int caseType;
  final int? initialCase;

  @override
  Override overrideWith(
    FutureOr<List<VideoCase>?> Function(FetchPopularVideoCaseRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchPopularVideoCaseProvider._internal(
        (ref) => create(ref as FetchPopularVideoCaseRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caseType: caseType,
        initialCase: initialCase,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<VideoCase>?> createElement() {
    return _FetchPopularVideoCaseProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchPopularVideoCaseProvider &&
        other.caseType == caseType &&
        other.initialCase == initialCase;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caseType.hashCode);
    hash = _SystemHash.combine(hash, initialCase.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchPopularVideoCaseRef
    on AutoDisposeFutureProviderRef<List<VideoCase>?> {
  /// The parameter `caseType` of this provider.
  int get caseType;

  /// The parameter `initialCase` of this provider.
  int? get initialCase;
}

class _FetchPopularVideoCaseProviderElement
    extends AutoDisposeFutureProviderElement<List<VideoCase>?>
    with FetchPopularVideoCaseRef {
  _FetchPopularVideoCaseProviderElement(super.provider);

  @override
  int get caseType => (origin as FetchPopularVideoCaseProvider).caseType;
  @override
  int? get initialCase => (origin as FetchPopularVideoCaseProvider).initialCase;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
