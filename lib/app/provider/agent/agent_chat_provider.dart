import 'dart:convert';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/member/member_provider.dart';
import 'package:text_generation_video/app/repository/modals/agent/bot_info.dart';
import 'package:text_generation_video/app/repository/modals/agent/consume_compute_power.dart';
import 'package:text_generation_video/app/repository/modals/agent/conversation_msg_item.dart';
import 'package:text_generation_video/app/repository/modals/agent/upload_file.dart';
import 'package:text_generation_video/app/repository/service/agent_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:path/path.dart' as path; // 用于提取文件名
import 'package:ui_widgets/ui_widgets.dart' show LoadState;

import '../../../config/global_config.dart';

part 'agent_chat_provider.g.dart';

/// 消息item
class MessageItem {
  final int? id;
  final String? type; // 1: 文本，2: 图片，3: 文件
  final int? attribution; //0:自己，1:智能体
  final String? message; // 文本消息
  final String? chatId; // 对话id
  final ConversationMsgItem? conversationMsgItem; // 消息item

  const MessageItem({
    this.id,
    this.type,
    this.attribution,
    this.message,
    this.chatId,
    this.conversationMsgItem,
  });

  MessageItem copyWith({
    String? type,
    int? attribution,
    String? message,
    String? chatId,
    ConversationMsgItem? conversationMsgItem,
  }) {
    return MessageItem(
      type: type ?? this.type,
      attribution: attribution ?? this.attribution,
      message: message ?? this.message,
      chatId: chatId ?? this.chatId,
      conversationMsgItem: conversationMsgItem ?? this.conversationMsgItem,
    );
  }
}

// 开场白
@riverpod
class OpeningRemarks extends _$OpeningRemarks {
  @override
  BotInfo? build() {
    return null;
  }

  // 获取智能体配置信息
  void getBotInfo(String? botId) async {
    if (botId == null) return;
    var botInfoResult = await AgentService.getBotInfo(botId);
    if (botInfoResult.status == Status.completed) {
      state = botInfoResult.data;
    }
  }
}

// 当前消息列表加载状态
@riverpod
class CurrentMsgListFooterState extends _$CurrentMsgListFooterState {
  @override
  LoadState? build() {
    return LoadState.idle;
  }

  void setState(LoadState value) {
    state = value;
  }
}

// 历史消息
const int limit = 30;
String? lastID = "";

@riverpod
class AgentChatHistoryMessage extends _$AgentChatHistoryMessage {
  @override
  List<MessageItem>? build() {
    return null;
  }

  void loadHistoryMessage(String? id) async {
    if (id == null) return;
    ref
        .read(currentMsgListFooterStateProvider.notifier)
        .setState(LoadState.loading);
    var msgResult = await AgentService.queryConversationMessageList(id, limit);
    if (msgResult.status == Status.completed) {
      lastID = msgResult.data?.lastID;
      var data = msgResult.data?.items;
      if (data != null && data.isNotEmpty) {
        state = data
            .map(
              (e) => MessageItem(
                type: e.content_type,
                attribution: e.role == "user" ? 0 : 1,
                conversationMsgItem: e,
                chatId: e.chat_id,
              ),
            )
            .toList();
      }
      if (msgResult.data?.hasMore == false) {
        ref
            .read(currentMsgListFooterStateProvider.notifier)
            .setState(LoadState.noMore);
      } else {
        ref
            .read(currentMsgListFooterStateProvider.notifier)
            .setState(LoadState.idle);
      }
    }
  }

  void loadMoreHistoryMessage(String? id) async {
    if (id == null) return;
    if (lastID == null) return;
    ref
        .read(currentMsgListFooterStateProvider.notifier)
        .setState(LoadState.loading);
    var msgResult = await AgentService.queryConversationMessageList(
      id,
      limit,
      afterId: lastID,
    );
    if (msgResult.status == Status.completed) {
      lastID = msgResult.data?.lastID;
      var data = msgResult.data?.items;
      if (data != null && data.isNotEmpty) {
        state = [
          ...?state,
          ...data.map(
            (e) => MessageItem(
              type: e.content_type,
              attribution: e.role == "user" ? 0 : 1,
              conversationMsgItem: e,
              chatId: e.chat_id,
            ),
          ),
        ];
      }
      if (msgResult.data?.hasMore == false) {
        ref
            .read(currentMsgListFooterStateProvider.notifier)
            .setState(LoadState.noMore);
      } else {
        ref
            .read(currentMsgListFooterStateProvider.notifier)
            .setState(LoadState.idle);
      }
    }
  }
}

// 消息回复状态
@riverpod
class AgentChatReplyState extends _$AgentChatReplyState {
  @override
  bool build() {
    return false;
  }

  void setState(bool value) {
    state = value;
  }
}

// 根据chatId查询算力消耗值
@riverpod
class ComputePower extends _$ComputePower {
  @override
  ConsumeComputePower? build() {
    return null;
  }

  void getComputePower(String? chatId) async {
    if (chatId == null || chatId.isEmpty) return;
    var result = await AgentService.getRecordPowerById(chatId);
    if (result.status == Status.completed) {
      state = result.data;
    }
  }
}

// 新消息
@riverpod
class AgentChatMessage extends _$AgentChatMessage {
  @override
  List<MessageItem>? build() {
    return null;
  }

  Future<void> sendChatMessageStream({
    required String url,
    required Map<String, dynamic> payload,
    required void Function(String data) onData,
    void Function(String? event)? onEvent,
    void Function(Object error)? onError,
  }) async {
    try {
      final client = HttpClient();
      client.idleTimeout = const Duration(seconds: 90);
      final request = await client.postUrl(Uri.parse(url));

      request.headers.set(HttpHeaders.contentTypeHeader, 'application/json');
      request.headers.set(HttpHeaders.acceptHeader, 'text/event-stream');
      request.headers.set(
        "Authorization",
        "${GlobalConfig.account?.tokenPrefix} ${GlobalConfig.account?.token}",
      );
      request.add(utf8.encode(jsonEncode(payload)));

      final response = await request.close();

      if (response.statusCode != 200) {
        // throw HttpException('请求失败: ${response.statusCode}');
        if (onError != null) onError(response);
        return;
      }

      final stream =
          response.transform(utf8.decoder).transform(const LineSplitter());

      debugPrint("sendChatMessageStream: 开始监听 SSE 数据流...");

      String? event;
      StringBuffer? dataBuffer;

      await for (final line in stream) {
        debugPrint('[SSE LINE] $line');
        if (line.isEmpty) {
          if (dataBuffer != null) {
            final data = dataBuffer.toString();
            onData(data);
            if (onEvent != null) onEvent(event);
            dataBuffer = null;
            event = null;
          }
        } else if (line.startsWith('data:')) {
          dataBuffer ??= StringBuffer();
          dataBuffer.writeln(line.substring(5).trim());
        } else if (line.startsWith('event:')) {
          event = line.substring(6).trim();
        }
      }

      client.close();
    } catch (e) {
      if (onError != null) {
        onError(e);
      } else {
        debugPrint('❌ SSE 错误: $e');
      }
    }
  }

  // 用户发送消息
  void sendMessage(
    String? conversationId,
    String message, {
    TextEditingController? textEditingController,
  }) async {
    if (conversationId == null || conversationId.isEmpty) return;

    String fileId = "";
    var uploadImg = ref.read(uploadFileSelectorProvider);
    if (uploadImg.state == 2) {
      fileId = uploadImg.info?.fileInfo?.id ?? "";
    } else if (uploadImg.state == 1) {
      ToastUtil.showToast("请等待文件上传完成");
      return;
    }

    // 发送的消息不能为空
    if (message.isEmpty && fileId.isEmpty) {
      return;
    }

    // 查询会员状态和剩余算力
    // 非会员和算力不足无法发送
    var checkMember = await ref
        .read(checkMemberProvider.notifier)
        .checkMemberAndPower(type: 1);
    if (!checkMember) return;

    if (fileId.isNotEmpty) {
      var conversation = ConversationMsgItem()
        ..content =
            "[{\"type\":\"text\", \"text\":\"$message\"},{\"type\":\"file\",\"file_id\":\"\",\"file_url\":\"\",\"size\":0,\"name\":\"${uploadImg.info?.fileInfo?.file_name}\"}]";
      state = [
        MessageItem(
          type: "object_string",
          attribution: 0,
          message: message,
          conversationMsgItem: conversation,
        ),
        ...?state
      ];
    } else {
      state = [
        MessageItem(
          type: "text",
          attribution: 0,
          message: message,
        ),
        ...?state
      ];
    }

    String? itemChatId;
    var payload = {
      "conversationId": conversationId,
      "fileId": fileId,
      "msgContent": message,
    };
    debugPrint("payload: $payload");
    if (textEditingController != null) {
      textEditingController.clear();
    }
    ref.read(uploadFileSelectorProvider.notifier).clean();
    // 显示待回复loading
    ref.read(agentChatReplyStateProvider.notifier).setState(true);
    sendChatMessageStream(
      url: "${GlobalConfig.baseUrl}/cozeApi/steamChat",
      payload: payload,
      onData: (chunk) {
        var contentJson = jsonDecode(chunk);
        var chatId = contentJson['chatId'];
        if (chatId != null) {
          itemChatId = chatId;
        }
        var content = contentJson['content'];
        if (content != null) {
          ref.read(agentChatReplyStateProvider.notifier).setState(false);
          state = [
            MessageItem(
              type: "text",
              attribution: 1,
              message: content,
              chatId: itemChatId,
            ),
            ...?state,
          ];
        }
      },
      onError: (e) {
        debugPrint("sendChatMessageStream-e: $e");
        ref.read(agentChatReplyStateProvider.notifier).setState(false);
      },
    );
  }
}

/// 图片文件选择上传
class UploadResult {
  final UploadFile? info;
  final int state;

  const UploadResult({
    this.info,
    this.state = 0,
  });

  UploadResult copyWith({
    UploadFile? info,
    int? state,
  }) {
    return UploadResult(
      info: info ?? this.info,
      state: state ?? this.state,
    );
  }
}

@riverpod
class UploadFileSelector extends _$UploadFileSelector {
  @override
  UploadResult build() {
    return const UploadResult(info: null, state: 0);
  }

  // 清理
  void clean() {
    state = const UploadResult(info: null, state: 0);
  }

  // 选择图片
  void selectImg(ImageSource source) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: source);
    if (image != null) {
      // 上传图片
      // 上传中
      var name = path.basename(image.path);
      var info = UploadFileInfo()..file_name = name;
      state = UploadResult(info: UploadFile()..fileInfo = info, state: 1);
      var result = await AgentService.uploadFile(image.path, image.name);
      if (result.status == Status.completed) {
        // 上传完成
        var info = result.data;
        state = UploadResult(info: info, state: 2);
      } else {
        // 上传失败
        state = const UploadResult(info: null, state: 0);
      }
    }
  }

  // 选择文件
  void selectFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();
    if (result != null) {
      File file = File(result.files.single.path!);

      var name = path.basename(file.path);
      var info = UploadFileInfo()..file_name = name;
      state = UploadResult(info: UploadFile()..fileInfo = info, state: 1);
      var uploadResult = await AgentService.uploadFile(file.path, name);
      if (uploadResult.status == Status.completed) {
        // 上传完成
        var info = uploadResult.data;
        state = UploadResult(info: info, state: 2);
      } else {
        // 上传失败
        state = const UploadResult(info: null, state: 0);
      }
    }
  }
}
