// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_chat_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$openingRemarksHash() => r'30499c6534c4e1babeef92e5017757995ea3a8cd';

/// See also [OpeningRemarks].
@ProviderFor(OpeningRemarks)
final openingRemarksProvider =
    AutoDisposeNotifierProvider<OpeningRemarks, BotInfo?>.internal(
  OpeningRemarks.new,
  name: r'openingRemarksProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$openingRemarksHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$OpeningRemarks = AutoDisposeNotifier<BotInfo?>;
String _$currentMsgListFooterStateHash() =>
    r'e334e853fcef52cc2265c91f1499dd2da6628548';

/// See also [CurrentMsgListFooterState].
@ProviderFor(CurrentMsgListFooterState)
final currentMsgListFooterStateProvider =
    AutoDisposeNotifierProvider<CurrentMsgListFooterState, LoadState?>.internal(
  CurrentMsgListFooterState.new,
  name: r'currentMsgListFooterStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentMsgListFooterStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentMsgListFooterState = AutoDisposeNotifier<LoadState?>;
String _$agentChatHistoryMessageHash() =>
    r'06e31763dac8c4b6d40848fcc64edcdce520c950';

/// See also [AgentChatHistoryMessage].
@ProviderFor(AgentChatHistoryMessage)
final agentChatHistoryMessageProvider = AutoDisposeNotifierProvider<
    AgentChatHistoryMessage, List<MessageItem>?>.internal(
  AgentChatHistoryMessage.new,
  name: r'agentChatHistoryMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$agentChatHistoryMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AgentChatHistoryMessage = AutoDisposeNotifier<List<MessageItem>?>;
String _$agentChatReplyStateHash() =>
    r'185538a0b878e2ce73a91970f6ba5a3f165e8ce3';

/// See also [AgentChatReplyState].
@ProviderFor(AgentChatReplyState)
final agentChatReplyStateProvider =
    AutoDisposeNotifierProvider<AgentChatReplyState, bool>.internal(
  AgentChatReplyState.new,
  name: r'agentChatReplyStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$agentChatReplyStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AgentChatReplyState = AutoDisposeNotifier<bool>;
String _$computePowerHash() => r'4c905d1ca95ffda5f5a21a49dfbd6a091ace6336';

/// See also [ComputePower].
@ProviderFor(ComputePower)
final computePowerProvider =
    AutoDisposeNotifierProvider<ComputePower, ConsumeComputePower?>.internal(
  ComputePower.new,
  name: r'computePowerProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$computePowerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ComputePower = AutoDisposeNotifier<ConsumeComputePower?>;
String _$agentChatMessageHash() => r'8ae289e74efc4f55552bed34252e825dfd8affb6';

/// See also [AgentChatMessage].
@ProviderFor(AgentChatMessage)
final agentChatMessageProvider =
    AutoDisposeNotifierProvider<AgentChatMessage, List<MessageItem>?>.internal(
  AgentChatMessage.new,
  name: r'agentChatMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$agentChatMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AgentChatMessage = AutoDisposeNotifier<List<MessageItem>?>;
String _$uploadFileSelectorHash() =>
    r'ff755c7449c7d63fe6b4277825c7fb2e84ce5175';

/// See also [UploadFileSelector].
@ProviderFor(UploadFileSelector)
final uploadFileSelectorProvider =
    AutoDisposeNotifierProvider<UploadFileSelector, UploadResult>.internal(
  UploadFileSelector.new,
  name: r'uploadFileSelectorProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$uploadFileSelectorHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UploadFileSelector = AutoDisposeNotifier<UploadResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
