// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fashion_shoot_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fashionShootHash() => r'ac2bfea50d64c8fa0f77b43ba42d7ec0a5184d8a';

/// See also [FashionShoot].
@ProviderFor(FashionShoot)
final fashionShootProvider =
    AutoDisposeNotifierProvider<FashionShoot, FashionShootData>.internal(
  FashionShoot.new,
  name: r'fashionShootProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fashionShootHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FashionShoot = AutoDisposeNotifier<FashionShootData>;
String _$clothesListCaseHash() => r'9d681c75594e337f0413a534e0354baaf3da4f5f';

/// See also [ClothesListCase].
@ProviderFor(ClothesListCase)
final clothesListCaseProvider =
    AutoDisposeNotifierProvider<ClothesListCase, ClothesListCaseData>.internal(
  ClothesListCase.new,
  name: r'clothesListCaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$clothesListCaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ClothesListCase = AutoDisposeNotifier<ClothesListCaseData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
