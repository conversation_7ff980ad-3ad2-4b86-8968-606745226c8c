import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text_generation_video/app/repository/service/fashion_shoot_service.dart';
import 'package:text_generation_video/app/repository/service/video_generation_service.dart';

part 'fashion_shoot_provider.g.dart';

class FashionShootData {
  /// 页面状态类型，0为上装图片，1为下装图片，2为模特图片
  int stateType;

  /// 服装数量，1：为1件服装，2：为两件服装
  int garmentsType;

  /// 上装图片
  String? clothingImage;

  /// 下装图片
  String? bottomWearImage;

  /// 模特图片
  String? modelImage;

  /// 是否保留上半身
  bool keepUpper;

  /// 是否保留下半身
  bool keepLower;

  /// 是否保留脚部
  bool keepFoot;

  /// 是否保留手部
  bool keepHand;

  FashionShootData({
    this.garmentsType = 1,
    this.clothingImage,
    this.modelImage,
    this.bottomWearImage,
    this.stateType = 0,
    this.keepUpper = true,
    this.keepLower = true,
    this.keepFoot = true,
    this.keepHand = true,
  });

  FashionShootData copyWith({
    int? garmentsType,
    String? clothingImage,
    String? modelImage,
    String? bottomWearImage,
    int? stateType,
    bool? keepUpper,
    bool? keepLower,
    bool? keepFoot,
    bool? keepHand,
  }) {
    return FashionShootData(
      garmentsType: garmentsType ?? this.garmentsType,
      clothingImage: clothingImage ?? this.clothingImage,
      modelImage: modelImage ?? this.modelImage,
      bottomWearImage: bottomWearImage ?? this.bottomWearImage,
      stateType: stateType ?? this.stateType,
      keepUpper: keepUpper ?? this.keepUpper,
      keepLower: keepLower ?? this.keepLower,
      keepFoot: keepFoot ?? this.keepFoot,
      keepHand: keepHand ?? this.keepHand,
    );
  }
}

@riverpod
class FashionShoot extends _$FashionShoot {
  @override
  FashionShootData build() {
    state = FashionShootData();
    // videoListCase();
    return state;
  }

  getModelProtection(int modelState) {
    switch (modelState) {
      case 0:
        state = state.copyWith(keepUpper: !state.keepUpper);
        break;
      case 1:
        state = state.copyWith(keepLower: !state.keepLower);
        break;
      case 2:
        state = state.copyWith(keepFoot: !state.keepFoot);
        break;
      case 3:
        state = state.copyWith(keepHand: !state.keepHand);
        break;
    }
  }

  /// 获取选择上装 || 下装 || 模特图片状态
  getStateType(int stateType) {
    state = state.copyWith(stateType: stateType);
    ref.read(clothesListCaseProvider.notifier).clothesListCaseTag(stateType);
    ref.read(clothesListCaseProvider.notifier).resetData();
    ref
        .read(clothesListCaseProvider.notifier)
        .assignmentData(stateType == 2 ? 'modal-key' : 'clothes-key');
  }

  /// 获取模特图片
  getModelImage(String imageUrl, int stateType) {
    if (stateType == 0) {
      state = state.copyWith(clothingImage: imageUrl);
    }

    if (stateType == 1) {
      state = state.copyWith(bottomWearImage: imageUrl);
    }

    if (stateType == 2) {
      state = state.copyWith(modelImage: imageUrl);
    }
  }

  /// 获取服装类型
  getGarmentsType(int garmentsType) {
    state = state.copyWith(garmentsType: garmentsType);
  }

  /// 服装商拍提交
  imageSubmit() async {
    var data = {
      "keepFoot": state.keepFoot,
      "keepHand": state.keepHand,
      "keepLower": state.keepLower,
      "keepUpper": state.keepUpper,
      "modelUrl": state.modelImage,
      "upperUrl": state.clothingImage
    };

    if (state.garmentsType == 2 &&
        state.bottomWearImage != null &&
        state.bottomWearImage != "") {
      data["bottomUrl"] = state.bottomWearImage;
    }
    SmartDialog.showLoading(msg: "上传中...");
    var result = await FashionShootService.imageSubmit(data);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      SmartDialog.showToast("上传成功");
    } else {
      SmartDialog.showToast("上传失败，请重试");
    }
  }
}

class ClothesListCaseData {
  ///主页面的模特案例数据
  List caseList;

  /// 分类tab数据列表
  List classTabData;

  /// 分类tab索引
  int classTabIndex;

  /// 分类案例数据列表
  List classCaseData;

  /// 缓存图片数据列表
  List cacheImageData;

  ClothesListCaseData({
    this.caseList = const [],
    this.classTabData = const [],
    this.classCaseData = const [],
    this.classTabIndex = 0,
    this.cacheImageData = const [],
  });

  ClothesListCaseData copyWith({
    List? caseList,
    List? classTabData,
    List? classCaseData,
    int? classTabIndex,
    List? cacheImageData,
  }) {
    return ClothesListCaseData(
      caseList: caseList ?? this.caseList,
      classTabData: classTabData ?? this.classTabData,
      classCaseData: classCaseData ?? this.classCaseData,
      classTabIndex: classTabIndex ?? this.classTabIndex,
      cacheImageData: cacheImageData ?? this.cacheImageData,
    );
  }
}

@riverpod
class ClothesListCase extends _$ClothesListCase {
  @override
  ClothesListCaseData build() {
    state = ClothesListCaseData();
    clothesListCase("1", "热门");
    return state;
  }

  Future<XFile?> webSelectImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? response = await picker.pickImage(source: ImageSource.gallery);
    // debugPrint('蓝色背景${response?.readAsBytes()} ----');

    return response;
  }

  // 选择图片
  void selectImg() async {
    var stateType =
        ref.read(fashionShootProvider.select((value) => value.stateType));
    var filePickerResult = await webSelectImage();
    if (filePickerResult != null) {
      // 上传图片
      // 上传中
      final fileBytes = await filePickerResult.readAsBytes();
      // state = VideoGenerationData(iamge: UploadFile()..fileInfo = info);
      SmartDialog.showLoading(msg: "上传中...");
      var result = await VideoGenerationService.uploadImage(
          fileBytes, filePickerResult.name);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        // 上传完成
        // debugPrint('图片上传成功${result.data}');
        List<String> imageData = [...state.cacheImageData, result.data];
        saveArrayToLocal(
            imageData, stateType == 2 ? "modal-key" : "clothes-key");
        state = state.copyWith(
          cacheImageData: imageData,
        );
      } else {
        SmartDialog.showToast("上传失败，请重试");
        // debugPrint('图片上传失败${result}');
        // 上传失败
        // state = state.copyWith(image: null);
      }
    }
  }

  // 保存数组到本地
  saveArrayToLocal(List<String> array, String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(key, array);
  }

  // 从本地读取数组
  getArrayFromLocal(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(key) ?? [];
  }

  assignmentData(String key) async {
    // debugPrint('assignmentData ---- ${getArrayFromLocal(key)}');
    var data = await getArrayFromLocal(key);
    state = state.copyWith(cacheImageData: data);
  }

  resetData() {
    state = state.copyWith(
      /// 分类tab数据列表
      classTabData: [],

      /// 分类tab索引
      classTabIndex: 0,

      /// 分类案例数据列表
      classCaseData: [],
    );
  }

  /// 获取分类tab索引
  getClassTabIndex(int index) {
    var stateType =
        ref.read(fashionShootProvider.select((value) => value.stateType));
    state = state.copyWith(classTabIndex: index);
    clothesListCase(
      stateType == 2 ? "1" : "2",
      state.classTabData[index],
      classType: true,
    );
  }

  clothesListCase(String caseType, String tag, {classType = false}) async {
    var result = await FashionShootService.clothesListCase(caseType, tag);
    if (result.status == Status.completed) {
      // 上传完成
      // SmartDialog.showToast("上传成功");
      if (classType) {
        state = state.copyWith(classCaseData: [...result.data]);
        return;
      }
      state = state.copyWith(caseList: [...result.data]);
    } else {
      // SmartDialog.showToast("上传失败，请重试");
    }
  }

  clothesListCaseTag(stateType) async {
    var result = await FashionShootService.clothesListCaseTag(
      stateType == 2 ? "1" : "2",
    );
    if (result.status == Status.completed) {
      // 上传完成
      state = state.copyWith(classTabData: [...result.data]);
      if (result.data != null && result.data.isNotEmpty) {
        clothesListCase(
          stateType == 2 ? "1" : "2",
          result.data[0],
          classType: true,
        );
      }
    } else {
      // SmartDialog.showToast("上传失败，请重试");
    }
  }
}
