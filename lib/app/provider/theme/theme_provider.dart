import 'package:flutter/material.dart';

import '../../../config/theme_config.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: theme_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/17 15:24
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 15:24
/// @UpdateRemark: 更新说明
ThemeData get lightTheme => ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primarySwatch: lightColor,
      fontFamily: "NotoSansSC",
      highlightColor: Colors.transparent,
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: Colors.blueAccent,
        selectionColor: Color(0xFFC4E9FF),
        selectionHandleColor: Color(0xFFC4E9FF),
      ),
      scaffoldBackgroundColor: const Color(0xFF18161A),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Color(0xFF141414),
        selectedLabelStyle: TextStyle(fontSize: 12),
        unselectedLabelStyle: TextStyle(fontSize: 12),
        unselectedItemColor: Color(0xFF86878B),
        selectedItemColor: Color(0xFFFFFFFF),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: Color(0xFF00CD6E),
      ),
      tabBarTheme: const TabBarTheme(
        labelColor: Color(0xFF000000),
        unselectedLabelColor: Color(0xFF666666),
        splashFactory: NoSplash.splashFactory,
        labelStyle: TextStyle(fontFamily: "NotoSansSC"),
        unselectedLabelStyle: TextStyle(fontFamily: "NotoSansSC"),
      ),
    );

ThemeData get darkTheme => ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: darkColor,
      fontFamily: "NotoSansSC",
      highlightColor: Colors.transparent,
      textSelectionTheme: const TextSelectionThemeData(
        cursorColor: Colors.blueAccent,
        selectionColor: Color(0xFFC4E9FF),
        selectionHandleColor: Color(0xFFC4E9FF),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF18161A),
        centerTitle: true,
      ),
      scaffoldBackgroundColor: const Color(0xFF18161A),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Color(0xFF141414),
        selectedLabelStyle: TextStyle(fontSize: 12),
        unselectedLabelStyle: TextStyle(fontSize: 12),
        unselectedItemColor: Color(0xFF86878B),
        selectedItemColor: Color(0xFFFFFFFF),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: Color(0xFF00CD6E),
      ),
      tabBarTheme: const TabBarTheme(
        labelColor: Color(0xFF000000),
        unselectedLabelColor: Color(0xFF666666),
        splashFactory: NoSplash.splashFactory,
        labelStyle: TextStyle(fontFamily: "NotoSansSC"),
        unselectedLabelStyle: TextStyle(fontFamily: "NotoSansSC"),
      ),
    );
