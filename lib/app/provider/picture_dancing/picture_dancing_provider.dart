import 'package:flutter/widgets.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/service/picture_dancing_service.dart';
import 'package:text_generation_video/app/repository/service/video_generation_service.dart';

part 'picture_dancing_provider.g.dart';

class PictureDancingData {
  String? image;
  List? dancingTag;
  int index;
  List? videoList;
  int videoIndex = 0;

  PictureDancingData({
    this.image,
    this.dancingTag = const [],
    this.index = 0,
    this.videoList = const [],
    this.videoIndex = 0,
  });

  PictureDancingData copyWith({
    String? image,
    List? dancingTag,
    int? index,
    List? videoList,
    int? videoIndex,
  }) {
    debugPrint('看下内容${videoIndex ?? this.videoIndex}');

    return PictureDancingData(
      image: image ?? this.image,
      dancingTag: dancingTag ?? this.dancingTag,
      index: index ?? this.index,
      videoList: videoList ?? this.videoList,
      videoIndex: videoIndex ?? this.videoIndex,
    );
  }
}

@riverpod
class PictureDancing extends _$PictureDancing {
  @override
  PictureDancingData build() {
    state = PictureDancingData();
    videoListCase();
    return state;
  }

  Future<XFile?> webSelectImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? response = await picker.pickImage(source: ImageSource.gallery);
    // debugPrint('蓝色背景${response?.readAsBytes()} ----');

    return response;
  }

  // 选择图片
  void selectImg() async {
    var filePickerResult = await webSelectImage();
    if (filePickerResult != null) {
      // 上传图片
      // 上传中
      final fileBytes = await filePickerResult.readAsBytes();
      // state = VideoGenerationData(iamge: UploadFile()..fileInfo = info);
      var result = await VideoGenerationService.uploadImage(
          fileBytes, filePickerResult.name);
      if (result.status == Status.completed) {
        // 上传完成
        // debugPrint('图片上传成功${result.data}');
        state = state.copyWith(
          image: result.data,
        );
      } else {
        // debugPrint('图片上传失败${result}');
        // 上传失败
        state = state.copyWith(image: null);
      }
    }
  }

  videoDancingEffects() async {
    if (state.image == null ||
        state.image == "" ||
        state.videoList == null ||
        state.videoList!.isEmpty) {
      return;
    }
    SmartDialog.showLoading(msg: "上传中...");

    var result = await VideoGenerationService.videoDancingEffects(
      state.image!,
      state.videoList?[state.videoIndex].videoUrl,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      // 上传完成
      SmartDialog.showToast("上传成功");
    } else {
      SmartDialog.showToast("上传失败，请重试");
    }
  }

  getTagIndex(int index) {
    // debugPrint('来了了${index}');
    state = state.copyWith(
      index: index,
      videoIndex: 0,
    );
    listCase();
  }

  getVideoIndex(int index) {
    state = state.copyWith(videoIndex: index);
    // if (state.videoList!.length - 1 < state.videoIndex) {
    //   state = state.copyWith(videoIndex: 0);
    // } else {}
  }

  videoListCase() async {
    var result = await PictureDancingService.videoListCase();
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      state = state.copyWith(dancingTag: result.data);
      listCase();
      // 上传完成
      // SmartDialog.showToast("上传成功");
    } else {
      // SmartDialog.showToast("上传失败，请重试");
    }
  }

  listCase() async {
    var result = await PictureDancingService.listCase(
      state.dancingTag?[state.index],
    );
    // SmartDialog.dismiss();
    if (result.status == Status.completed) {
      state = state.copyWith(videoList: result.data);

      // 上传完成
      // SmartDialog.showToast("上传成功");
    } else {
      // SmartDialog.showToast("上传失败，请重试");
    }
  }
}
