// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'picture_dancing_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$pictureDancingHash() => r'd973c4b5e6b286485b23c25c777cc5fef81607a0';

/// See also [PictureDancing].
@ProviderFor(PictureDancing)
final pictureDancingProvider =
    AutoDisposeNotifierProvider<PictureDancing, PictureDancingData>.internal(
  PictureDancing.new,
  name: r'pictureDancingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$pictureDancingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PictureDancing = AutoDisposeNotifier<PictureDancingData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
