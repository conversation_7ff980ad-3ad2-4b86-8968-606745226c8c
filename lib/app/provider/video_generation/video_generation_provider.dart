import 'package:flutter/foundation.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
// import 'package:path/path.dart' as path;
import 'package:text_generation_video/app/repository/modals/video_generation/video_list_case_data.dart';
import 'package:text_generation_video/app/repository/service/video_generation_service.dart';

part 'video_generation_provider.g.dart';

class VideoGenerationData {
  // 头部tab模式切换 1-图生模式，2-文生模式 3-创意特效
  int tabType;

  // 视频类型：1-普通模式，2-首尾帧，3-图文+图片
  int patternType = 1;

  // 首帧图片地址
  String? image;

  // 尾帧图片地址
  String? image2;

  // 参考图片扩展地址
  String? image3;
  String? image4;

  // 尾部图片地址
  String? tailImage;

  // 创意描述文本内容
  String? textValue;

  List<int> videoDurationData = [5, 10];

  // 视频时长（秒）
  int? videoDurationIndex;

  // 视频分辨率
  List<String> videoQualityData = ['标准', '高清', '超清'];

  // 视频分辨率索引
  int? videoQualityIndex;

  VideoGenerationData({
    this.tabType = 1,
    this.patternType = 1,
    this.image2,
    this.image3,
    this.image4,
    this.image,
    this.tailImage,
    this.textValue,
    this.videoDurationIndex = 0,
    this.videoQualityIndex = 0,
  });

  VideoGenerationData copyWith({
    int? tabType,
    int? patternType,
    String? image,
    String? image2,
    String? image3,
    String? image4,
    String? tailImage,
    String? textValue,
    int? wordCount,
    int? videoDurationIndex,
    int? videoQualityIndex,
  }) {
    return VideoGenerationData(
      tabType: tabType ?? this.tabType,
      patternType: patternType ?? this.patternType,
      image: image ?? this.image,
      image2: image2 ?? this.image2,
      tailImage: tailImage ?? this.tailImage,
      textValue: textValue ?? this.textValue,
      videoDurationIndex: videoDurationIndex ?? this.videoDurationIndex,
      videoQualityIndex: videoQualityIndex ?? this.videoQualityIndex,
    );
  }
}

@riverpod
class VideoGeneration extends _$VideoGeneration {
  @override
  VideoGenerationData build() {
    return VideoGenerationData();
  }

  Future<XFile?> webSelectImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? response = await picker.pickImage(source: ImageSource.gallery);
    // debugPrint('蓝色背景${response?.readAsBytes()} ----');

    return response;
  }

  // 选择图片
  void selectImg(patternType) async {
    var filePickerResult = await webSelectImage();
    if (filePickerResult != null) {
      // 上传图片
      // 上传中
      final fileBytes = await filePickerResult.readAsBytes();
      // state = VideoGenerationData(iamge: UploadFile()..fileInfo = info);
      var result = await VideoGenerationService.uploadImage(
          fileBytes, filePickerResult.name);
      if (result.status == Status.completed) {
        // 上传完成
        debugPrint('图片上传成功${result.data}');
        if (patternType == 1) {
          state = state.copyWith(
            image: result.data,
          );
        }

        if (patternType == 2) {
          state = state.copyWith(
            image2: result.data,
          );
        }
      } else {
        // debugPrint('图片上传失败${result}');
        // 上传失败
        state = state.copyWith(image: null);
        if (patternType == 1) {
          state = state.copyWith(
            image: null,
          );
        }

        if (patternType == 2) {
          state = state.copyWith(
            image2: null,
          );
        }
      }
    }
  }

  tabTypeChange(int index) {
    state = state.copyWith(
      tabType: index,
      image: "",
      image2: "",
      textValue: "",
    );
  }

  switchPatternType(int index) {
    state = state.copyWith(
      patternType: index,
      image: "",
      image2: "",
      textValue: "",
    );
  }

  // 获取用户输入的创意描述文本
  getInputText(value) {
    state = state.copyWith(
      textValue: value,
    );
  }

  // 获取视频时长下标（秒）
  getVideoDurationIndex(int index) {
    state = state.copyWith(
      videoDurationIndex: index,
    );
    // debugPrint('视频时长下标$index');
  }

  // 获取视频分辨率下标
  getVideoQualityIndex(int index) {
    state = state.copyWith(
      videoQualityIndex: index,
    );
    // debugPrint('视频分辨率下标$index');
  }

  makeVideosClick() {
    if (state.tabType == 3) {
      videoSpecialEffects();
      return;
    }
    if (state.tabType == 1) {
      if (state.patternType == 1) {
        imgToVideoByFirstFrame();
      }

      if (state.patternType == 2) {
        imgToVideoByFirstAndLastFrame();
      }
    }
  }

  videoSpecialEffects() async {
    var caseData =
        ref.read(creativeEffectsProvider.select((value) => value.caseData));
    var caseIndex = ref
        .read(creativeEffectsProvider.select((value) => value.selectedIndex));
    if (state.image != null &&
        state.image != "" &&
        caseData != null &&
        caseData[caseIndex].templateId != null) {
      SmartDialog.showLoading(msg: "上传中...");
      var result = await VideoGenerationService.videoSpecialEffects(
        [state.image],
        caseData[caseIndex].templateId!,
      );
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        // 上传完成
        debugPrint('视频特效生成成功$result');
        SmartDialog.showToast("上传成功");
      } else {
        // 上传失败
        // debugPrint('视频特效生成失败${result}');
        SmartDialog.showToast("上传失败，请重试");
      }
    } else {
      debugPrint('图片或文本不能为空');
    }
  }

  imgToVideoByFirstAndLastFrame() async {
    if (state.image != null &&
        state.image != "" &&
        state.image2 != null &&
        state.image2 != "" &&
        state.textValue != null &&
        state.textValue != "") {
      var videoQualityOptions = ['480p', '720p', '1080p'];
      var selectedQuality = videoQualityOptions[state.videoQualityIndex ?? 0];

      var textValue =
          "${state.textValue} --rs $selectedQuality --rt 16:9 --dur ${state.videoDurationData[state.videoDurationIndex ?? 0]}";
      SmartDialog.showLoading(msg: "上传中...");
      var result = await VideoGenerationService.imgToVideoByFirstAndLastFrame(
        state.image!,
        state.image2!,
        textValue,
      );
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        // 上传完成
        debugPrint('图片转视频成功$result');
        SmartDialog.showToast("上传成功");
      } else {
        // 上传失败
        // debugPrint('图片转视频失败${result}');
        SmartDialog.showToast("上传失败，请重试");
      }
    } else {
      debugPrint('图片或文本不能为空');
    }
  }

  imgToVideoByFirstFrame() async {
    if (state.image != null &&
        state.image != "" &&
        state.textValue != null &&
        state.textValue != "") {
      var videoQualityOptions = ['480p', '720p', '1080p'];
      var selectedQuality = videoQualityOptions[state.videoQualityIndex ?? 0];

      var textValue =
          "${state.textValue} --rs $selectedQuality --rt 16:9 --dur ${state.videoDurationData[state.videoDurationIndex ?? 0]}";
      SmartDialog.showLoading(msg: "上传中...");
      var result = await VideoGenerationService.imgToVideoByFirstFrame(
        state.image!,
        textValue,
      );
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        // 上传完成
        debugPrint('图片转视频成功${result.data}');
        SmartDialog.showToast("上传成功");
      } else {
        // 上传失败
        // debugPrint('图片转视频失败${result.error}');
        SmartDialog.showToast("上传失败，请重试");
      }
    } else {
      debugPrint('图片或文本不能为空');
    }
  }

  referenceImageToVideo() async {
    if (state.image != null &&
        state.image != "" &&
        state.image2 != null &&
        state.image2 != "" &&
        state.image3 != null &&
        state.image3 != "" &&
        state.image4 != null &&
        state.image4 != "" &&
        state.textValue != null &&
        state.textValue != "") {
      var videoQualityOptions = ['480p', '720p', '1080p'];
      var selectedQuality = videoQualityOptions[state.videoQualityIndex ?? 0];

      var textValue =
          "${state.textValue} --rs $selectedQuality --rt 16:9 --dur ${state.videoDurationData[state.videoDurationIndex ?? 0]}";
      SmartDialog.showLoading(msg: "上传中...");
      var result = await VideoGenerationService.referenceImageToVideo(
        state.image!,
        state.image2!,
        state.image3!,
        state.image4!,
        textValue,
      );
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        // 上传完成
        debugPrint('图片转视频成功${result.data}');
        SmartDialog.showToast("上传成功");
      } else {
        // 上传失败
        // debugPrint('图片转视频失败${result.error}');
        SmartDialog.showToast("上传失败，请重试");
      }
    } else {
      debugPrint('图片或文本不能为空');
    }
  }
}

class CreativeEffectsData {
  List<VideoListCaseData>? caseData;
  int selectedIndex;

  CreativeEffectsData({
    this.caseData = const [],
    this.selectedIndex = 0,
  });

  CreativeEffectsData copyWith({
    List<VideoListCaseData>? caseData,
    int? selectedIndex,
  }) {
    return CreativeEffectsData(
      caseData: caseData ?? this.caseData,
      selectedIndex: selectedIndex ?? this.selectedIndex,
    );
  }
}

@riverpod
class CreativeEffects extends _$CreativeEffects {
  @override
  CreativeEffectsData build() {
    state = CreativeEffectsData();
    videoListCase();
    return state;
  }

  videoListCase() async {
    var result = await VideoGenerationService.videoListCase();
    if (result.status == Status.completed) {
      // 上传完成
      state = state.copyWith(
        caseData: result.data,
      );
      debugPrint('视频特效案例获取成功${result.data}');
    } else {
      // 上传失败
      // debugPrint('视频特效案例获取失败${result}');
    }
  }

  selectCase(int index) {
    state = state.copyWith(selectedIndex: index);
  }
}
