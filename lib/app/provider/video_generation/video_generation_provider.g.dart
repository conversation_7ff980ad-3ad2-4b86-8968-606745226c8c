// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_generation_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$videoGenerationHash() => r'952212ba101b9b44832df0ac6a4dcc99a5c46ec4';

/// See also [VideoGeneration].
@ProviderFor(VideoGeneration)
final videoGenerationProvider =
    AutoDisposeNotifierProvider<VideoGeneration, VideoGenerationData>.internal(
  VideoGeneration.new,
  name: r'videoGenerationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$videoGenerationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$VideoGeneration = AutoDisposeNotifier<VideoGenerationData>;
String _$creativeEffectsHash() => r'7433caeed8dd916b0dda11d6fca014b7cdfb8c72';

/// See also [CreativeEffects].
@ProviderFor(CreativeEffects)
final creativeEffectsProvider =
    AutoDisposeNotifierProvider<CreativeEffects, CreativeEffectsData>.internal(
  CreativeEffects.new,
  name: r'creativeEffectsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$creativeEffectsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CreativeEffects = AutoDisposeNotifier<CreativeEffectsData>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
