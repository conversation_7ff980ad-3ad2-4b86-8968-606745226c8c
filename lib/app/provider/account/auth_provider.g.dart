// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authHash() => r'7f2cd2b648847dfeade133a62aafc2f0667a115e';

/// Copyright (C), 2021-2023, <PERSON><PERSON> Lee
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: auth_provider
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/5 17:02
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/5 17:02
/// @UpdateRemark: 更新说明
///
/// Copied from [Auth].
@ProviderFor(Auth)
final authProvider = AutoDisposeNotifierProvider<Auth, Account?>.internal(
  Auth.new,
  name: r'authProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$authHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Auth = AutoDisposeNotifier<Account?>;
String _$userInfoHash() => r'9fe49004bd570651165e465cf03bd29c641001a8';

/// See also [UserInfo].
@ProviderFor(UserInfo)
final userInfoProvider =
    AutoDisposeNotifierProvider<UserInfo, AccountDetail?>.internal(
  UserInfo.new,
  name: r'userInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserInfo = AutoDisposeNotifier<AccountDetail?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
