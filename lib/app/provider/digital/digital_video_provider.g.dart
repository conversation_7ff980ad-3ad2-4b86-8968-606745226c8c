// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'digital_video_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$publicSelectorDigitalHash() =>
    r'5733406478a352a22179f6f71908580d0ea2e618';

/// See also [PublicSelectorDigital].
@ProviderFor(PublicSelectorDigital)
final publicSelectorDigitalProvider = AutoDisposeNotifierProvider<
    PublicSelectorDigital, PublicDigitalHuman?>.internal(
  PublicSelectorDigital.new,
  name: r'publicSelectorDigitalProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$publicSelectorDigitalHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PublicSelectorDigital = AutoDisposeNotifier<PublicDigitalHuman?>;
String _$publicDigitalVideoNameHash() =>
    r'2d53b4fd28164fac42da4306f542fd1ba89994ed';

/// See also [PublicDigitalVideoName].
@ProviderFor(PublicDigitalVideoName)
final publicDigitalVideoNameProvider =
    AutoDisposeNotifierProvider<PublicDigitalVideoName, String?>.internal(
  PublicDigitalVideoName.new,
  name: r'publicDigitalVideoNameProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$publicDigitalVideoNameHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PublicDigitalVideoName = AutoDisposeNotifier<String?>;
String _$publicDigitalVideoContentHash() =>
    r'c54189e601317385dc2802e6550c65b0c86ab325';

/// See also [PublicDigitalVideoContent].
@ProviderFor(PublicDigitalVideoContent)
final publicDigitalVideoContentProvider =
    AutoDisposeNotifierProvider<PublicDigitalVideoContent, String?>.internal(
  PublicDigitalVideoContent.new,
  name: r'publicDigitalVideoContentProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$publicDigitalVideoContentHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PublicDigitalVideoContent = AutoDisposeNotifier<String?>;
String _$digitalMakeVideoHash() => r'b584173f933286e5cbae1212c057d221b68ca660';

/// See also [DigitalMakeVideo].
@ProviderFor(DigitalMakeVideo)
final digitalMakeVideoProvider =
    AutoDisposeNotifierProvider<DigitalMakeVideo, void>.internal(
  DigitalMakeVideo.new,
  name: r'digitalMakeVideoProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$digitalMakeVideoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DigitalMakeVideo = AutoDisposeNotifier<void>;
String _$customizeVideoRecordHash() =>
    r'aa2ff7ee4d010032af98bc4577c37e53948f9fe7';

/// See also [CustomizeVideoRecord].
@ProviderFor(CustomizeVideoRecord)
final customizeVideoRecordProvider = AutoDisposeNotifierProvider<
    CustomizeVideoRecord, CustomizeVideoListResult>.internal(
  CustomizeVideoRecord.new,
  name: r'customizeVideoRecordProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$customizeVideoRecordHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CustomizeVideoRecord = AutoDisposeNotifier<CustomizeVideoListResult>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
