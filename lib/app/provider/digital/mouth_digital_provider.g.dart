// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mouth_digital_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mouthDigitalUploadHash() =>
    r'814a53bab4ceeb905c16bc699461941913c0967d';

/// 上传视频状态
///
/// Copied from [MouthDigitalUpload].
@ProviderFor(MouthDigitalUpload)
final mouthDigitalUploadProvider =
    AutoDisposeNotifierProvider<MouthDigitalUpload, Modification>.internal(
  MouthDigitalUpload.new,
  name: r'mouthDigitalUploadProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mouthDigitalUploadHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MouthDigitalUpload = AutoDisposeNotifier<Modification>;
String _$mouthDigitalScriptHash() =>
    r'e198b188ef9435d03145f0cda74cfe0780012ddd';

/// 口播文案
///
/// Copied from [MouthDigitalScript].
@ProviderFor(MouthDigitalScript)
final mouthDigitalScriptProvider = AutoDisposeNotifierProvider<
    MouthDigitalScript, TextEditingController>.internal(
  MouthDigitalScript.new,
  name: r'mouthDigitalScriptProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mouthDigitalScriptHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$MouthDigitalScript = AutoDisposeNotifier<TextEditingController>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
