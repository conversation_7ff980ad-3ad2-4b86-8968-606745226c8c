import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/service/digital_service.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
part 'mouth_digital_provider.g.dart';

/// 上传视频状态
@riverpod
class MouthDigitalUpload extends _$MouthDigitalUpload {
  final ImagePicker _picker = ImagePicker();

  @override
  Modification build() {
    return const Modification();
  }

  Future<void> selectVideo({ImageSource source = ImageSource.gallery}) async {
    final XFile? video = await _picker.pickVideo(source: source);
    if (video == null) {
      return;
    }
    // 支持格式mp4,mov,WebM
    // if (!video.path.endsWith(".mp4") &&
    //     !video.path.endsWith(".mov") &&
    //     !video.path.endsWith(".WebM")) {
    //   ToastUtil.showToast("只支持mp4,mov,WebM格式");
    //   return;
    // }
    // // 大小2G以下
    // var file = File(video.path);
    // if (file.lengthSync() > 2 * 1024 * 1024 * 1024) {
    //   ToastUtil.showToast("视频大小不能超过2G");
    //   return;
    // }
    // // 30秒到120秒范围
    // if (file.lengthSync() < 30 * 1000 || file.lengthSync() > 120 * 1000) {
    //   ToastUtil.showToast("视频时长不符合要求");
    //   return;
    // }

    // 标记上传开始
    state = state.copyWith(
      state: 1,
      localUrl: video.path,
      remoteUrl: null,
    );
    var result = await TextToVideoService.repairUploadFile(video.path);
    if (result.status == Status.completed) {
      state = state.copyWith(
        state: 2,
        localUrl: video.path,
        remoteUrl: result.data,
      );
      debugPrint("remoteUrl: ${result.data}");
    } else {
      debugPrint("上传失败: ${result.exception!.getMessage()}");
      state = state.copyWith(state: 3);
    }
  }

  void clean() {
    state = const Modification();
  }
}

/// 口播文案
@riverpod
class MouthDigitalScript extends _$MouthDigitalScript {
  final TextEditingController textEditingController = TextEditingController();

  @override
  TextEditingController build() {
    ref.onDispose(() => textEditingController.dispose());
    return textEditingController;
  }

  void clean() {
    textEditingController.clear();
  }

  void generateVideo(String workName) async {
    Modification video = ref.read(mouthDigitalUploadProvider);
    if (video.remoteUrl == null) {
      ToastUtil.showToast("请先上传视频");
      return;
    }
    var script = state.text;
    if (script.isEmpty) {
      ToastUtil.showToast("请先生成口播文案");
      return;
    }
    if (workName.isEmpty) {
      ToastUtil.showToast("请输入作品名称");
      return;
    }
    SmartDialog.showLoading(msg: "提交中...");
    var result = await DigitalService.customizeDigitalHuman(
      workName,
      script,
      video.remoteUrl!,
    );
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      clean();
      ref.read(mouthDigitalUploadProvider.notifier).clean();
      navigatorKey.currentContext?.push("/$workRecordPage");
    } else {
      debugPrint("生成失败: ${result.exception!.getMessage()}");
    }
  }
}
