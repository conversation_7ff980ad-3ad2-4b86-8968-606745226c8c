// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentAppleProductHash() =>
    r'feb0d9ccfd48367c132b4604708c3fdcf83e60e9';

/// See also [CurrentAppleProduct].
@ProviderFor(CurrentAppleProduct)
final currentAppleProductProvider = AutoDisposeNotifierProvider<
    CurrentAppleProduct, CustomAppleProduct?>.internal(
  CurrentAppleProduct.new,
  name: r'currentAppleProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAppleProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentAppleProduct = AutoDisposeNotifier<CustomAppleProduct?>;
String _$zenAiConsentAgreementHash() =>
    r'980c521ee16d6fa4d1d5bb80ff9e9b20e7677f64';

/// See also [ZenAiConsentAgreement].
@ProviderFor(ZenAiConsentAgreement)
final zenAiConsentAgreementProvider =
    AutoDisposeNotifierProvider<ZenAiConsentAgreement, bool>.internal(
  ZenAiConsentAgreement.new,
  name: r'zenAiConsentAgreementProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiConsentAgreementHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiConsentAgreement = AutoDisposeNotifier<bool>;
String _$currentAppleProductPowerHash() =>
    r'80afe0fdb4afea3625f2a2b1391c1f117bac95a0';

/// See also [CurrentAppleProductPower].
@ProviderFor(CurrentAppleProductPower)
final currentAppleProductPowerProvider = AutoDisposeNotifierProvider<
    CurrentAppleProductPower, CustomAppleProduct?>.internal(
  CurrentAppleProductPower.new,
  name: r'currentAppleProductPowerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAppleProductPowerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentAppleProductPower = AutoDisposeNotifier<CustomAppleProduct?>;
String _$zenAiPowerAgreementHash() =>
    r'd00ecbef46289ee0ca0b9db3b83a0c1b03e3f439';

/// See also [ZenAiPowerAgreement].
@ProviderFor(ZenAiPowerAgreement)
final zenAiPowerAgreementProvider =
    AutoDisposeNotifierProvider<ZenAiPowerAgreement, bool>.internal(
  ZenAiPowerAgreement.new,
  name: r'zenAiPowerAgreementProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiPowerAgreementHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiPowerAgreement = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
