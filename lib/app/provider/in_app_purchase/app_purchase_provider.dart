import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/order/package.dart';

part 'app_purchase_provider.g.dart';

// 会员订阅产品
enum ApplePayProduct {
  weeklyMember(1, "WeeklyMember"),
  monthlyMember(2, "MonthlyMember"),
  yearMember(3, "YearMember");

  const ApplePayProduct(this.id, this.productId);

  final int id;
  final String productId;
}

// 灵感值充值产品
enum ApplePayPowerProduct {
  linggan1000(1, "linggan1000"),
  linggan1880(2, "linggan1880"),
  linggan3680(3, "linggan3680");

  const ApplePayPowerProduct(this.id, this.productId);

  final int id;
  final String productId;
}

class CustomAppleProduct {
  String productId;
  Package? package;
  ProductDetails? details;

  CustomAppleProduct(this.productId, this.package, this.details);
}

// 当前选择的会员产品
@riverpod
class CurrentAppleProduct extends _$CurrentAppleProduct {
  @override
  CustomAppleProduct? build() {
    return null;
  }

  void setCurrentProduct(CustomAppleProduct product) {
    debugPrint("setCurrentProduct: ${product.productId}");
    state = product;
  }
}

// 是否同意会员订阅协议
@riverpod
class ZenAiConsentAgreement extends _$ZenAiConsentAgreement {
  @override
  bool build() {
    return false;
  }

  void setAgreement(bool value) {
    state = value;
  }
}

// 当前选中的算力充值产品
@riverpod
class CurrentAppleProductPower extends _$CurrentAppleProductPower {
  @override
  CustomAppleProduct? build() {
    return null;
  }

  void setCurrentProduct(CustomAppleProduct product) {
    debugPrint("setCurrentProduct: ${product.productId}");
    state = product;
  }
}

// 是否同意算力购买协议
@riverpod
class ZenAiPowerAgreement extends _$ZenAiPowerAgreement {
  @override
  bool build() {
    return false;
  }

  void setAgreement(bool value) {
    state = value;
  }
}
