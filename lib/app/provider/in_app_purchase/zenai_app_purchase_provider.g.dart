// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'zenai_app_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$zenAiAppPurchaseProductHash() =>
    r'998629171244cac308ac3ba6868ec3552fe7b128';

/// See also [ZenAiAppPurchaseProduct].
@ProviderFor(ZenAiAppPurchaseProduct)
final zenAiAppPurchaseProductProvider = AutoDisposeNotifierProvider<
    ZenAiAppPurchaseProduct, ZenAiPurchaseData>.internal(
  ZenAiAppPurchaseProduct.new,
  name: r'zenAiAppPurchaseProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchaseProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchaseProduct = AutoDisposeNotifier<ZenAiPurchaseData>;
String _$zenAiAppPurchasePowerProductHash() =>
    r'b76bb33af82550579c6ed592ba6df9782d92471a';

/// See also [ZenAiAppPurchasePowerProduct].
@ProviderFor(ZenAiAppPurchasePowerProduct)
final zenAiAppPurchasePowerProductProvider = AutoDisposeNotifierProvider<
    ZenAiAppPurchasePowerProduct, List<CustomAppleProduct>?>.internal(
  ZenAiAppPurchasePowerProduct.new,
  name: r'zenAiAppPurchasePowerProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchasePowerProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchasePowerProduct
    = AutoDisposeNotifier<List<CustomAppleProduct>?>;
String _$zenAiAppPurchaseHash() => r'dd632c39878a7125a3fee1d7b00ee756fa1abf74';

/// See also [ZenAiAppPurchase].
@ProviderFor(ZenAiAppPurchase)
final zenAiAppPurchaseProvider =
    AutoDisposeNotifierProvider<ZenAiAppPurchase, void>.internal(
  ZenAiAppPurchase.new,
  name: r'zenAiAppPurchaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchase = AutoDisposeNotifier<void>;
String _$zenAiConsentAgreementHash() =>
    r'980c521ee16d6fa4d1d5bb80ff9e9b20e7677f64';

/// See also [ZenAiConsentAgreement].
@ProviderFor(ZenAiConsentAgreement)
final zenAiConsentAgreementProvider =
    AutoDisposeNotifierProvider<ZenAiConsentAgreement, bool>.internal(
  ZenAiConsentAgreement.new,
  name: r'zenAiConsentAgreementProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiConsentAgreementHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiConsentAgreement = AutoDisposeNotifier<bool>;
String _$zenAiPowerAgreementHash() =>
    r'd00ecbef46289ee0ca0b9db3b83a0c1b03e3f439';

/// See also [ZenAiPowerAgreement].
@ProviderFor(ZenAiPowerAgreement)
final zenAiPowerAgreementProvider =
    AutoDisposeNotifierProvider<ZenAiPowerAgreement, bool>.internal(
  ZenAiPowerAgreement.new,
  name: r'zenAiPowerAgreementProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiPowerAgreementHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiPowerAgreement = AutoDisposeNotifier<bool>;
String _$currentAppleProductPowerHash() =>
    r'80afe0fdb4afea3625f2a2b1391c1f117bac95a0';

/// See also [CurrentAppleProductPower].
@ProviderFor(CurrentAppleProductPower)
final currentAppleProductPowerProvider = AutoDisposeNotifierProvider<
    CurrentAppleProductPower, CustomAppleProduct?>.internal(
  CurrentAppleProductPower.new,
  name: r'currentAppleProductPowerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentAppleProductPowerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CurrentAppleProductPower = AutoDisposeNotifier<CustomAppleProduct?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
