// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'zenai_app_purchase_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$zenAiAppPurchaseProductHash() =>
    r'998629171244cac308ac3ba6868ec3552fe7b128';

/// See also [ZenAiAppPurchaseProduct].
@ProviderFor(ZenAiAppPurchaseProduct)
final zenAiAppPurchaseProductProvider = AutoDisposeNotifierProvider<
    ZenAiAppPurchaseProduct, ZenAiPurchaseData>.internal(
  ZenAiAppPurchaseProduct.new,
  name: r'zenAiAppPurchaseProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchaseProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchaseProduct = AutoDisposeNotifier<ZenAiPurchaseData>;
String _$zenAiAppPurchasePowerProductHash() =>
    r'b76bb33af82550579c6ed592ba6df9782d92471a';

/// See also [ZenAiAppPurchasePowerProduct].
@ProviderFor(ZenAiAppPurchasePowerProduct)
final zenAiAppPurchasePowerProductProvider = AutoDisposeNotifierProvider<
    ZenAiAppPurchasePowerProduct, List<CustomAppleProduct>?>.internal(
  ZenAiAppPurchasePowerProduct.new,
  name: r'zenAiAppPurchasePowerProductProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchasePowerProductHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchasePowerProduct
    = AutoDisposeNotifier<List<CustomAppleProduct>?>;
String _$zenAiAppPurchaseHash() => r'486eac76d78b6d17f50afa8e5e51f2e103aef40f';

/// See also [ZenAiAppPurchase].
@ProviderFor(ZenAiAppPurchase)
final zenAiAppPurchaseProvider =
    AutoDisposeNotifierProvider<ZenAiAppPurchase, void>.internal(
  ZenAiAppPurchase.new,
  name: r'zenAiAppPurchaseProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$zenAiAppPurchaseHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ZenAiAppPurchase = AutoDisposeNotifier<void>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
