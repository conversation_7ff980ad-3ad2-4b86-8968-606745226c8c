import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../config/config.dart';
import '../../../utils/platform_util.dart';
import '../../../utils/toast_util.dart';
import '../../navigation/router.dart';
import '../../repository/service/member_service.dart';
import '../account/auth_provider.dart';
import '../member/compute_power_provider.dart';
import '../member/member_provider.dart';
import 'app_purchase_provider.dart';

part 'zenai_app_purchase_provider.g.dart';

class ZenAiPurchaseData {
  final int status; // 0:加载中，1:加载成功，2:加载失败
  final List<CustomAppleProduct> products;
  final String? errMsg; // 加载失败时的文本

  const ZenAiPurchaseData({
    this.status = 0,
    this.products = const [],
    this.errMsg,
  });

  ZenAiPurchaseData copyWith({
    int? status,
    List<CustomAppleProduct>? products,
    String? errMsg,
  }) {
    return ZenAiPurchaseData(
      status: status ?? this.status,
      products: products ?? this.products,
      errMsg: errMsg ?? this.errMsg,
    );
  }
}

// 会员订阅产品
@riverpod
class ZenAiAppPurchaseProduct extends _$ZenAiAppPurchaseProduct {
  @override
  ZenAiPurchaseData build() {
    loadAllProducts();
    return const ZenAiPurchaseData();
  }

  // 加载所有可购买产品
  Future<List<CustomAppleProduct>?> loadAllProducts() async {
    // 服务器配置的包
    state = const ZenAiPurchaseData();
    var serverPackagesResult = await MemberService.getAllPackage();
    var serverPackagesData = serverPackagesResult.data;
    if (serverPackagesData == null || (serverPackagesData.isEmpty)) {
      state = state.copyWith(status: 2, errMsg: "没有可购买商品");
      return null;
    }

    // 内购是否可用
    final bool available = await InAppPurchase.instance.isAvailable();
    if (!available) {
      state = state.copyWith(status: 2, errMsg: "无法连接到商店");
      return null;
    }

    // if (PlatformUtils.isIOS) {
    //   final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
    //   _inAppPurchase
    //       .getPlatformAddition();
    //   await iosPlatformAddition.setDelegate(ExamplePaymentQueueDelegate());
    // }

    // 根据服务器上的数据获取产品ID列表
    // 以下获取产品ID需要替换服务器返回的
    List<String> productIds =
        serverPackagesData.map((e) => e.appleProductId ?? "").toList();
    debugPrint("loadAllProducts: ${productIds.toSet()}");
    // 请求苹果服务器产品列表
    final ProductDetailsResponse response =
        await InAppPurchase.instance.queryProductDetails(productIds.toSet());

    if (response.error != null) {
      debugPrint("loadAllProducts-err: ${response.error}");
      debugPrint("loadAllProducts-err: ${response.error?.message}");
      state = state.copyWith(status: 2, errMsg: "商品加载失败");
      return null;
    }

    debugPrint("notFoundIDs: ${response.notFoundIDs}");
    // if (response.notFoundIDs.isNotEmpty) {
    //   ToastUtil.showToast("没有找到商品");
    //   SmartDialog.dismiss();
    //   return null;
    // }

    // 商品列表
    List<ProductDetails> products = response.productDetails;
    if (products.isEmpty) {
      state = state.copyWith(status: 2, errMsg: "没有可购买商品");
      return null;
    }

    List<CustomAppleProduct> newList = [];
    for (var i = 0; i < serverPackagesData.length; i++) {
      var detail = serverPackagesData[i];
      var package = products.firstWhere((e) => e.id == detail.appleProductId);
      newList.add(CustomAppleProduct(package.id, detail, package));
    }

    state = state.copyWith(status: 1, products: newList);

    // 默认选中第一个产品
    ref
        .watch(currentAppleProductProvider.notifier)
        .setCurrentProduct(newList.first);

    return newList;
  }
}

// 灵感值充值产品
@riverpod
class ZenAiAppPurchasePowerProduct extends _$ZenAiAppPurchasePowerProduct {
  @override
  List<CustomAppleProduct>? build() {
    return null;
  }

  // 加载所有可购买产品
  Future<List<CustomAppleProduct>?> loadAllProducts() async {
    SmartDialog.showLoading(msg: "加载中...");

    // 服务器配置的包
    var serverPackagesResult = await MemberService.getAllPowerPackage();
    var serverPackagesData = serverPackagesResult.data;
    if (serverPackagesData == null || (serverPackagesData.isEmpty)) {
      SmartDialog.dismiss();
      ToastUtil.showToast("没有可购买商品");
      return null;
    }

    // 内购是否可用
    final bool available = await InAppPurchase.instance.isAvailable();
    if (!available) {
      SmartDialog.dismiss();
      ToastUtil.showToast("无法连接到商店");
      return null;
    }

    // if (PlatformUtils.isIOS) {
    //   final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
    //   _inAppPurchase
    //       .getPlatformAddition();
    //   await iosPlatformAddition.setDelegate(ExamplePaymentQueueDelegate());
    // }

    // 根据服务器上的数据获取产品ID列表
    // 以下获取产品ID需要替换服务器返回的
    List<String> productIds =
        serverPackagesData.map((e) => e.appleProductId ?? "").toList();
    debugPrint("loadAllProducts: ${productIds.toSet()}");
    // 请求苹果服务器产品列表
    final ProductDetailsResponse response =
        await InAppPurchase.instance.queryProductDetails(productIds.toSet());

    if (response.error != null) {
      debugPrint("loadAllProducts-err: ${response.error}");
      debugPrint("loadAllProducts-err: ${response.error?.message}");
      SmartDialog.dismiss();
      ToastUtil.showToast("商品加载失败");
      return null;
    }

    debugPrint("notFoundIDs: ${response.notFoundIDs}");
    // if (response.notFoundIDs.isNotEmpty) {
    //   ToastUtil.showToast("没有找到商品");
    //   SmartDialog.dismiss();
    //   return null;
    // }

    // 商品列表
    List<ProductDetails> products = response.productDetails;
    if (products.isEmpty) {
      SmartDialog.dismiss();
      ToastUtil.showToast("没有可购买商品");
      return null;
    }

    List<CustomAppleProduct> newList = [];
    for (var i = 0; i < serverPackagesData.length; i++) {
      var detail = serverPackagesData[i];
      var package = products.firstWhere((e) => e.id == detail.appleProductId);
      newList.add(CustomAppleProduct(package.id, detail, package));
    }

    SmartDialog.dismiss();

    state = newList;

    // 默认选中第一个产品
    // ref
    //     .watch(currentAppleProductProvider.notifier)
    //     .setCurrentProduct(newList.first);

    return newList;
  }
}

@riverpod
class ZenAiAppPurchase extends _$ZenAiAppPurchase {
  @override
  void build() {
    return;
  }

  void initInAppPurchase() async {
    StreamSubscription<List<PurchaseDetails>>? subscription;

    // 监听
    final Stream<List<PurchaseDetails>> purchaseUpdated =
        InAppPurchase.instance.purchaseStream;
    subscription = purchaseUpdated.listen(
      (List<PurchaseDetails> purchaseDetailsList) {
        _listenToPurchaseUpdated(purchaseDetailsList);
      },
      onDone: () {
        subscription?.cancel();
      },
      onError: (error) {
        ToastUtil.showToast("购买失败");
      },
    );

    // resumePurchase();
  }

  // 恢复购买
  void resumePurchase() {
    InAppPurchase.instance.restorePurchases();
  }

  // 购买状态更新
  void _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    for (final PurchaseDetails purchase in purchaseDetailsList) {
      if (purchase.status == PurchaseStatus.pending) {
        // 等待支付完成
        _handlePending();
      } else if (purchase.status == PurchaseStatus.canceled) {
        // 取消支付
        _handleCancel(purchase);
      } else if (purchase.status == PurchaseStatus.error) {
        // 购买失败
        _handleError(purchase);
      } else if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        debugPrint('purchase.status: ${purchase.status}');
        // 完成购买，到服务器验证
        if (PlatformUtils.isIOS) {
          var applePurchase = purchase as AppStorePurchaseDetails;
          checkApplePayInfo(
            applePurchase,
            needCancelDialog: purchase.status == PurchaseStatus.purchased,
          );
        }
      }
    }
  }

  // pending
  void _handlePending() {
    debugPrint("apple pay: 支付中");
  }

  // canceled
  void _handleCancel(PurchaseDetails purchase) {
    debugPrint("apple pay: 取消支付");
    SmartDialog.dismiss();
    InAppPurchase.instance.completePurchase(purchase);
  }

  // error
  void _handleError(PurchaseDetails purchase) {
    SmartDialog.dismiss();
    var err = purchase.error;
    debugPrint("apple pay: 支付失败-${err?.code},${err?.message}, ${err?.details}");
    InAppPurchase.instance.completePurchase(purchase);
  }

  // apple支付验证
  void checkApplePayInfo(
    AppStorePurchaseDetails appstoreDetail, {
    bool needCancelDialog = true,
  }) async {
    var userData = ref.read(authProvider);
    if (userData == null) return;

    // 通过appstoreDetail判断是会员订阅还是算力充值
    var memberSub = ApplePayProduct.values
        .any((e) => e.productId == appstoreDetail.productID);
    if (memberSub) {
      // 会员订阅
      _memberSubCheck(appstoreDetail, needCancelDialog);
    } else {
      // 算力充值
      _powerSubCheck(appstoreDetail, needCancelDialog);
    }
  }

  // 会员订阅支付验证
  void _memberSubCheck(
    AppStorePurchaseDetails appstoreDetail,
    bool needCancelDialog,
  ) async {
    // 创建订单
    SmartDialog.showLoading(msg: "加载中...");
    var customAppleProduct = ref.read(currentAppleProductProvider);
    var subType = customAppleProduct?.package?.memberType;
    var orderRes = await MemberService.buyPackage(
      1,
      3,
      subType,
    );
    if (orderRes.status == Status.completed) {
      // 服务器验证
      var result = await MemberService.applePayVerify(
        Config.appleVerifyMethod,
        orderRes.data?.outTradeNo,
        appstoreDetail.verificationData.serverVerificationData,
      );
      if (result.status == Status.completed) {
        debugPrint("pay status: ${result.data?.state}");
        if (result.data?.state == 1) {
          // 支付验证完成，发放会员资格
          debugPrint("验证完成，请发放会员资格");
          if (needCancelDialog &&
              navigatorKey.currentContext?.canPop() == true) {
            navigatorKey.currentContext?.pop();
          }
          ToastUtil.showToast("会员开通成功", state: true);
          // 刷新用户会员信息
          ref.read(memberInfoProvider.notifier).getMember();
          // 刷新算力值
          ref.read(userPowerBalanceProvider.notifier).loadData();
        } else {
          ToastUtil.showToast("购买失败");
        }
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    } else {
      ToastUtil.showToast(orderRes.exception!.getMessage());
    }
    SmartDialog.dismiss();

    // 完成
    await InAppPurchase.instance.completePurchase(appstoreDetail);
  }

  // 算力充值验证
  void _powerSubCheck(
    AppStorePurchaseDetails appstoreDetail,
    bool needCancelDialog,
  ) async {
    // 创建订单
    SmartDialog.showLoading(msg: "加载中...");
    var customAppleProduct = ref.read(currentAppleProductPowerProvider);
    var subType = customAppleProduct?.package?.additionType;
    var orderRes = await MemberService.buyPackage(
      2,
      3,
      subType,
    );
    if (orderRes.status == Status.completed) {
      // 服务器验证
      var result = await MemberService.applePayPowerVerify(
        Config.appleVerifyMethod,
        orderRes.data?.outTradeNo,
        appstoreDetail.verificationData.serverVerificationData,
      );
      if (result.status == Status.completed) {
        debugPrint("pay status: ${result.data?.state}");
        if (result.data?.state == 1) {
          // 支付验证完成，发放会员资格
          debugPrint("验证完成，发放算力值");
          if (needCancelDialog &&
              navigatorKey.currentContext?.canPop() == true) {
            navigatorKey.currentContext?.pop();
          }
          ToastUtil.showToast(
            "灵感值+${customAppleProduct?.package?.powerNum}点",
            state: true,
          );
          // 刷新算力值
          ref.read(userPowerBalanceProvider.notifier).loadData();
          // 刷新算力值明细
          ref.read(powerRecordProvider.notifier).loadData();
        } else {
          ToastUtil.showToast("购买失败");
        }
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    } else {
      ToastUtil.showToast(orderRes.exception!.getMessage());
    }
    SmartDialog.dismiss();

    // 完成
    await InAppPurchase.instance.completePurchase(appstoreDetail);
  }

  // 购买指定产品
  void purchaseById() async {
    var customAppleProduct = ref.read(currentAppleProductProvider);
    if (customAppleProduct?.details != null) {
      try {
        // 拉起支付
        SmartDialog.showLoading(msg: "支付中...");
        var payResult = await InAppPurchase.instance.buyNonConsumable(
          purchaseParam:
              PurchaseParam(productDetails: customAppleProduct!.details!),
        );
        debugPrint("purchaseById: $payResult");
        if (payResult) {}
      } catch (e) {
        SmartDialog.dismiss();
        debugPrint("apple pay: 购买失败-${e.toString()}");
      }
    } else {
      ToastUtil.showToast("请选择购买产品");
    }
  }

  // 购买算力充值产品
  void purchasePowerById() async {
    try {
      var customAppleProduct = ref.read(currentAppleProductPowerProvider);
      if (customAppleProduct?.details != null) {
        // 拉起支付
        SmartDialog.showLoading(msg: "支付中...");
        var payResult = await InAppPurchase.instance.buyConsumable(
          purchaseParam:
              PurchaseParam(productDetails: customAppleProduct!.details!),
        );
        debugPrint("purchaseById: $payResult");
        if (payResult) {}
      } else {
        ToastUtil.showToast("请选择购买产品");
      }
    } catch (e) {
      SmartDialog.dismiss();
      debugPrint("apple pay: 购买失败-${e.toString()}");
    }
  }
}

// 是否同意会员订阅协议
@riverpod
class ZenAiConsentAgreement extends _$ZenAiConsentAgreement {
  @override
  bool build() {
    return false;
  }

  void setAgreement(bool value) {
    state = value;
  }
}

// 是否同意算力购买协议
@riverpod
class ZenAiPowerAgreement extends _$ZenAiPowerAgreement {
  @override
  bool build() {
    return false;
  }

  void setAgreement(bool value) {
    state = value;
  }
}

// 当前选中的算力充值产品
@riverpod
class CurrentAppleProductPower extends _$CurrentAppleProductPower {
  @override
  CustomAppleProduct? build() {
    return null;
  }

  void setCurrentProduct(CustomAppleProduct product) {
    debugPrint("setCurrentProduct: ${product.productId}");
    state = product;
  }
}
