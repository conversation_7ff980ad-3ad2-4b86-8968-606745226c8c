// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cartoon_digimon_audio_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cartoonDigimonSelectedAudioHash() =>
    r'4ee5d36a876ec1dd39fcb99ee688ee3d5aaac4c2';

/// 当前选中音频的状态管理Provider
/// 用于管理用户在音频列表中选中的音频项
///
/// Copied from [CartoonDigimonSelectedAudio].
@ProviderFor(CartoonDigimonSelectedAudio)
final cartoonDigimonSelectedAudioProvider = AutoDisposeNotifierProvider<
    CartoonDigimonSelectedAudio, AudioItem?>.internal(
  CartoonDigimonSelectedAudio.new,
  name: r'cartoonDigimonSelectedAudioProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$cartoonDigimonSelectedAudioHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CartoonDigimonSelectedAudio = AutoDisposeNotifier<AudioItem?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
