import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/poster_design_data.dart';
import 'package:text_generation_video/app/repository/modals/record/work_record.dart';
import 'package:text_generation_video/app/repository/service/poster_design_service.dart';
import 'package:text_generation_video/app/widgets/refresh/refresh_container.dart';

part 'figure_packaging_provider.g.dart';

class FigurePackagingDatas {
  String? inputValue;
  String? imageUrl;
  List<PosterDesignData> posterCaseList;

  FigurePackagingDatas({
    this.inputValue,
    this.imageUrl,
    this.posterCaseList = const [],
  });

  FigurePackagingDatas copyWith({
    String? inputValue,
    String? imageUrl,
    List<PosterDesignData>? posterCaseList,
  }) {
    return FigurePackagingDatas(
      inputValue: inputValue ?? this.inputValue,
      imageUrl: imageUrl ?? this.imageUrl,
      posterCaseList: posterCaseList ?? this.posterCaseList,
    );
  }
}

@riverpod
class FigurePackaging extends _$FigurePackaging {
  @override
  FigurePackagingDatas build() {
    state = FigurePackagingDatas();
    posterCaseList();
    return state;
  }

  posterCaseList() async {
    var result = await PosterDesignService.posterCaseList(15);
    debugPrint('怎么回事？$result');
    if (result.status == Status.completed) {
      state = state.copyWith(posterCaseList: result.data?.list ?? []);
    }
  }

  // 输入框的值变化时，更新state中的inputValue值
  getInputValue(String? value) {
    state = state.copyWith(inputValue: value);
  }

  /// 获取用户上传的图片
  getImageUrl(String? imageUrl) {
    state = state.copyWith(imageUrl: imageUrl);
  }

  posterDesign(String proportion) async {
    List size;
    switch (proportion) {
      case '1:1':
        size = [1328, 1328];
        break;
      case '4:3':
        size = [1472, 1104];
        break;
      case '3:2':
        size = [1584, 1056];
        break;
      case '16:9':
        size = [1664, 936];
        break;
      case '21:9':
        size = [2016, 864];
        break;
      default:
        size = [1328, 1328];
        break;
    }

    if (state.inputValue == null || state.inputValue == "") {
      return;
    }

    if (state.imageUrl == null || state.imageUrl == "") {
      return;
    }

    var params = {
      "prompt": state.inputValue,
      "imgUrl": state.imageUrl,
      "width": size[0],
      "height": size[1],
    };

    SmartDialog.showLoading(msg: "上传中...");
    var result = await PosterDesignService.imgPackaging(params);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      state = state.copyWith(inputValue: "", imageUrl: "");
      ref.read(packagRecordListProvider.notifier).loadRecord();
      SmartDialog.showToast("上传成功");
    } else {
      SmartDialog.showToast("上传失败，请重试");
    }
  }
}

class PackagRecordData {
  final int pageNum;
  final int pageSize;
  final List<WorkRecord>? records;
  final LoadState? loadState;

  const PackagRecordData({
    this.pageNum = 1,
    this.pageSize = 20,
    this.records,
    this.loadState = LoadState.idle,
  });

  PackagRecordData copyWith({
    int? page,
    int? size,
    List<WorkRecord>? records,
    LoadState? loadState,
  }) {
    return PackagRecordData(
      pageNum: page ?? pageNum,
      pageSize: size ?? pageSize,
      records: records ?? this.records,
      loadState: loadState ?? this.loadState,
    );
  }
}

@riverpod
class PackagRecordList extends _$PackagRecordList {
  @override
  PackagRecordData? build() {
    loadRecord();
    return null;
  }

  deleteRecord(List<int> idArray) async {
    SmartDialog.showLoading(msg: "删除中...");
    var result = await PosterDesignService.deleteRecord(idArray);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      SmartDialog.showToast("删除成功");
      loadRecord();
    }
  }

  /// 加载数据
  void loadRecord() async {
    state = const PackagRecordData(loadState: LoadState.loading);
    var result = await PosterDesignService.aiWorkRecordList(
      15,
      state!.pageNum,
      state!.pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null && result.data!.list != null) {
        state = PackagRecordData(
          records: result.data?.list,
          loadState: result.data!.hasNextPage ?? false
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }

  /// 加载更多
  void loadMoreRecord() async {
    state = state?.copyWith(
      loadState: LoadState.loading,
      page: (state?.pageNum ?? 1) + 1,
    );
    var result = await PosterDesignService.aiWorkRecordList(
      15,
      state!.pageNum,
      state!.pageSize,
    );
    if (result.status == Status.completed) {
      if (result.data != null && result.data!.list != null) {
        state = state?.copyWith(
          records: [...?state?.records, ...?result.data?.list],
          loadState: result.data!.hasNextPage ?? false
              ? LoadState.idle
              : LoadState.noMore,
        );
      }
    }
  }
}
