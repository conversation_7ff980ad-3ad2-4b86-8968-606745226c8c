// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'figure_packaging_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$figurePackagingHash() => r'10997755a6ca2e65be04978371a3febdb12813fd';

/// See also [FigurePackaging].
@ProviderFor(FigurePackaging)
final figurePackagingProvider =
    AutoDisposeNotifierProvider<FigurePackaging, FigurePackagingDatas>.internal(
  FigurePackaging.new,
  name: r'figurePackagingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$figurePackagingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FigurePackaging = AutoDisposeNotifier<FigurePackagingDatas>;
String _$packagRecordListHash() => r'03b74631d09549d5d66bf71154d498d35107d302';

/// See also [PackagRecordList].
@ProviderFor(PackagRecordList)
final packagRecordListProvider =
    AutoDisposeNotifierProvider<PackagRecordList, PackagRecordData?>.internal(
  PackagRecordList.new,
  name: r'packagRecordListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$packagRecordListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PackagRecordList = AutoDisposeNotifier<PackagRecordData?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
