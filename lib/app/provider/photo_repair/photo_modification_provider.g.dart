// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_modification_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photoModificationCurrentHash() =>
    r'c9d14c332dadcda33b5d4fb65d5068fff21cb4ff';

/// See also [PhotoModificationCurrent].
@ProviderFor(PhotoModificationCurrent)
final photoModificationCurrentProvider = AutoDisposeNotifierProvider<
    PhotoModificationCurrent, Modification>.internal(
  PhotoModificationCurrent.new,
  name: r'photoModificationCurrentProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photoModificationCurrentHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoModificationCurrent = AutoDisposeNotifier<Modification>;
String _$modificationEditTextHash() =>
    r'501ee598f4102044aed11c703e83b628be8bd7ab';

/// See also [ModificationEditText].
@ProviderFor(ModificationEditText)
final modificationEditTextProvider = AutoDisposeNotifierProvider<
    ModificationEditText, TextEditingController>.internal(
  ModificationEditText.new,
  name: r'modificationEditTextProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$modificationEditTextHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ModificationEditText = AutoDisposeNotifier<TextEditingController>;
String _$photoModificationListHash() =>
    r'b3deaa9e28b6ae365d5f9209f66e65d7fdf70a56';

/// See also [PhotoModificationList].
@ProviderFor(PhotoModificationList)
final photoModificationListProvider = AutoDisposeNotifierProvider<
    PhotoModificationList, List<Modification>?>.internal(
  PhotoModificationList.new,
  name: r'photoModificationListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$photoModificationListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoModificationList = AutoDisposeNotifier<List<Modification>?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
