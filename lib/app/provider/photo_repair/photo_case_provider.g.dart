// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_case_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchModificationCaseHash() =>
    r'7d14d37e1944bbdb3744979a3ba44de57ad852b0';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchModificationCase].
@ProviderFor(fetchModificationCase)
const fetchModificationCaseProvider = FetchModificationCaseFamily();

/// See also [fetchModificationCase].
class FetchModificationCaseFamily
    extends Family<AsyncValue<List<PhotoModification>?>> {
  /// See also [fetchModificationCase].
  const FetchModificationCaseFamily();

  /// See also [fetchModificationCase].
  FetchModificationCaseProvider call(
    int? caseId,
  ) {
    return FetchModificationCaseProvider(
      caseId,
    );
  }

  @override
  FetchModificationCaseProvider getProviderOverride(
    covariant FetchModificationCaseProvider provider,
  ) {
    return call(
      provider.caseId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchModificationCaseProvider';
}

/// See also [fetchModificationCase].
class FetchModificationCaseProvider
    extends AutoDisposeFutureProvider<List<PhotoModification>?> {
  /// See also [fetchModificationCase].
  FetchModificationCaseProvider(
    int? caseId,
  ) : this._internal(
          (ref) => fetchModificationCase(
            ref as FetchModificationCaseRef,
            caseId,
          ),
          from: fetchModificationCaseProvider,
          name: r'fetchModificationCaseProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchModificationCaseHash,
          dependencies: FetchModificationCaseFamily._dependencies,
          allTransitiveDependencies:
              FetchModificationCaseFamily._allTransitiveDependencies,
          caseId: caseId,
        );

  FetchModificationCaseProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.caseId,
  }) : super.internal();

  final int? caseId;

  @override
  Override overrideWith(
    FutureOr<List<PhotoModification>?> Function(
            FetchModificationCaseRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchModificationCaseProvider._internal(
        (ref) => create(ref as FetchModificationCaseRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        caseId: caseId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<PhotoModification>?> createElement() {
    return _FetchModificationCaseProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchModificationCaseProvider && other.caseId == caseId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, caseId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchModificationCaseRef
    on AutoDisposeFutureProviderRef<List<PhotoModification>?> {
  /// The parameter `caseId` of this provider.
  int? get caseId;
}

class _FetchModificationCaseProviderElement
    extends AutoDisposeFutureProviderElement<List<PhotoModification>?>
    with FetchModificationCaseRef {
  _FetchModificationCaseProviderElement(super.provider);

  @override
  int? get caseId => (origin as FetchModificationCaseProvider).caseId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
