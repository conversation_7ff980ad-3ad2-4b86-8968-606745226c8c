import 'package:flutter/foundation.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_muscle.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

part 'photo_muscle_provider.g.dart';

class PhotoMuscleState {
  final List<PhotoMuscleCaseItem> maleCases;
  final List<PhotoMuscleCaseItem> femaleCases;
  final int currentGender; // 1 for male, 2 for female
  final PhotoMuscleCaseItem? selectedMaleCase;
  final PhotoMuscleCaseItem? selectedFemaleCase;

  PhotoMuscleState({
    this.maleCases = const [],
    this.femaleCases = const [],
    this.currentGender = 1,
    this.selectedMaleCase,
    this.selectedFemaleCase,
  });

  List<PhotoMuscleCaseItem> get currentCases =>
      currentGender == 1 ? maleCases : femaleCases;

  PhotoMuscleCaseItem? get currentSelectedCase =>
      currentGender == 1 ? selectedMaleCase : selectedFemaleCase;

  PhotoMuscleState copyWith({
    List<PhotoMuscleCaseItem>? maleCases,
    List<PhotoMuscleCaseItem>? femaleCases,
    int? currentGender,
    ValueGetter<PhotoMuscleCaseItem?>? selectedMaleCase,
    ValueGetter<PhotoMuscleCaseItem?>? selectedFemaleCase,
    bool? isLoading,
  }) {
    return PhotoMuscleState(
      maleCases: maleCases ?? this.maleCases,
      femaleCases: femaleCases ?? this.femaleCases,
      currentGender: currentGender ?? this.currentGender,
      selectedMaleCase:
          selectedMaleCase != null ? selectedMaleCase() : this.selectedMaleCase,
      selectedFemaleCase: selectedFemaleCase != null
          ? selectedFemaleCase()
          : this.selectedFemaleCase,
    );
  }
}

@riverpod
class PhotoMuscle extends _$PhotoMuscle {
  @override
  Future<PhotoMuscleState> build() async {
    final maleCasesFuture = TextToVideoService.photoMuscleCaseList(1);
    final femaleCasesFuture = TextToVideoService.photoMuscleCaseList(2);

    final results = await Future.wait([maleCasesFuture, femaleCasesFuture]);

    final maleResult = results[0];
    final femaleResult = results[1];

    return PhotoMuscleState(
      maleCases:
          maleResult.status == Status.completed ? maleResult.data ?? [] : [],
      femaleCases: femaleResult.status == Status.completed
          ? femaleResult.data ?? []
          : [],
    );
  }

  void setGender(int gender) {
    state = AsyncData(state.value!.copyWith(currentGender: gender));
  }

  void setCaseItem(PhotoMuscleCaseItem caseItem) {
    final currentState = state.value!;
    if (currentState.currentGender == 1) {
      if (currentState.selectedMaleCase?.id == caseItem.id) {
        state = AsyncData(currentState.copyWith(selectedMaleCase: () => null));
      } else {
        state =
            AsyncData(currentState.copyWith(selectedMaleCase: () => caseItem));
      }
    } else {
      if (currentState.selectedFemaleCase?.id == caseItem.id) {
        state =
            AsyncData(currentState.copyWith(selectedFemaleCase: () => null));
      } else {
        state = AsyncData(
            currentState.copyWith(selectedFemaleCase: () => caseItem));
      }
    }
  }

  void generateVideo() async {
    final currentState = state.value!;
    final imgUrl = ref.read(photoModificationCurrentProvider).remoteUrl;
    if (imgUrl == null) {
      ToastUtil.showToast("请先选择图片");
      return;
    }

    final selectedCase = currentState.currentSelectedCase;
    if (selectedCase?.casePrompt == null) {
      ToastUtil.showToast("请先选择模版");
      return;
    }

    SmartDialog.showLoading(msg: "提交中...");
    try {
      var result = await TextToVideoService.imageToImage(
          selectedCase!.casePrompt!, imgUrl);
      if (result.status == Status.completed) {
        navigatorKey.currentContext?.push("/$workRecordPage");
      } else {
        ToastUtil.showToast("提交失败");
        if (result.exception != null) {
          debugPrint(result.exception!.getMessage());
        }
      }
    } catch (e) {
      ToastUtil.showToast("提交异常");
      debugPrint(e.toString());
    } finally {
      SmartDialog.dismiss();
    }
  }
}
