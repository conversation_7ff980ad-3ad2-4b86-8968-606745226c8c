// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'photo_muscle_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$photoMuscleHash() => r'6c2e55df1f6c0114727fe69e35c5e3f6f8c7ab2b';

/// See also [PhotoMuscle].
@ProviderFor(PhotoMuscle)
final photoMuscleProvider =
    AutoDisposeAsyncNotifierProvider<PhotoMuscle, PhotoMuscleState>.internal(
  PhotoMuscle.new,
  name: r'photoMuscleProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$photoMuscleHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PhotoMuscle = AutoDisposeAsyncNotifier<PhotoMuscleState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
