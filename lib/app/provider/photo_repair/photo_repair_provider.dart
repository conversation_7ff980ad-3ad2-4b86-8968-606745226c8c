import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/repository/service/text_to_video_service.dart';
import 'package:text_generation_video/utils/toast_util.dart';

import '../../navigation/router.dart';

part 'photo_repair_provider.g.dart';

@riverpod
class PhotoRepairAction extends _$PhotoRepairAction {
  @override
  void build() {
    return;
  }

  // 选择图片
  Future<String?> selectImg(ImageSource source) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: source);
    if (image != null) {
      // 上传图片
      SmartDialog.showLoading(msg: "上传中...");
      var result = await TextToVideoService.repairUploadFile(image.path);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        return result.data;
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
    return null;
  }

  // 提交老照片修复
  void commitOldPhotoRepair() async {
    var imgUrl = await selectImg(ImageSource.gallery);
    if (imgUrl != null && imgUrl.isNotEmpty) {
      SmartDialog.showLoading(msg: "提交中...");
      var result = await TextToVideoService.oldPhotoRepair(imgUrl);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        navigatorKey.currentContext?.push("/$workRecordPage");
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }

  // 提交照片画质修复
  void commitQualityPhotoRepair() async {
    var imgUrl = await selectImg(ImageSource.gallery);
    if (imgUrl != null && imgUrl.isNotEmpty) {
      SmartDialog.showLoading(msg: "提交中...");
      var result = await TextToVideoService.qualityPhotoRepair(imgUrl);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        navigatorKey.currentContext?.push("/$workRecordPage");
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }

  // AI抠图
  void saliencySegSubmitTask() async {
    var imgUrl = await selectImg(ImageSource.gallery);
    if (imgUrl != null && imgUrl.isNotEmpty) {
      SmartDialog.showLoading(msg: "提交中...");
      var result = await TextToVideoService.saliencySegSubmitTask(imgUrl);
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        navigatorKey.currentContext?.push("/$workRecordPage");
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
      }
    }
  }
}
