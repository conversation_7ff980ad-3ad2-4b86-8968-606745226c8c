import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:text_generation_video/app/navigation/router.dart';

import '../../../utils/toast_util.dart';
import '../../repository/service/text_to_video_service.dart';

part 'photo_modification_provider.g.dart';

class Modification {
  final int state; // 0: 默认状态，1:上传中，2:上传成功，3:上传失败
  final String? localUrl;
  final String? remoteUrl;

  const Modification({
    this.state = 0,
    this.localUrl,
    this.remoteUrl,
  });

  Modification copyWith({
    int? state,
    String? localUrl,
    String? remoteUrl,
  }) {
    return Modification(
      state: state ?? this.state,
      localUrl: localUrl ?? this.localUrl,
      remoteUrl: remoteUrl ?? this.remoteUrl,
    );
  }
}

// 当前上传图片的状态
@riverpod
class PhotoModificationCurrent extends _$PhotoModificationCurrent {
  @override
  Modification build() {
    return const Modification();
  }

  // 选择图片上传
  void selectImg({ImageSource? source}) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: source ?? ImageSource.gallery);
    if (image != null) {
      // 上传图片
      state = state.copyWith(state: 1);
      var result = await TextToVideoService.repairUploadFile(image.path);
      if (result.status == Status.completed) {
        state = state.copyWith(
          state: 2,
          remoteUrl: result.data,
          localUrl: image.path,
        );
      } else {
        ToastUtil.showToast(result.exception!.getMessage());
        state = state.copyWith(state: 3);
      }
    }
  }

  // 清除
  void clean() {
    state = const Modification();
  }

  // 提交改图
  void commit() async {
    var imgUrl = state.remoteUrl;
    if (imgUrl == null) {
      ToastUtil.showToast("请先上传图片");
      return;
    }
    var prompt = ref.read(modificationEditTextProvider).text;
    if (prompt.isEmpty) {
      ToastUtil.showToast("请输入提示内容");
      return;
    }
    SmartDialog.showLoading(msg: "提交中...");
    var result = await TextToVideoService.imageToImage(prompt, imgUrl);
    SmartDialog.dismiss();
    if (result.status == Status.completed) {
      navigatorKey.currentContext?.push("/$workRecordPage");
    } else {
      ToastUtil.showToast(result.exception!.getMessage());
    }
  }
}

@riverpod
class ModificationEditText extends _$ModificationEditText {
  final TextEditingController textEditingController = TextEditingController();

  @override
  TextEditingController build() {
    ref.onDispose(() => textEditingController.dispose());
    return textEditingController;
  }

  void setText(String? value) {
    textEditingController.text = value ?? "";
  }

  void clean() {
    textEditingController.clear();
  }
}

// 制作成功的集合列表
// 实现查看上一步，下一步内容
final List<String> imgList = [
  "http://gips3.baidu.com/it/u=100751361,1567855012&fm=3028&app=3028&f=JPEG&fmt=auto?w=60&h=80",
  "https://gips3.baidu.com/it/u=1039279337,1441343044&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1024_1024",
  "https://gips3.baidu.com/it/u=3732737575,1337431568&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1440_2560",
  "http://gips1.baidu.com/it/u=3874647369,3220417986&fm=3028&app=3028&f=JPEG&fmt=auto?w=130&h=320",
];

@riverpod
class PhotoModificationList extends _$PhotoModificationList {
  final uniqueRandom = UniqueRandom(0, 3);

  @override
  List<Modification>? build() {
    return null;
  }

  void modification() async {
    // 模拟请求
    SmartDialog.showLoading(msg: "改图中...");
    await Future.delayed(const Duration(seconds: 2));
    SmartDialog.dismiss();
    var index = uniqueRandom.next();
    if (index != null) {
      debugPrint("modification: $index");
      state = [...?state, Modification(state: 2, remoteUrl: imgList[index])];
    }
  }

  void clean() {
    state = null;
  }
}

class UniqueRandom {
  final int min;
  final int max;
  final Random _random = Random();
  late List<int> _pool;
  int _index = 0;

  UniqueRandom(this.min, this.max) {
    _reset();
  }

  /// 获取下一个不重复的随机数
  int? next() {
    if (_index >= _pool.length) {
      return null; // 已经没有数字可用了
    }
    return _pool[_index++];
  }

  /// 重置（重新洗牌，可以再次开始）
  void reset() {
    _reset();
  }

  void _reset() {
    _pool = List.generate(max - min + 1, (i) => min + i);
    _pool.shuffle(_random);
    _index = 0;
  }
}
