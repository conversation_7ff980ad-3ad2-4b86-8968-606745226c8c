import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../provider/theme/theme_provider.dart';
import 'router.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: CooSea
/// @Package:
/// @ClassName: CooSea
/// @Description: APP入口，可以设置一些配置信息，
/// 目前配置了支持多语言，暗黑模式，路由等等
/// @Author: frankylee
/// @CreateDate: 2023/5/17 14:46
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/17 14:46
/// @UpdateRemark: 更新说明

class TextToVideo extends ConsumerWidget {
  const TextToVideo({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ScreenUtilInit(
      useInheritedMediaQuery: true,
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp.router(
          debugShowCheckedModeBanner: false,
          theme: darkTheme,
          darkTheme: darkTheme,
          routerConfig: router,
          builder: FlutterSmartDialog.init(),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('zh', 'CN'),
          ],
        );
      },
    );
  }
}
