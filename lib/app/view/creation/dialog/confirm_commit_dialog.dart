import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/app/navigation/router.dart';

import '../../../provider/in_app_purchase/zenai_app_purchase_provider.dart';
import '../../compute_power/widget/top_up_product_widget.dart';

class ConfirmCommitDialog {
  static Future<bool?> confirmCommit(int powerItem) async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "confirm_creation_dialog",
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 313.w,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 22.h, bottom: 29.h),
                    child: const Text(
                      "确认是否提交",
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  Text(
                    "本次消耗$powerItem灵感值，请确认是否提交",
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF939393),
                    ),
                  ),
                  const SizedBox(height: 31),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "confirm_creation_dialog",
                              result: false,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFFF8F9FD),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              "关闭",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: Colors.black,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            SmartDialog.dismiss(
                              tag: "confirm_creation_dialog",
                              result: true,
                            );
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10.h),
                            decoration: BoxDecoration(
                              color: const Color(0xFF00CD6E),
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: Text(
                              "确认提交",
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: const Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 15),
                    ],
                  ),
                  const SizedBox(height: 15),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // 算力不足提示
  static void powerLessCommit(int? powerItem) async {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "power_less_dialog",
      builder: (context) {
        return PowerLessWidget(powerItem: powerItem);
      },
    );
  }
}

class PowerLessWidget extends ConsumerWidget {
  const PowerLessWidget({super.key, this.powerItem});

  final int? powerItem;

  // 充值组件
  void _openTopUpSheet(WidgetRef ref) async {
    var list = await ref
        .read(zenAiAppPurchasePowerProductProvider.notifier)
        .loadAllProducts();
    if (list != null &&
        list.isNotEmpty &&
        navigatorKey.currentContext != null) {
      showModalBottomSheet(
        context: navigatorKey.currentContext!,
        isScrollControlled: true,
        builder: (context) {
          return TopUpProductWidget(list: list);
        },
      );
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String txt = "";
    if (powerItem == null) {
      txt = "（续费会员，免费送灵感值）";
    } else {
      txt = "（本次消耗$powerItem灵感值）";
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 313.w,
          decoration: BoxDecoration(
            color: const Color(0xFF2D2C2F),
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 22.h, bottom: 29.h),
                child: const Text(
                  "灵感值不足",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
              const Text(
                "抱歉，您的灵感值不足",
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF9F9F9F),
                ),
              ),
              Text(
                txt,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF9F9F9F),
                ),
              ),
              const SizedBox(height: 31),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(width: 15),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        SmartDialog.dismiss(tag: "power_less_dialog");
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        decoration: BoxDecoration(
                          color: const Color(0xFF234C44),
                          borderRadius: BorderRadius.circular(7),
                        ),
                        child: const Text(
                          "关闭",
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF30E6B8),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        SmartDialog.dismiss(tag: "power_less_dialog");
                        _openTopUpSheet(ref);
                      },
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        decoration: BoxDecoration(
                          color: const Color(0xFF30E6B8),
                          borderRadius: BorderRadius.circular(7),
                        ),
                        child: const Text(
                          "充值灵感值",
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF222123),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                ],
              ),
              const SizedBox(height: 15),
            ],
          ),
        ),
      ],
    );
  }
}
