import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/config/icon_address.dart';

class MemberCheckDialog {
  // member: true表示是非会员
  static void showCheckDialog(bool member) {
    SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: true,
      tag: "member_check_dialog",
      builder: (context) {
        return MemberCheckWidget(member: member);
      },
    );
  }
}

class MemberCheckWidget extends StatelessWidget {
  const MemberCheckWidget({super.key, required this.member});

  final bool member;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        InkWell(
          onTap: () {
            SmartDialog.dismiss(tag: "member_check_dialog");
          },
          child: Image.asset(
            memberDialogClose,
            width: 32,
          ),
        ),
        const SizedBox(height: 49),
        Container(
          width: 312.w,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color(0xFF30E6B8),
                Color(0xFF9EF3DE),
                Color(0xFFC1FFEF),
                Color(0xFFE2FFF8),
                Color(0xFFFFFFFF),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(30),
          ),
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.topCenter,
            children: [
              Column(
                children: [
                  const SizedBox(height: 56),
                  const Text(
                    "您还不是会员",
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF18161A),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: 47,
                        height: 2,
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0x00E8FFF9), Color(0xFF4BE9C1)],
                          ),
                        ),
                      ),
                      const SizedBox(width: 9),
                      const Text(
                        "这是会员专属功能",
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF01CD99),
                        ),
                      ),
                      const SizedBox(width: 9),
                      Container(
                        width: 47,
                        height: 2,
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0xFF4BE9C1), Color(0x00E8FFF9)],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 19),
                  const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Column(
                        children: [
                          SizedBox(
                            width: 60,
                            height: 60,
                          ),
                          SizedBox(height: 6),
                          Text(
                            "解锁",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF495051),
                            ),
                          ),
                          Text(
                            "全部AI功能",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF495051),
                            ),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          SizedBox(
                            width: 60,
                            height: 60,
                          ),
                          SizedBox(height: 6),
                          Text(
                            "获取",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF495051),
                            ),
                          ),
                          Text(
                            "超多灵感值",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF495051),
                            ),
                          ),
                        ],
                      ),
                      Column(
                        children: [
                          SizedBox(
                            width: 60,
                            height: 60,
                          ),
                          SizedBox(height: 6),
                          Text(
                            "AI功能",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF495051),
                            ),
                          ),
                          Text(
                            "免费更新",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF495051),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  InkWell(
                    onTap: () {
                      SmartDialog.dismiss(tag: "member_check_dialog");
                      navigatorKey.currentContext?.push("/$memberPage");
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 13),
                      margin: const EdgeInsets.symmetric(horizontal: 31),
                      decoration: BoxDecoration(
                        color: const Color(0xFF000000),
                        borderRadius: BorderRadius.circular(26),
                      ),
                      child: Text(
                        member ? '立即开通' : '立即续费',
                        style: const TextStyle(
                          fontSize: 18,
                          color: Color(0xFF2CFFCA),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
              Positioned(
                top: -30,
                child: Image.asset(tabAiChat, width: 95),
              ),
            ],
          ),
        ),
        const SizedBox(height: 49),
      ],
    );
  }
}
