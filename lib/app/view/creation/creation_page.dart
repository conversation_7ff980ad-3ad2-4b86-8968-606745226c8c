import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/creation/creation_provider.dart';
import 'package:text_generation_video/app/view/creation/widget/creation_input_widget.dart';
import 'package:text_generation_video/app/view/creation/widget/creation_timbre_widget.dart';
import 'package:text_generation_video/app/view/creation/widget/creation_type_widget.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../config/icon_address.dart';
import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.creation
/// @ClassName: creation_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/1 14:47
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/1 14:47
/// @UpdateRemark: 更新说明
class CreationPage extends ConsumerStatefulWidget {
  const CreationPage({super.key});

  @override
  CreationPageState createState() => CreationPageState();
}

class CreationPageState extends ConsumerState<CreationPage> {
  final TextEditingController _textEditingController = TextEditingController();

  @override
  void dispose() {
    super.dispose();
    _textEditingController.dispose();
  }

  /// 玩法介绍
  void _introduction() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(padding: EdgeInsets.only(top: 16.h)),
            Stack(
              alignment: Alignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "文生视频玩法介绍",
                      style: TextStyle(
                        fontSize: 20.sp,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
                Positioned(
                  top: 0,
                  right: 19.w,
                  child: InkWell(
                    onTap: () {
                      context.pop();
                    },
                    child: Icon(
                      Icons.close,
                      color: Colors.black,
                      size: 25.r,
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(17.w, 15.h, 8.w, 12.h),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "1、提示词推荐格式",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 5.h)),
                  Text(
                    "视频画面的提示词无需复杂的语句，尽量以简短的名次，词组输入，并以逗号隔开。如“小猫睡觉，绿色草地，温馨可爱，阳光细腻”",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(17.w, 18.h, 8.w, 12.h),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "2、视频风格",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                  Padding(padding: EdgeInsets.only(bottom: 5.h)),
                  Text(
                    "通过视频风格，来选择最终视频效果的画面风格，感受精彩纷呈的视频效果",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
            Image.asset(
              descBg,
              width: 329.w,
            ),
            Padding(padding: EdgeInsets.only(bottom: 45.h)),
            const Divider(
              color: Color(0xFFE4E4E4),
              height: 1,
            ),
            Padding(padding: EdgeInsets.only(bottom: 12.h)),
            SizedBox(
              width: 228.w,
              height: 47.h,
              child: GradientButton(
                onPress: () {
                  context.pop();
                },
                radius: 12.r,
                gradient: const LinearGradient(
                  colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                ),
                child: Text(
                  "我知道了",
                  style: TextStyle(
                    fontSize: 18.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                bottom: 12.h + MediaQuery.paddingOf(context).bottom,
              ),
            ),
          ],
        );
      },
    );
  }

  /// 合成按钮
  Widget _buildCreation() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Divider(
          color: Color(0xFFE4E4E4),
          height: 1,
        ),
        Padding(padding: EdgeInsets.only(top: 12.h)),
        SizedBox(
          width: 228.w,
          height: 47.h,
          child: GradientButton(
            onPress: () {
              String content = _textEditingController.text;
              RouterUtil.checkLogin(context, call: () {
                ref.read(creationVideoProvider.notifier).creation(
                  context,
                  content,
                  () {
                    context.go("/$creationState");
                  },
                );
              });
            },
            gradient: const LinearGradient(
              colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
            ),
            child: Text(
              "开始合成",
              style: TextStyle(
                fontSize: 18.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(height: 6),
        const Text(
          "内容由AI生成，仅供参考",
          style: TextStyle(
            fontSize: 9,
            color: Color(0xFFA2A2A2),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.paddingOf(context).bottom,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "制作",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        leading: const Leading(),
        actions: [
          InkWell(
            onTap: _introduction,
            child: Image.asset(
              tutorialIcon,
              width: 18.w,
              height: 18.h,
            ),
          ),
          // Padding(padding: EdgeInsets.only(right: 17.w)),
          // InkWell(
          //   child: Image.asset(
          //     descIcon,
          //     width: 19.w,
          //     height: 19.h,
          //   ),
          // ),
          Padding(padding: EdgeInsets.only(right: 16.w)),
        ],
      ),
      body: CustomScrollView(
        slivers: [
          CreationInputWidget(
            controller: _textEditingController,
          ),
          const CreationTimbreWidget(),
          SliverPadding(
            padding: EdgeInsets.only(top: 16.h, left: 17.w, right: 17.w),
            sliver: SliverToBoxAdapter(
              child: Text(
                "视频风格",
                style: TextStyle(
                  fontSize: 17.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),
          ),
          const CreationTypeWidget(),
        ],
      ),
      bottomNavigationBar: _buildCreation(),
    );
  }
}
