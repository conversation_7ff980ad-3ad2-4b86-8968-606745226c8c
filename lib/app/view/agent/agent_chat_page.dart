import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/view/agent/widget/agent_chat_message_list_widget.dart';
import 'package:text_generation_video/app/view/agent/widget/agent_input_field_widget.dart';

import '../../widgets/appbar/leading.dart';

class AgentChatPage extends ConsumerWidget {
  const AgentChatPage({
    super.key,
    required this.data,
  });

  final Map data;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent, // 确保可以点击空白区域
      onTap: () {
        FocusScope.of(context).unfocus(); // 关闭软键盘
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          toolbarHeight: 44.h,
          title: Text(
            data["title"] ?? "",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
          leading: const Leading(
            color: Colors.white,
          ),
          actions: [
            InkWell(
              onTap: () {
                context.push(
                  '/$recordPage',
                  extra: {
                    "conversationId": data["conversationId"],
                  },
                );
              },
              child: const Text(
                "作品记录",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
          ),
            const SizedBox(width: 16),
          ],
        ),
        body: Column(
          children: [
            Expanded(
              child: Align(
                alignment: Alignment.topCenter,
                child: AgentChatMessageListWidget(
                  botId: data["botId"],
                  id: data["conversationId"],
                ),
              ),
            ),
            AgentInputFieldWidget(id: data["conversationId"]),
            const Text(
              "内容由AI生成，仅供参考",
              style: TextStyle(
                fontSize: 9,
                color: Color(0xFFA2A2A2),
              ),
            ),
            SizedBox(height: MediaQuery.paddingOf(context).bottom),
          ],
        ),
      ),
    );
  }
}
