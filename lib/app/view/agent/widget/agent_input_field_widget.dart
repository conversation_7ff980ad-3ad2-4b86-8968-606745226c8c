import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:text_generation_video/app/provider/agent/agent_chat_provider.dart';

import '../../../../config/icon_address.dart';
import '../../../navigation/router.dart';

class AgentInputFieldWidget extends ConsumerStatefulWidget {
  const AgentInputFieldWidget({
    super.key,
    required this.id,
  });

  final String? id;

  @override
  AgentInputFieldWidgetState createState() => AgentInputFieldWidgetState();
}

class AgentInputFieldWidgetState extends ConsumerState<AgentInputFieldWidget> {
  final TextEditingController _textEditingController = TextEditingController();

  void _showMorePlane() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Color(0xFF222123),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10),
              topRight: Radius.circular(10),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 15, right: 15),
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: const Text(
                    "取消",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFABABAB),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 22),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _itemBuild("拍摄", uploadCamera, () {
                      Navigator.pop(context);
                      ref
                          .read(uploadFileSelectorProvider.notifier)
                          .selectImg(ImageSource.camera);
                    }),
                    _itemBuild("照片", uploadPhoto, () {
                      Navigator.pop(context);
                      ref
                          .read(uploadFileSelectorProvider.notifier)
                          .selectImg(ImageSource.gallery);
                    }),
                    _itemBuild("文件", uploadFile, () {
                      Navigator.pop(context);
                      ref
                          .read(uploadFileSelectorProvider.notifier)
                          .selectFile();
                    }),
                  ],
                ),
              ),
              SizedBox(
                height: MediaQuery.paddingOf(context).bottom,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _itemBuild(String title, String icon, Function onTap) {
    return InkWell(
      onTap: () {
        onTap();
      },
      child: Column(
        children: [
          Container(
              width: 88,
              height: 88,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Image.asset(icon, width: 28, height: 28)),
          const SizedBox(height: 10),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var agentState = ref.watch(agentChatReplyStateProvider);
    var uploadInfo = ref.watch(uploadFileSelectorProvider);
    var state = uploadInfo.state;
    Widget uploadWidget = const SizedBox();
    if (state == 1 || state == 2) {
      // 上传中
      uploadWidget = Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        margin: const EdgeInsets.fromLTRB(10, 0, 10, 10),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                "${uploadInfo.info?.fileInfo?.file_name}",
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
            ),
            const SizedBox(width: 10),
            InkWell(
              onTap: () {
                ref.read(uploadFileSelectorProvider.notifier).clean();
              },
              child: const Icon(
                Icons.cancel,
                size: 18,
                color: Colors.black,
              ),
            ),
          ],
        ),
      );
    }
    return Container(
      padding: const EdgeInsets.only(top: 10, bottom: 10),
      child: Column(
        children: [
          uploadWidget,
          Row(
            children: [
              const SizedBox(width: 10),
              InkWell(
                onTap: () {
                  context.push('/$computePowerPage');
                },
                child: Column(
                  children: [
                    Image.asset(powerGreyIcon, width: 22, height: 22),
                    const SizedBox(height: 5),
                    const Text(
                      "我的算力",
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFFABABAB),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2D2C2F),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          decoration: const InputDecoration(
                            hintText: "输入您想问的…",
                            border: InputBorder.none,
                            hintStyle: TextStyle(
                              fontSize: 14,
                              color: Color(0xFFB9B9B9),
                            ),
                          ),
                          minLines: 1,
                          maxLines: 4,
                          keyboardType: TextInputType.multiline,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                          ),
                          controller: _textEditingController,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          _showMorePlane();
                        },
                        child: Image.asset(
                          chatMore,
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 10),
              InkWell(
                onTap: () {
                  if (!agentState) {
                    String msg = _textEditingController.text;
                    ref.read(agentChatMessageProvider.notifier).sendMessage(
                          widget.id,
                          msg,
                          textEditingController: _textEditingController,
                        );
                  }
                },
                child: Container(
                  width: 44,
                  height: 44,
                  alignment: Alignment.topRight,
                  padding: const EdgeInsets.only(top: 3, right: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2D2C2F),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Image.asset(
                    chatSend,
                    width: 31,
                    height: 31,
                    color: !agentState ? const Color(0xFF4BE6B7) : Colors.grey,
                  ),
                ),
              ),
              const SizedBox(width: 10),
            ],
          ),
        ],
      ),
    );
  }
}
