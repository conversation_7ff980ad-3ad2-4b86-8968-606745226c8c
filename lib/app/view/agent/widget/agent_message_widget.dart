import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:text_generation_video/app/provider/agent/agent_chat_provider.dart';
import 'package:text_generation_video/app/view/agent/widget/bubble_painter.dart';
import 'package:text_generation_video/utils/toast_util.dart';

import '../../../../config/icon_address.dart';

class AgentMessageWidget extends ConsumerStatefulWidget {
  const AgentMessageWidget({
    super.key,
    required this.messageItem,
    this.needAnima = false,
  });

  final MessageItem messageItem;

  final bool needAnima;

  @override
  AgentMessageWidgetState createState() => AgentMessageWidgetState();
}

class AgentMessageWidgetState extends ConsumerState<AgentMessageWidget> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((d) {
      if (widget.needAnima) {
        ref
            .read(computePowerProvider.notifier)
            .getComputePower(widget.messageItem.chatId);
      }
    });
  }

  // 复制内容
  void _copyToClipboard() {
    var text = widget.messageItem.conversationMsgItem?.content;
    if (text != null && text.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: text));
      ToastUtil.showToast("已复制");
    }
  }

  Widget _contentWidget() {
    if (widget.needAnima) {
      return TypewriterMarkdown(
        fullMarkdown:
            "${widget.messageItem.conversationMsgItem?.content ?? widget.messageItem.message}",
      );
    }
    return MarkdownText(
      markdownText:
          "${widget.messageItem.conversationMsgItem?.content ?? widget.messageItem.message}",
    );
  }

  @override
  Widget build(BuildContext context) {
    var powerNum = ref.watch(
      computePowerProvider.select((value) => value?.powerNum),
    );
    Widget powerWidget = const SizedBox();
    if (powerNum != null && widget.needAnima) {
      powerWidget = Text(
        "消耗$powerNum算力",
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFFABABAB),
        ),
      );
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(width: 10),
          Image.asset(
            chatAvatar,
            width: 40,
            height: 40,
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomPaint(
                  painter: BubblePainter(isMe: false),
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(15, 10, 10, 10),
                    child: _contentWidget(),
                  ),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.only(left: 6, right: 3),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          _copyToClipboard();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 4,
                            horizontal: 10,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF2C2C2F),
                            borderRadius: BorderRadius.circular(14),
                          ),
                          child: Row(
                            children: [
                              Image.asset(
                                chatCopy,
                                width: 12,
                                height: 14,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 3),
                              const Text(
                                "复制",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      powerWidget,
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 60),
        ],
      ),
    );
  }
}

class TypewriterMarkdown extends StatefulWidget {
  final String fullMarkdown;
  final Duration charDelay;

  const TypewriterMarkdown({
    super.key,
    required this.fullMarkdown,
    this.charDelay = const Duration(milliseconds: 20),
  });

  @override
  State<TypewriterMarkdown> createState() => _TypewriterMarkdownState();
}

class _TypewriterMarkdownState extends State<TypewriterMarkdown> {
  String _visibleText = '';
  Timer? _timer;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _startTyping();
  }

  void _startTyping() {
    _timer = Timer.periodic(widget.charDelay, (timer) {
      if (_currentIndex >= widget.fullMarkdown.length) {
        timer.cancel();
        return;
      }
      setState(() {
        _visibleText += widget.fullMarkdown[_currentIndex];
        _currentIndex++;
      });
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MarkdownText(markdownText: _visibleText);
  }
}

class MarkdownText extends ConsumerWidget {
  const MarkdownText({
    super.key,
    required this.markdownText,
  });

  final String markdownText;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MarkdownBlock(
      data: markdownText,
      config: MarkdownConfig(
        configs: [
          ImgConfig(
            builder: (String url, Map<String, String> attributes) {
              final isNetImage = url.startsWith('http');
              final imgWidget = isNetImage
                  ? CachedNetworkImage(
                      imageUrl: url,
                      placeholder: (c, u) {
                        return Container(
                          color: Colors.grey.shade200,
                          width: 200,
                          height: 120,
                        );
                      },
                      errorWidget: (c, o, s) {
                        return const SizedBox();
                      },
                      fadeInDuration: const Duration(milliseconds: 400),
                    )
                  : Image.asset(url, fit: BoxFit.contain);
              return imgWidget;
            },
          ),
        ],
      ),
    );
  }
}
