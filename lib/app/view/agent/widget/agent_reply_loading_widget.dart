import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/agent/agent_chat_provider.dart';
import 'dart:math';

import '../../../../config/icon_address.dart';
import 'bubble_painter.dart';

class AgentReplyLoadingWidget extends ConsumerWidget {
  const AgentReplyLoadingWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var state = ref.watch(agentChatReplyStateProvider);
    if (state) {
      return Container(
        margin: const EdgeInsets.only(bottom: 20),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(width: 10),
            Image.asset(
              chatAvatar,
              width: 40,
              height: 40,
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomPaint(
                    painter: BubblePainter(isMe: false),
                    child: Container(
                      padding: const EdgeInsets.fromLTRB(15, 10, 10, 10),
                      child: PerfectBreathingDots(
                        dotSize: 10,
                        dotSpacing: 8,
                        activeColor: Colors.grey.withAlpha(180),
                        inactiveColor: Colors.grey.withAlpha(100),
                        duration: const Duration(milliseconds: 1000), // 呼吸频率
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 60),
          ],
        ),
      );
    }
    return const SizedBox();
  }
}

class PerfectBreathingDots extends StatefulWidget {
  final double dotSize;
  final double dotSpacing;
  final Color activeColor;
  final Color inactiveColor;
  final Duration duration;

  const PerfectBreathingDots({
    super.key,
    this.dotSize = 12.0,
    this.dotSpacing = 8.0,
    this.activeColor = Colors.blue,
    this.inactiveColor = const Color(0xFFCCCCCC),
    this.duration = const Duration(milliseconds: 3000),
  });

  @override
  State<PerfectBreathingDots> createState() => _PerfectBreathingDotsState();
}

class _PerfectBreathingDotsState extends State<PerfectBreathingDots>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  double _sineWave(double t, double phaseShift) {
    // t = 0~1, phaseShift = 0~1
    final radians = 2 * pi * (t + phaseShift);
    return (sin(radians) + 1) / 2; // Normalize to 0~1
  }

  Widget _buildDot(double phaseShift) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (_, __) {
        final progress = _sineWave(_controller.value, phaseShift);
        final scale = 0.85 + 0.25 * progress;
        final color =
            Color.lerp(widget.inactiveColor, widget.activeColor, progress);

        return Transform.scale(
          scale: scale,
          child: Container(
            width: widget.dotSize,
            height: widget.dotSize,
            margin: EdgeInsets.symmetric(horizontal: widget.dotSpacing / 2),
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          "正在思考中",
          style: TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        const SizedBox(width: 4),
        _buildDot(0.0), // 第一个点：phase 0°
        _buildDot(1 / 3), // 第二个点：phase 120°
        _buildDot(2 / 3), // 第三个点：phase 240°
      ],
    );
  }
}
