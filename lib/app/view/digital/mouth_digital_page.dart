import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/digital/mouth_digital_provider.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/view/digital/dialog/mouth_digital_commit_dialog.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';
import 'package:text_generation_video/app/widgets/image_upload/media_upload_section.dart';
import 'package:text_generation_video/app/widgets/image_upload/upload_status_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

import '../../provider/record/record_provider.dart';
import '../../widgets/common/work_record_ation.dart';

class MouthDigitalPage extends ConsumerWidget {
  const MouthDigitalPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final uploadStatus = ref.watch(mouthDigitalUploadProvider);
    final scriptController = ref.watch(mouthDigitalScriptProvider);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "口播数字人",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          WorkRecordAction(workType: WorkRecordType.video.type),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              "数字人形象",
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
          ),
          MediaUploadSection(
            modification: uploadStatus,
            onSelect: () =>
                ref.read(mouthDigitalUploadProvider.notifier).selectVideo(),
            label: "添加数字人形象视频",
            subLabel: "仅支持mp4,mov,WebM格式\n时长30秒-2分钟，小于2G",
            iconAsset: modificationSelectIcon,
            mediaType: UploadMediaType.video,
            uploadingChild: const Text(
              "视频导入中\n请稍候",
              style: TextStyle(fontSize: 14, color: Color(0xFFFFFFFF)),
              textAlign: TextAlign.center,
            ),
            onRetry: () =>
                ref.read(mouthDigitalUploadProvider.notifier).selectVideo(),
            onClear: () =>
                ref.read(mouthDigitalUploadProvider.notifier).clean(),
            height: 313,
            sidePanel: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "示例",
                  style: TextStyle(color: Colors.white, fontSize: 18),
                ),
                const SizedBox(height: 6),
                // SizedBox(
                //     height: 160,
                //     width: 160,
                //     child: Image.asset(mouthDigitalDemoCorrect,
                //         fit: BoxFit.cover)),
                // const SizedBox(height: 6),
                // SizedBox(
                //     height: 160,
                //     width: 160,
                //     child:
                //         Image.asset(mouthDigitalDemoError, fit: BoxFit.cover)),
                _buildSampleImg(
                  isError: false,
                ),
                const SizedBox(height: 6),
                _buildSampleImg(
                  isError: true,
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              "注:作品将复刻形象视频中的音色，请确保视",
              style: TextStyle(color: Color(0xFF8A8D93), fontSize: 12),
            ),
          ),
          const SizedBox(height: 20),
          Expanded(child: _buildScriptInput(scriptController)),
          BottomActionBar(
            type: FunctionType.digitalHumanSpeech.type,
            buttonText: "立即生成",
            onPress: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                builder: (context) {
                  return MouthDigitalCommitDialog(
                    onConfirm: (workName) {
                      debugPrint('提交生成请求: workName=$workName');
                      ref
                          .read(mouthDigitalScriptProvider.notifier)
                          .generateVideo(workName);
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSampleImg({required bool isError}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Image.asset(
        isError ? mouthDigitalDemoError : mouthDigitalDemoCorrect,
        width: 130,
        height: 130,
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _buildScriptInput(TextEditingController scriptController) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "口播文案",
            style: TextStyle(color: Colors.white, fontSize: 16),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(color: const Color(0xFF565656), width: 0.6),
              ),
              child: TextField(
                controller: scriptController,
                decoration: const InputDecoration(
                  hintText: "请输入口播文案，建议控制在200字以内",
                  hintStyle: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF8A8D93),
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
                minLines: 4,
                maxLines: 20,
                keyboardType: TextInputType.multiline,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
