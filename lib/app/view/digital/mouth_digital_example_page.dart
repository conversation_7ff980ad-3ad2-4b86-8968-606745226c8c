import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:video_player/video_player.dart';

import '../../provider/record/record_provider.dart';
import '../../widgets/common/work_record_ation.dart';

class MouthDigitalExamplePage extends ConsumerWidget {
  const MouthDigitalExamplePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "口播数字人",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          WorkRecordAction(workType: WorkRecordType.video.type),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // const SizedBox(height: 20),
          Image.asset(mouthDigitalExampleBanner, width: 380, height: 100),
          _buildTitle(),
          Expanded(child: _buildVideo()),

          Image.asset(mouthDigitalExampleStyleText, width: 380, height: 100),
          GestureDetector(
              onTap: () {
                GoRouter.of(context).go("/$mouthDigitalPage");
              },
              child: _buildButton()),
          SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: const LinearGradient(colors: [
            Color(0xFF7BFAFF),
            Color(0xFFB8FCFF),
            Color(0xFFB8FCFF),
            Color(0xFF7BFAFF),
          ])),
      height: 40,
      width: 260,
      child: const Center(
          child: Text("立即生成优质口播视频",
              style: TextStyle(
                  color: Color(0xFF18161A),
                  fontSize: 16,
                  fontWeight: FontWeight.w600))),
    );
  }

  Widget _buildVideo() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: _MouthDigitalExampleVideo(
        videoUrl:
            'https://cdn.camera.msmds.cn/64/2025-09-18/68cbd82de4b00d580b27bda1.mp4',
      ),
    );
  }

  Widget _buildButton() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              gradient: const LinearGradient(colors: [
                Color(0xFF37F2FF),
                Color(0xFFDBFDFF),
                Color(0xFFFE62FF),
              ])),
          height: 60,
          width: 230,
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          top: 0,
          child: Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: const Color(0xFF17161A)),
            child: const Center(
                child: Text("立即制作",
                    style: TextStyle(color: Colors.white, fontSize: 16))),
          ),
        )
      ],
    );
  }
}

class _MouthDigitalExampleVideo extends StatefulWidget {
  const _MouthDigitalExampleVideo({required this.videoUrl});

  final String videoUrl;

  @override
  State<_MouthDigitalExampleVideo> createState() =>
      _MouthDigitalExampleVideoState();
}

class _MouthDigitalExampleVideoState extends State<_MouthDigitalExampleVideo> {
  late final VideoPlayerController _controller;
  late final Future<void> _initializeFuture;

  @override
  void initState() {
    super.initState();
    final uri = Uri.parse(widget.videoUrl);
    _controller = VideoPlayerController.networkUrl(uri)..setLooping(true);
    _initializeFuture = _controller.initialize().then((_) {
      if (!mounted) return;
      _controller.play();
      setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFF191619),
      child: FutureBuilder<void>(
        future: _initializeFuture,
        builder: (context, snapshot) {
          if (snapshot.hasError || _controller.value.hasError) {
            final message = snapshot.error?.toString() ??
                _controller.value.errorDescription ??
                '视频加载失败';
            return Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  message,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          }

          if (snapshot.connectionState != ConnectionState.done ||
              !_controller.value.isInitialized) {
            return const Center(
              child: SizedBox(
                width: 32,
                height: 32,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            );
          }

          final aspectRatio = _controller.value.aspectRatio;
          return LayoutBuilder(
            builder: (context, constraints) {
              final ratio = aspectRatio <= 0 ? 9 / 16 : aspectRatio;
              double width = constraints.maxWidth;
              double height = width / ratio;
              if (height > constraints.maxHeight && constraints.maxHeight > 0) {
                height = constraints.maxHeight;
                width = height * ratio;
              }
              return Align(
                alignment: Alignment.topCenter,
                child: SizedBox(
                  width: width,
                  height: height,
                  child: VideoPlayer(_controller),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
