import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';

class MouthDigitalExamplePage extends ConsumerWidget {
  const MouthDigitalExamplePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "卡通数字人",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {
              context.push("/$workRecordPage", extra: 1);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildTitle(),
          Expanded(child: _buildVideo()),
          GestureDetector(
              onTap: () {
                GoRouter.of(context).go("/$mouthDigitalPage");
              },
              child: _buildButton())
          
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: const LinearGradient(colors: [
            Color(0xFF7BFAFF),
            Color(0xFFB8FCFF),
            Color(0xFFB8FCFF),
            Color(0xFF7BFAFF),
          ])),
      height: 40,
      width: 260,
      child: const Center(
          child: Text("立即生成优质口播视频",
              style: TextStyle(
                  color: Color(0xFF18161A),
                  fontSize: 16,
                  fontWeight: FontWeight.w600))),
    );
  }

  Widget _buildVideo() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Container(
        color: Colors.red,
      ),
    );
  }

  Widget _buildButton() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              gradient: const LinearGradient(colors: [
                Color(0xFF37F2FF),
                Color(0xFFDBFDFF),
                Color(0xFFFE62FF),
              ])),
          height: 60,
          width: 230,
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          top: 0,
          child: Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(30),
                color: const Color(0xFF17161A)),
            child: const Center(
                child: Text("立即制作",
                    style: TextStyle(color: Colors.white, fontSize: 16))),
          ),
        )
      ],
    );
  }
}
