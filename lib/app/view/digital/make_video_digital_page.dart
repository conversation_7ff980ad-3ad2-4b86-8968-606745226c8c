import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/digital/digital_video_provider.dart';
import 'package:text_generation_video/app/provider/home/<USER>';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../navigation/router.dart';
import '../../provider/digital/digital_provider.dart';
import '../../repository/modals/digital/public_digital_human.dart';
import '../../widgets/appbar/leading.dart';

class MakeVideoDigitalPage extends ConsumerStatefulWidget {
  const MakeVideoDigitalPage({
    super.key,
    this.publicDigitalHuman,
  });

  final PublicDigitalHuman? publicDigitalHuman;

  @override
  MakeVideoDigitalPageState createState() => MakeVideoDigitalPageState();
}

class MakeVideoDigitalPageState extends ConsumerState<MakeVideoDigitalPage> {
  final TextEditingController _textEditingController = TextEditingController();

  void _jumpProcessResult() {
    RouterUtil.checkLogin(context, call: () {
      ref.read(digitalMakeVideoProvider.notifier).createVideo(
        () {
          context.pushReplacement("/$makeVideoDigitalProcessPage");
        },
      );
    });
  }

  // 视频名称
  Widget _buildInputWidget() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        decoration: BoxDecoration(
          color: const Color(0xFFF9FAFC),
          borderRadius: BorderRadius.circular(8),
        ),
        child: TextField(
          decoration: const InputDecoration(
            hintText: "请填写视频名称",
            border: InputBorder.none,
            hintStyle: TextStyle(
              fontSize: 14,
              color: Color(0xFF939794),
            ),
          ),
          onChanged: (text) {
            ref
                .read(publicDigitalVideoNameProvider.notifier)
                .setVideoName(text);
          },
          maxLines: 1,
          maxLength: 32,
          buildCounter: (
            BuildContext context, {
            required int currentLength,
            required bool isFocused,
            required int? maxLength,
          }) {
            return null; // ✅ 返回 null 即可隐藏计数器
          },
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  // 数字人显示区
  Widget _buildPublicDigital() {
    var crossAxisCount = 2;
    if (widget.publicDigitalHuman != null) {
      return SizedBox(
        height: 250,
        child: CustomListView(
          scrollDirection: Axis.horizontal,
          data: [widget.publicDigitalHuman],
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliverGridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: 8,
            crossAxisSpacing: 8,
            childAspectRatio: 115 / 84,
          ),
          renderItem: (context, index, o) {
            return ItemWidget(
              publicDigitalHuman: o,
            );
          },
        ),
      );
    }
    return SizedBox(
      height: 250,
      child: CustomListView(
        scrollDirection: Axis.horizontal,
        data: ref.watch(
          publicDigitalHumanListProvider
              .select((value) => value.publicDigitalList),
        ),
        onLoadMore: () async {
          ref.read(publicDigitalHumanListProvider.notifier).loadMore();
        },
        footerState: ref.watch(
          publicDigitalHumanListProvider.select((value) => value.loadState),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        sliverGridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: 8,
          crossAxisSpacing: 8,
          childAspectRatio: 115 / 84,
        ),
        renderItem: (context, index, o) {
          return ItemWidget(
            publicDigitalHuman: o,
          );
        },
      ),
    );
  }

  // 文案输入区
  Widget _buildContentWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              ref
                  .read(publicDigitalVideoContentProvider.notifier)
                  .randomContent(_textEditingController);
            },
            child: Image.asset(randomContentIcon, width: 56, height: 56),
          ),
          const SizedBox(height: 20),
          TextField(
            controller: _textEditingController,
            decoration: const InputDecoration(
              hintText: "请输入或粘贴您的文案…",
              border: InputBorder.none,
              hintStyle: TextStyle(
                fontSize: 14,
                color: Color(0xFF939794),
              ),
            ),
            onChanged: (text) {
              ref
                  .read(publicDigitalVideoContentProvider.notifier)
                  .setVideoContent(text);
            },
            minLines: 1,
            maxLines: 6,
            keyboardType: TextInputType.multiline,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var videoName = ref.watch(publicDigitalVideoNameProvider);
    var videoContent = ref.watch(publicDigitalVideoContentProvider);
    bool btnEnable = videoName != null &&
        videoName.isNotEmpty &&
        videoContent != null &&
        videoContent.isNotEmpty;
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      behavior: HitTestBehavior.translucent,
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          toolbarHeight: 44.h,
          title: Text(
            "定制数字人视频",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.black,
            ),
          ),
          leading: const Leading(),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 28),
              const Padding(
                padding: EdgeInsets.only(left: 16),
                child: Row(
                  children: [
                    Text(
                      "视频名称",
                      style: TextStyle(
                        fontSize: 15,
                        color: Color(0xFF141414),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              _buildInputWidget(),
              const SizedBox(height: 10),
              const Padding(
                padding: EdgeInsets.only(left: 16),
                child: Row(
                  children: [
                    Text(
                      "选择数字人",
                      style: TextStyle(
                        fontSize: 15,
                        color: Color(0xFF141414),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 18),
              _buildPublicDigital(),
              const SizedBox(height: 10),
              _buildContentWidget(),
              const SizedBox(height: 10),
            ],
          ),
        ),
        bottomNavigationBar: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 10),
            Row(
              children: [
                const SizedBox(width: 16),
                InkWell(
                  onTap: () {
                    ref.read(homeProvider.notifier).jumpToPage(2);
                    context.go("/");
                  },
                  child: Column(
                    children: [
                      Image.asset(myProductIcon, width: 20, height: 20),
                      const SizedBox(height: 5),
                      const Text(
                        "我的作品",
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF141414),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: GradientButton(
                    onPress: () {
                      _jumpProcessResult();
                    },
                    radius: 12,
                    enable: btnEnable,
                    shadow: false,
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.symmetric(vertical: 11),
                    gradient: const LinearGradient(
                      colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                    ),
                    child: const Text(
                      "生成视频",
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
              ],
            ),
            const SizedBox(height: 14),
            const Text(
              "内容由AI生成，仅供参考",
              style: TextStyle(
                fontSize: 10,
                color: Color(0xFF96969A),
              ),
            ),
            SizedBox(
              height: MediaQuery.paddingOf(context).bottom +
                  (MediaQuery.paddingOf(context).bottom > 0 ? 0 : 12),
            ),
          ],
        ),
      ),
    );
  }
}

class ItemWidget extends ConsumerWidget {
  const ItemWidget({
    super.key,
    required this.publicDigitalHuman,
  });

  final PublicDigitalHuman publicDigitalHuman;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var currentDigital = ref.watch(publicSelectorDigitalProvider);
    return InkWell(
      onTap: () {
        ref
            .read(publicSelectorDigitalProvider.notifier)
            .setDigital(publicDigitalHuman);
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: const Color(0xFFBBBBBB), width: 0.6),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 7),
                    Container(
                      width: 45,
                      height: 80,
                      decoration: BoxDecoration(
                        color: const Color(0xFF1D272B),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: CachedNetworkImage(
                        imageUrl: "${publicDigitalHuman.cover}",
                        width: 45,
                        height: 80,
                        errorWidget: (context, url, o) {
                          return const SizedBox(
                            width: 45,
                            height: 80,
                          );
                        },
                      ),
                    ),
                  ],
                ),
                Container(
                  height: 23,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                    gradient: LinearGradient(
                      colors: [Color(0xFFEAFFD8), Color(0xFFB1FFE1)],
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "${publicDigitalHuman.personName}",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 11,
                          color: Color(0xFF141414),
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
            InkWell(
              onTap: () {
                context.push(
                  "/$digitalPreviewPage",
                  extra: publicDigitalHuman.previewVideoUrl,
                );
              },
              child: Image.asset(digitalPreviewIcon, width: 29, height: 29),
            ),
            if (currentDigital?.id == publicDigitalHuman.id)
              Positioned(
                top: 0,
                left: 0,
                child: Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(6),
                      bottomRight: Radius.circular(6),
                    ),
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
