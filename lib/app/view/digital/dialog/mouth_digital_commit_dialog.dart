import 'package:flutter/material.dart';
import 'package:text_generation_video/utils/toast_util.dart';

class MouthDigitalCommitDialog extends StatefulWidget {
  const MouthDigitalCommitDialog({
    super.key,
    required this.onConfirm,
    this.onCancel,
  });
  final void Function(String) onConfirm;
  final VoidCallback? onCancel;

  @override
  State<MouthDigitalCommitDialog> createState() =>
      _MouthDigitalCommitDialogState();
}

class _MouthDigitalCommitDialogState extends State<MouthDigitalCommitDialog> {
  final TextEditingController _textController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            "给作品起个名字吧",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF2D2C2F),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: const Color(0xFF565656), width: 0.6),
            ),
            child: TextField(
              controller: _textController,
              autofocus: true,
              textAlign: TextAlign.center,
              decoration: const InputDecoration(
                hintText: "请填写本次作品名称",
                hintStyle: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF8A8D93),
                ),
                border: InputBorder.none,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              ),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 26),
          _buildButtons(
            context,
            () => widget.onCancel?.call(),
            () => widget.onConfirm.call(_textController.text),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons(
    BuildContext context,
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 140,
          child: _buildButton(
            "取消",
            () {
              Navigator.pop(context);
              onCancel?.call();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildButton(
            "确认提交",
            () {
              if (_textController.text.isEmpty) {
                ToastUtil.showToast("请输入作品名称");
                return;
              }
              Navigator.pop(context);
              onConfirm?.call();
            },
            isMain: true,
          ),
        ),
      ],
    );
  }

  Widget _buildButton(String text, VoidCallback onTap, {bool isMain = false}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 140,
        height: 56,
        decoration: BoxDecoration(
          color: isMain ? const Color(0xFF30E6B8) : const Color(0xff234C44),
          borderRadius: BorderRadius.circular(16),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: isMain ? Colors.black : const Color(0xff30E6B8),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}