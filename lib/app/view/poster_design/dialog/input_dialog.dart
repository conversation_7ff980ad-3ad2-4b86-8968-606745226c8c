import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ms_http/ms_http.dart';
import 'package:text_generation_video/app/repository/service/video_generation_service.dart';
import 'package:text_generation_video/config/icon_address.dart';

class InputDialog {
  static Future<bool?> input(
    BuildContext context,
    String? value,
    Function? onConfirm, {
    Function? onChanged,
    Function? onImageChange,
    String? imageUrl = "",
    int? dialogType = 0,
  }) async {
    return await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return InputView(
            value: value,
            onConfirm: onConfirm,
            onChanged: onChanged,
            imageUrl: imageUrl,
            dialogType: dialogType,
            onImageChange: onImageChange);
      },
    );
  }
}

class InputView extends StatefulWidget {
  final String? value;
  final Function? onConfirm;
  final Function? onChanged;
  final Function? onImageChange;
  final String? imageUrl;
  final int? dialogType;

  const InputView({
    super.key,
    this.value = "",
    this.onConfirm,
    this.onChanged,
    this.onImageChange,
    this.imageUrl,
    this.dialogType,
  });

  @override
  State<InputView> createState() => _InputViewState();
}

class _InputViewState extends State<InputView> {
  List<String> proportionsList = ["1:1", "4:3", "3:2", "16:9", "21:9"];
  List paintingStyle = [
    {
      "title": "真人写实",
      "tipsText": "(超写实:1.2), 超写实主义, 原始照片, 最佳质量, 杰作, 8k, 高细节, 真实光线"
    },
    {
      "title": "国风3D卡通",
      "tipsText": "(3D艺术:1.2), 中国风, 可爱, Q版, c4d, blender, octane渲染器, 柔和光线, 暖色调"
    },
    {
      "title": "吉卜力漫画",
      "tipsText": "(吉卜力风格:1.2), 细节背景, 水彩画, 吉卜力工作室, 异想天开的氛围, 柔和灯光, 手绘"
    },
    {
      "title": "文字海报",
      "tipsText": "(版式:1.2), 创意海报, 矢量图, 扁平设计, 极简主义, 粗体字体, 平面设计, 抽象背景"
    },
    {
      "title": "儿童绘本",
      "tipsText": "(儿童读物插画:1.2), 可爱风格, 鲜艳色彩, 清晰线条, 简单背景, 童话, 水彩画"
    },
    {"title": "国风水彩", "tipsText": "(中国水彩画:1.2), 水墨画, 传统艺术, 柔和色彩, 泼墨, 传统笔触, 诗意"},
    {"title": "古风水墨", "tipsText": "(古风:1.2), 水墨画, 传统绘画, 单色, 黑白, 山水, 细腻笔触"},
    {
      "title": "二次元漫画",
      "tipsText": "(动漫风格:1.2), 漫画风格, 高细节, 传神的眼睛, 赛璐珞上色, 奇幻, 鲜艳色彩"
    },
    {
      "title": "国风武侠",
      "tipsText": "(中国武侠风格:1.2), 武侠, 电影光效, 古代中国, 飘逸长袍, 戏剧性场景, 细节盔甲"
    },
    {"title": "中国水墨", "tipsText": "(中国水墨画:1.2), 传统艺术, 墨笔, 黑白, 细微色调, 随性, 禅意风格"},
    {
      "title": "厚涂漫画",
      "tipsText": "(油画:1.2), 数字绘画, 厚重笔触, 细节纹理, 戏剧性光线, 电影感, 平滑阴影"
    },
    {"title": "扁平插画", "tipsText": "(扁平插画:1.2), 矢量图, 极简主义, 粗线条, 纯色, 简洁设计, 简单形状"},
    {
      "title": "美漫",
      "tipsText": "(美国漫画风格:1.2), 赛璐珞上色, 超级英雄, 粗轮廓线, 肌肉感, 动态姿势, 富有表现力"
    },
    {"title": "机甲风", "tipsText": "(机甲:1.2), 未来主义, 复杂细节, 金属装甲, 发光部件, 科幻, 高达"},
    {
      "title": "蒸汽朋克",
      "tipsText": "(蒸汽朋克:1.2), 齿轮, 黄铜, 复杂机械, 维多利亚, 细节建筑, 复古未来主义"
    },
  ];
  int? paintingSelection;
  int ratioSelection = 0;
  String? _imageUrl;

  final TextEditingController _usernameController = TextEditingController();

  //用于判断是否点开比例选择框的标志位，点开则为true
  bool proporState = false;

  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        setState(() {
          proporState = false;
        });
      }
    });

    _imageUrl = widget.imageUrl;
    _usernameController.text = widget.value ?? "";
    _usernameController.selection = TextSelection.fromPosition(
      TextPosition(offset: _usernameController.text.length),
    );
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _usernameController.dispose();
    super.dispose();
  }

  Future<XFile?> webSelectImage() async {
    final ImagePicker picker = ImagePicker();
    final XFile? response = await picker.pickImage(source: ImageSource.gallery);
    // debugPrint('蓝色背景${response?.readAsBytes()} ----');

    return response;
  }

  // 选择图片
  void selectImg() async {
    var filePickerResult = await webSelectImage();
    if (filePickerResult != null) {
      // 上传图片
      // 上传中
      final fileBytes = await filePickerResult.readAsBytes();
      // state = VideoGenerationData(iamge: UploadFile()..fileInfo = info);
      SmartDialog.showLoading(msg: "上传中...");
      var result = await VideoGenerationService.uploadImage(
        fileBytes,
        filePickerResult.name,
      );
      SmartDialog.dismiss();
      if (result.status == Status.completed) {
        // 上传完成
        // debugPrint('图片上传成功${result.data}');
        setState(() {
          _imageUrl = result.data;
        });
        widget.onImageChange?.call(_imageUrl);
      } else {
        SmartDialog.showToast("上传失败，请重试");
        // debugPrint('图片上传失败${result}');
        // 上传失败
        // state = state.copyWith(image: null);
      }
    }
  }

  Widget imageView() {
    if (_imageUrl != null && _imageUrl != "") {
      return InkWell(
        onTap: () {
          selectImg();
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Image.network(
            "$_imageUrl",
            width: 54.w,
            height: 54.h,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    return InkWell(
      onTap: () {
        selectImg();
      },
      child: Image.asset(
        imageBoxImg,
        width: 54.w,
        height: 54.h,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom +
            (proporState ? 0 : 10.h), // 关键：键盘弹出时自动顶上
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 343.w,
            // height: 129.h,
            padding: EdgeInsets.all(10.r),
            decoration: BoxDecoration(
              color: const Color(0xFF2D2C2F),
              borderRadius: BorderRadius.circular(14.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                widget.dialogType == 1 ? imageView() : const SizedBox(),
                TextField(
                  controller: _usernameController,
                  focusNode: _focusNode,
                  autofocus: true,
                  maxLines: 3,
                  minLines: 3,
                  maxLength: 200,
                  decoration: const InputDecoration(
                    hintText: "输入图片描述",
                    border: InputBorder.none,
                  ),
                  style: TextStyle(
                    color: Colors.white, // 设置字体颜色
                    fontSize: 14.sp,
                  ),
                  onChanged: (value) {
                    if (widget.onChanged != null) {
                      widget.onChanged!(value);
                    }
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              proporState = !proporState;
                            });
                            FocusScope.of(context).unfocus();
                          },
                          child: Image.asset(
                            proportionIconImg,
                            width: 30.w,
                            height: 30.h,
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        paintingSelection != null
                            ? Row(
                                children: [
                                  Text(
                                    paintingStyle[paintingSelection ?? 0]
                                        ["title"],
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: const Color(0xFFFFFFFF),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8.w,
                                  ),
                                ],
                              )
                            : const SizedBox(),
                        Text(
                          proportionsList[ratioSelection],
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        )
                      ],
                    ),
                    Row(
                      children: [
                        Image.asset(
                          inspirationIconImg,
                          width: 17.w,
                          height: 18.h,
                        ),
                        SizedBox(
                          width: 6.w,
                        ),
                        Text(
                          "5",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        InkWell(
                          onTap: () {
                            if (widget.dialogType == 2) {
                              var content = paintingSelection != null
                                  ? paintingStyle[paintingSelection ?? 0]
                                      ["tipsText"]
                                  : null;
                              widget.onConfirm!(
                                proportionsList[ratioSelection],
                                content,
                              );
                            } else {
                              widget.onConfirm!(
                                proportionsList[ratioSelection],
                              );
                            }

                            Navigator.pop(context);
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 21.w,
                              vertical: 6.h,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF30E6B8),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Text(
                              "生成",
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: const Color(0xFF0C0B0D),
                              ),
                            ),
                          ),
                        )
                      ],
                    )
                  ],
                ),
                Container(
                  width: 323.w,
                  height: 2.h,
                  margin: EdgeInsets.symmetric(
                    vertical: 10.h,
                  ),
                  decoration: const BoxDecoration(
                    color: Color(0xFF565656),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Image.asset(
                          inspirationIconImg,
                          width: 17.w,
                          height: 18.h,
                        ),
                        SizedBox(
                          width: 6.w,
                        ),
                        Text(
                          "需要消耗灵感值 消耗规则",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        )
                      ],
                    ),
                    Row(
                      children: [
                        Text(
                          '成为会员获取灵感值',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        ),
                        SizedBox(
                          width: 6.w,
                        ),
                        Image.asset(
                          whiteArrowRight,
                          width: 5.w,
                          height: 7.h,
                        )
                      ],
                    )
                  ],
                )
              ],
            ),
          ),
          proporState
              ? Container(
                  width: 375.w,
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
                  margin: EdgeInsets.only(top: 20.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFF222123),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(14.r),
                      topRight: Radius.circular(14.r),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      widget.dialogType == 2
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "选择绘画风格：${paintingSelection != null ? paintingStyle[paintingSelection ?? 0]["title"] : ''}",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: const Color(0xFFFFFFFF),
                                  ),
                                ),
                                SizedBox(
                                  height: 10.h,
                                ),
                                SizedBox(
                                  height: 110.h,
                                  child: ListView(
                                    scrollDirection:
                                        Axis.horizontal, // 关键属性：设置为水平方向
                                    children: [
                                      ...paintingStyle.map(
                                        (item) {
                                          int index = paintingStyle.indexWhere(
                                              (items) =>
                                                  items['title'] ==
                                                  item["title"]);
                                          return Container(
                                            margin:
                                                EdgeInsets.only(right: 10.w),
                                            child: InkWell(
                                              onTap: () {
                                                setState(() {
                                                  paintingSelection =
                                                      index == paintingSelection
                                                          ? null
                                                          : index;
                                                });
                                              },
                                              child: Column(
                                                children: [
                                                  Container(
                                                    width: 76.w,
                                                    height: 92.h,
                                                    decoration: BoxDecoration(
                                                      color: const Color(
                                                          0xFF333136),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8.r),
                                                      border: Border.all(
                                                        color:
                                                            paintingSelection ==
                                                                    index
                                                                ? const Color(
                                                                    0xFF30E6B8)
                                                                : const Color(
                                                                    0xFF535256),
                                                        width: 1.w,
                                                      ),
                                                    ),
                                                    child: Center(
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Container(
                                                            width: 68,
                                                            height: 68,
                                                            decoration:
                                                                BoxDecoration(
                                                              color: const Color(
                                                                  0xFF18161A),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                4.r,
                                                              ),
                                                            ),
                                                          ),
                                                          Text(
                                                            item['title'],
                                                            style: TextStyle(
                                                              fontSize: 10.sp,
                                                              color: const Color(
                                                                  0xFFFFFFFF),
                                                            ),
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          );
                                        },
                                      )
                                    ],
                                  ),
                                )
                              ],
                            )
                          : const SizedBox(),
                      Text(
                        "选择绘画比例：${proportionsList[ratioSelection]}",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFFFFFFFF),
                        ),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      SizedBox(
                        height: 110.h,
                        child: ListView(
                          scrollDirection: Axis.horizontal, // 关键属性：设置为水平方向
                          children: [
                            ...proportionsList.map(
                              (item) {
                                int index = proportionsList.indexOf(item);
                                return Container(
                                  margin: EdgeInsets.only(right: 10.w),
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        ratioSelection = index;
                                      });
                                    },
                                    child: Column(
                                      children: [
                                        Container(
                                          width: 76.w,
                                          height: 76.h,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF333136),
                                            borderRadius:
                                                BorderRadius.circular(8.r),
                                          ),
                                          child: Center(
                                            child: Container(
                                              width: item == '1:1'
                                                  ? 27.w
                                                  : item == '4:3'
                                                      ? 33.w
                                                      : item == '3:2'
                                                          ? 33.w
                                                          : item == '16:9'
                                                              ? 43.w
                                                              : 48.w,
                                              height: item == '4:3'
                                                  ? 27.w
                                                  : item == '1:1'
                                                      ? 24.h
                                                      : item == '3:2'
                                                          ? 22.h
                                                          : item == '16:9'
                                                              ? 24.h
                                                              : 24.h,
                                              decoration: BoxDecoration(
                                                color: const Color(0xFF333136),
                                                borderRadius:
                                                    BorderRadius.circular(4.r),
                                                border: Border.all(
                                                  color: ratioSelection == index
                                                      ? const Color(0xFF30E6B8)
                                                      : const Color(0xFF535256),
                                                  width: 1.w,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 5.h,
                                        ),
                                        Text(
                                          item,
                                          style: TextStyle(
                                            fontSize: 10.sp,
                                            color: const Color(0xFFFFFFFF),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              },
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              : const SizedBox()
        ],
      ),
    );
  }
}
