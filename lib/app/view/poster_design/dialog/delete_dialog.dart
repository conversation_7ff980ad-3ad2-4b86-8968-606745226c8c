import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DeleteDialog {
  static Future<bool?> delete(
    BuildContext context,
    Function? onTap,
  ) async {
    return await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          width: 375.w,
          height: 222.h,
          decoration: BoxDecoration(
              color: const Color(0xFF222123),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(14.r),
                topRight: Radius.circular(14.r),
              )),
          child: Column(
            children: [
              SizedBox(
                height: 20.h,
              ),
              Text(
                "确定要删除吗",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFFFFFFF),
                ),
              ),
              SizedBox(
                height: 20.h,
              ),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2C2F),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () {
                        onTap!();
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: 20.h,
                          horizontal: 156.w,
                        ),
                        child: Text(
                          "确认",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: 311.w,
                      height: 1.h,
                      color: const Color(0xFF565656),
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: 20.h,
                          horizontal: 156.w,
                        ),
                        child: Text(
                          "取消",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: const Color(0xFFF4565F),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }
}
