import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/config/icon_address.dart';

class ImageDialog {
  static Future<bool?> image(
    String? imageUrl,
    String? titleText,
    Function onTap,
  ) async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "image_dialog",
      builder: (contexts) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(18.r), // 设置圆角
          child: Container(
            width: 270.w,
            height: 361.h,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: NetworkImage(imageUrl ?? ''),
                fit: BoxFit.cover,
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  bottom: 0,
                  child: Container(
                    width: 270.w,
                    height: 150.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.4),
                          Colors.black.withValues(alpha: 0.8),
                        ],
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 10.h,
                  right: 10.w,
                  child: InkWell(
                    onTap: () {
                      SmartDialog.dismiss(tag: "image_dialog");
                    },
                    child: Image.asset(
                      closeIconImg,
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 70.h,
                  right: 10.w,
                  child: SizedBox(
                    width: 250.w,
                    child: Text(
                      "$titleText",
                      maxLines: 2, // 限制最大行数
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFFFFFFFF),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  left: 10.w,
                  bottom: 11.h,
                  child: InkWell(
                    onTap: () {
                      onTap(titleText);
                      SmartDialog.dismiss(tag: "image_dialog");
                    },
                    child: Container(
                      width: 249,
                      height: 47,
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(47),
                      ),
                      child: Center(
                        child: Text(
                          "创作同款",
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: const Color(0xFF18161A),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
