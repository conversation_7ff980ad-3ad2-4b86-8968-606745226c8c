import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../../config/constant.dart';
import '../../../utils/prefs_util.dart';
import '../../provider/account/auth_provider.dart';
import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: server_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/11 17:54
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/11 17:54
/// @UpdateRemark: 更新说明
class ServerPage extends StatefulWidget {
  const ServerPage({super.key});

  @override
  ServerPageState createState() => ServerPageState();
}

class ServerPageState extends State<ServerPage> {
  String groupValue = PrefsUtil().getBool(PrefsKeys.serverWitchKey) == false
      ? Constant.releaseBaseUrl
      : Constant.devBaseUrl;

  /// 切换服务器
  void _onChange(WidgetRef ref, bool isTest, String url) {
    setState(() {
      groupValue = url;
    });
    ref.read(authProvider.notifier).logout();
    PrefsUtil().setBool(PrefsKeys.serverWitchKey, isTest);
    SmartDialog.showToast("切换成功，重启APP生效");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("服务器设置"),
        leading: const Leading(
          color: Colors.white,
        ),
        elevation: 0,
      ),
      body: Consumer(
        builder: (context, ref, child) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(padding: EdgeInsets.only(bottom: 16.h)),
                RadioListTile(
                  title: Text(
                    "测试服\n${Constant.devBaseUrl}",
                    style: TextStyle(
                      fontSize: 14.sp,
                    ),
                  ),
                  value: Constant.devBaseUrl,
                  groupValue: groupValue,
                  activeColor: Colors.red,
                  onChanged: (value) {
                    _onChange(ref, true, Constant.devBaseUrl);
                  },
                ),
                RadioListTile(
                  title: Text(
                    "正式服\n${Constant.releaseBaseUrl}",
                    style: TextStyle(
                      fontSize: 14.sp,
                    ),
                  ),
                  value: Constant.releaseBaseUrl,
                  groupValue: groupValue,
                  activeColor: Colors.red,
                  onChanged: (value) {
                    _onChange(ref, false, Constant.releaseBaseUrl);
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
