import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/repository/modals/recommend/category_list.dart';

import '../../../config/icon_address.dart';
import '../../widgets/appbar/leading.dart';

class HomeFunListPage extends ConsumerWidget {
  const HomeFunListPage({super.key, required this.categoryList});

  final CategoryList? categoryList;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        centerTitle: false,
        title: Text(
          "${categoryList?.categoryName}",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: GridView.builder(
        padding: const EdgeInsets.fromLTRB(14, 5, 14, 20),
        itemCount: categoryList?.functionInfoList?.length,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          childAspectRatio: 169 / 225,
        ),
        itemBuilder: (context, index) {
          var item = categoryList?.functionInfoList?[index];
          return InkWell(
            onTap: () {
              ref
                  .read(sameStyleActionProvider.notifier)
                  .sameAction(item?.functionType);
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(14),
              child: Stack(
                children: [
                  CachedNetworkImage(
                    imageUrl: item?.functionImgUrl ?? "",
                    placeholder: (c, s) {
                      return Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFF29282B),
                        ),
                      );
                    },
                    errorWidget: (c, o, s) {
                      return Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFF29282B),
                        ),
                      );
                    },
                  ),
                  Positioned(
                    top: 10,
                    left: 13,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Image.asset(funDescTag, width: 17),
                            const SizedBox(width: 4),
                            Text(
                              "${item?.functionName}",
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          "${item?.functionDesc}",
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
