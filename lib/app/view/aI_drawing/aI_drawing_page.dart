import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/aI_drawing/ai_drawing_provider.dart';
import 'package:text_generation_video/app/repository/modals/al_drawing/al_drawing_data.dart';
import 'package:text_generation_video/app/repository/modals/poster_design/ai_work_record_data.dart';
import 'package:text_generation_video/app/view/poster_design/dialog/delete_dialog.dart';
import 'package:text_generation_video/app/view/poster_design/dialog/image_dialog.dart';
import 'package:text_generation_video/app/view/poster_design/dialog/input_dialog.dart';
import 'package:text_generation_video/app/widgets/refresh/refresh_container.dart';
import 'package:text_generation_video/config/icon_address.dart';

class AiDrawingPage extends ConsumerStatefulWidget {
  const AiDrawingPage({super.key});

  @override
  AiDrawingState createState() => AiDrawingState();
}

class AiDrawingState extends ConsumerState<AiDrawingPage> {
  bool isShow = false;

  Widget headView(BuildContext context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      height: 44.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20),
            child: InkWell(
              onTap: () {
                context.pop();
              },
              child: Image.asset(whiteArrowLeft, width: 10, height: 18),
            ),
          ),
          Row(
            children: [
              Text(
                "AI绘图",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFFFFFFF),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(
            width: 30.w,
          )
        ],
      ),
    );
  }

  Widget exampleView() {
    // var caseList = ['1', '2', '3', '4', '5', '6'];
    List<AlDrawingData> posterCaseList =
        ref.watch(aidrawingProvider.select((state) => state.posterCaseList));
    var records = ref.watch(
        aiDrawingRecordListProvider.select((state) => state?.records ?? []));
    return Column(
      children: [
        Row(
          children: [
            Image.asset(
              newStarIconImg,
              width: 16.w,
              height: 16.h,
            ),
            SizedBox(
              width: 8.w,
            ),
            Image.asset(
              posterExampleImg,
              width: 87.w,
              height: 13.h,
            )
          ],
        ),
        SizedBox(
          height: 6.h,
        ),
        Row(
          children: [
            Text(
              "灵感示例",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFFFFFFFF),
              ),
            ),
            SizedBox(
              width: 4.w,
            ),
            Image.asset(
              slantDownwardImg,
              width: 15.w,
              height: 15.h,
            )
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
        SizedBox(
          // 必须指定高度，因为横向 ListView 在垂直方向是收缩的
          height: 130.h,
          child: ListView(
            scrollDirection: Axis.horizontal, // 关键属性：设置为水平方向
            children: posterCaseList.map((item) {
              // int index = posterCaseList.indexOf(item);
              return Padding(
                padding: EdgeInsets.only(right: 6.w),
                child: GestureDetector(
                  onTap: () {
                    // ExampleDialog.example(
                    //   item.imageUrl,
                    //   () {
                    //     ref
                    //         .read(fashionShootProvider.notifier)
                    //         .getModelImage(item.imageUrl, 2);
                    //   },
                    // );
                    ImageDialog.image(item.imgUrl, item.prompt, (text) {
                      ref.read(aidrawingProvider.notifier).getInputValue(text);
                      onClick(text);
                      // setState(() {
                      //   isShow = !isShow;
                      // });
                      // InputDialog.input(
                      //   context,
                      //   text,
                      //   (String proportion) {
                      //     // onConfirm
                      //     ref
                      //         .read(posterDesignProvider.notifier)
                      //         .posterDesign(proportion);
                      //   },
                      //   onChanged: (String value) {
                      //     ref
                      //         .read(posterDesignProvider.notifier)
                      //         .getInputValue(value);
                      //   },
                      // ).then((result) {
                      //   // 这里就是弹窗消失后的回调
                      //   setState(() {
                      //     isShow = !isShow;
                      //   });
                      // });
                    });
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.r), // 设置圆角
                    child: Container(
                      width: 82.w,
                      height: 113.h,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: NetworkImage(item.imgUrl ?? ''),
                          fit: BoxFit.cover, // 设置图片填充模式为覆盖整个容器
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            left: 5.w,
                            bottom: 4.h,
                            child: SizedBox(
                              width: 70.w,
                              child: Text(
                                "${item.caseTitle}",
                                overflow: TextOverflow.ellipsis, // 超出显示省略号...
                                maxLines: 1,
                                style: TextStyle(
                                  fontSize: 11.sp,
                                  color: const Color(0xFFFFFFFF),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        SizedBox(
          height: 10.h,
        ),
        SizedBox(
          height: 430.h,
          child: CustomListView(
            onRefresh: () async {
              ref.read(aiDrawingRecordListProvider.notifier).loadRecord();
            },
            onLoadMore: () async {
              ref.read(aiDrawingRecordListProvider.notifier).loadMoreRecord();
            },
            footerState: ref.watch(
              aiDrawingRecordListProvider.select((value) => value?.loadState),
            ),
            data: records,
            renderItem: (context, index) {
              return _buildItemWidget(records[index]);
            },
            // separator: (context, index) {
            //   return Container(
            //     margin: EdgeInsets.symmetric(horizontal: 10.w),
            //     child: const Divider(
            //       height: 1,
            //     ),
            //   );
            // },
            // empty: Container(
            //   margin: EdgeInsets.only(top: 60.h),
            //   child: Column(
            //     children: [
            //       Image.asset(
            //         noData,
            //         width: 74.w,
            //         height: 80.h,
            //         fit: BoxFit.contain,
            //       ),
            //       Padding(padding: EdgeInsets.only(bottom: 16.h)),
            //       Text(
            //         "暂无数据",
            //         style: TextStyle(
            //           fontSize: 12.sp,
            //           color: const Color(0xFF999999),
            //         ),
            //       ),
            //     ],
            //   ),
            // ),
          ),
        )
      ],
    );
  }

  Widget _buildItemWidget(AiWorkRecordData? record) {
    List data = record!.uploadDetailList ?? [];
    int? index = data.indexWhere((item) => item.uploadType == 1);
    if (index == -1) {
      return Container();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 16.h,
        ),
        Text(
          "图片生成",
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF8A8D93),
          ),
        ),
        SizedBox(
          height: 6.h,
        ),
        Text(
          "${record.uploadDetailList?[index].uploadContent}",
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFFFFFFFF),
          ),
        ),
        SizedBox(
          height: 12.h,
        ),
        record.state == 2
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8.r), // 设置圆角
                child: Image.network(
                  "${record.recordDetailList?[0].workContent}",
                  width: 82.w,
                  height: 113.h,
                  fit: BoxFit.cover,
                ),
              )
            : Container(
                width: 82.w,
                height: 113.h,
                decoration: BoxDecoration(
                  color: const Color(0xFF242326),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        lightningIconImg,
                        width: 15.w,
                        height: 15.h,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      Text(
                        "生成中…",
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: const Color(0xFFFFFFFF),
                        ),
                      )
                    ],
                  ),
                ),
              ),
        SizedBox(
          height: 10.h,
        ),
        Row(
          children: [
            InkWell(
              onTap: () {
                onClick(record.uploadDetailList?[index].uploadContent ?? "");
              },
              child: Container(
                height: 29.h,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2C2F),
                  borderRadius: BorderRadius.circular(7.r),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      editIconImg,
                      width: 12.w,
                      height: 13.h,
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    Text(
                      "重新编辑",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFFA4A8B0),
                      ),
                    )
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 5.w,
            ),
            InkWell(
              onTap: () {
                onClick(record.uploadDetailList?[index].uploadContent ?? "");
              },
              child: Container(
                height: 29.h,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2C2F),
                  borderRadius: BorderRadius.circular(7.r),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      againIconImg,
                      width: 13.w,
                      height: 10.h,
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    Text(
                      "再次生成",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFFA4A8B0),
                      ),
                    )
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 5.w,
            ),
            InkWell(
              onTap: () {
                DeleteDialog.delete(context, () {
                  ref
                      .read(aiDrawingRecordListProvider.notifier)
                      .deleteRecord([record.id!]);
                });
              },
              child: Container(
                height: 29.h,
                padding: EdgeInsets.symmetric(horizontal: 10.w),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2C2F),
                  borderRadius: BorderRadius.circular(7.r),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      deleteIconImg,
                      width: 12.w,
                      height: 13.h,
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    Text(
                      "删除",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFFA4A8B0),
                      ),
                    )
                  ],
                ),
              ),
            )
          ],
        )
      ],
    );
  }

  Widget inputView() {
    String inputValue =
        ref.watch(aidrawingProvider.select((state) => state.inputValue ?? ''));
    return Container(
      color: const Color(0xFF18161A),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 343.w,
            height: 48.h,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            decoration: BoxDecoration(
              color: const Color(0xFF333136),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: 280.w,
                  child: SingleChildScrollView(
                    child: Text(
                      inputValue == "" ? '输入图片描述' : inputValue,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFFFFFFFF),
                      ),
                    ),
                  ),
                ),
                Image.asset(
                  sendArrowImg,
                  width: 28.w,
                  height: 28.h,
                )
              ],
            ),
            // TextField(
            //   controller: TextEditingController(text: inputValue),
            //   enabled: false,
            //   style: TextStyle(
            //     fontSize: 14.sp,
            //     color: const Color(0xFFFFFFFF),
            //   ),
            //   decoration: const InputDecoration(
            //     hintText: "输入图片描述",
            //     border: InputBorder.none,
            //     hintStyle: TextStyle(
            //       color: Color(0xFF8A8D93),
            //     ),
            //   ),
            // ),
          ),
          SizedBox(
            height: 38.h,
          )
        ],
      ),
    );
  }

  onClick(inputValue) {
    setState(() {
      isShow = !isShow;
    });
    InputDialog.input(
      context,
      inputValue,
      (String proportion, String? tipsText) {
        ref.read(aidrawingProvider.notifier).posterDesign(proportion, tipsText);
      },
      onChanged: (String value) {
        ref.read(aidrawingProvider.notifier).getInputValue(value);
      },
      dialogType: 2,
    ).then((result) {
      // 这里就是弹窗消失后的回调
      // print('弹窗已关闭，返回值：$result');
      setState(() {
        isShow = !isShow;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    String inputValue =
        ref.watch(aidrawingProvider.select((state) => state.inputValue ?? ''));
    return Scaffold(
      backgroundColor: const Color(0xFF18161A),
      resizeToAvoidBottomInset: true, // 保证输入框跟随键盘弹起
      body: Stack(
        children: [
          Image.asset(
            fashionHeadBjImg,
            width: MediaQuery.sizeOf(context).width,
            // height: 197,
          ),
          Positioned(
            top: 44.h,
            child: headView(context),
          ),
          Container(
            margin: EdgeInsets.only(top: 94.h),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          exampleView(),
                        ],
                      ),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    onClick(inputValue);
                  },
                  child: !isShow ? inputView() : const SizedBox(),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
