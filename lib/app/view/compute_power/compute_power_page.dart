import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/zenai_app_purchase_provider.dart';
import 'package:text_generation_video/app/provider/member/compute_power_provider.dart';
import 'package:text_generation_video/app/view/compute_power/widget/top_up_product_widget.dart';
import 'package:text_generation_video/app/widgets/back/back_widget.dart';
import 'package:text_generation_video/config/constant.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../repository/modals/power/power_detail.dart';
import '../../widgets/appbar/leading.dart';

class ComputePowerPage extends ConsumerStatefulWidget {
  const ComputePowerPage({super.key});

  @override
  ComputePowerPageState createState() => ComputePowerPageState();
}

class ComputePowerPageState extends ConsumerState<ComputePowerPage> {
  final ScrollController _scrollController = ScrollController();

  // 充值组件
  void _openTopUpSheet() async {
    var list = await ref
        .read(zenAiAppPurchasePowerProductProvider.notifier)
        .loadAllProducts();
    if (list != null && list.isNotEmpty && mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) {
          return TopUpProductWidget(list: list);
        },
      );
    }
  }

  // 灵感值规则
  void _onRuleDesc() {
    context.push("/$webPage", extra: {"url": Constant.powerRuleDesc});
  }

  // header
  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: MediaQuery.sizeOf(context).width,
              height: 156 * MediaQuery.sizeOf(context).width / 375,
              padding: EdgeInsets.only(left: 32.w, right: 32.w),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.contain,
                  image: AssetImage(powerTagBg),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Image.asset(powerTag, width: 72),
                      const SizedBox(width: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "我的灵感值",
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            "${ref.watch(
                              userPowerBalanceProvider
                                  .select((value) => value?.powerBalance ?? 0),
                            )}",
                            style: const TextStyle(
                              fontSize: 46,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  // const SizedBox(height: 7),
                  // Padding(
                  //   padding: const EdgeInsets.symmetric(horizontal: 18),
                  //   child: Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       const Column(
                  //         crossAxisAlignment: CrossAxisAlignment.start,
                  //         children: [
                  //           Text(
                  //             "每会员每30天赠送",
                  //             style: TextStyle(
                  //               fontSize: 11,
                  //               color: Color(0xFF5DFFCE),
                  //             ),
                  //           ),
                  //           SizedBox(height: 3),
                  //           Text(
                  //             "600灵感值",
                  //             style: TextStyle(
                  //               fontSize: 12,
                  //               color: Color(0xFFFFFFFF),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //       Container(
                  //         height: 23,
                  //         width: 0.6,
                  //         color: const Color(0xFFFFFFFF),
                  //       ),
                  //       const Column(
                  //         children: [
                  //           Text(
                  //             "下次赠送时间",
                  //             style: TextStyle(
                  //               fontSize: 11,
                  //               color: Color(0xFF5DFFCE),
                  //             ),
                  //           ),
                  //           SizedBox(height: 3),
                  //           Text(
                  //             "2025-08-22",
                  //             style: TextStyle(
                  //               fontSize: 12,
                  //               color: Color(0xFFFFFFFF),
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 11, 16, 24),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                children: [
                  const Text(
                    "灵感值明细",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Container(
                    height: 4,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      gradient: const LinearGradient(colors: [
                        Color(0x0018161A),
                        Color(0x4630E6B8),
                        Color(0x8630E6B8),
                        Color(0xFF30E6B8)
                      ]),
                    ),
                    child: const Text(
                      "灵感值明细",
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.transparent,
                      ),
                    ),
                  ),
                ],
              ),
              const Text(
                "*以下数据更新可能存在延时",
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF595B60),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // item
  Widget _buildItem(PowerDetail detail) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${detail.description}",
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 3),
              Text(
                "${detail.createTime}",
                style: const TextStyle(
                  fontSize: 10,
                  color: Color(0xFF8A8D93),
                ),
              ),
            ],
          ),
          Text(
            "${detail.accountType == 1 ? '+' : '-'}${detail.powerNum}",
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // 充值
  Widget _buildTopUpWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(
          16, 10, 16, MediaQuery.paddingOf(context).bottom + 20),
      child: GradientButton(
        onPress: _openTopUpSheet,
        shadow: false,
        radius: 26,
        padding: const EdgeInsets.symmetric(vertical: 15),
        gradient: const LinearGradient(
          colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
        ),
        child: const Text(
          "去充值",
          style: TextStyle(
            fontSize: 16,
            color: Color(0xFF18161A),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          BackWidget(
            scrollController: _scrollController,
            assets: powerBgNew,
            rate: 1.0,
          ),
          Column(
            children: [
              Expanded(
                child: CustomListView(
                  controller: _scrollController,
                  sliverHeader: [
                    SliverAppBar(
                      centerTitle: true,
                      snap: true,
                      floating: true,
                      backgroundColor: Colors.transparent,
                      toolbarHeight: 44.h,
                      title: Text(
                        "灵感值",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.white,
                        ),
                      ),
                      leading: const Leading(
                        color: Colors.white,
                      ),
                      actions: [
                        InkWell(
                          onTap: _onRuleDesc,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 9,
                              vertical: 10,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.black54,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              "灵感值规则",
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],
                    ),
                  ],
                  header: _buildHeader(context, ref),
                  onLoadMore: () async {
                    ref.read(powerRecordProvider.notifier).loadMore();
                  },
                  data: ref.watch(
                    powerRecordProvider
                        .select((value) => value.powerDetailList),
                  ),
                  footerState: ref.watch(
                    powerRecordProvider.select((value) => value.loadState),
                  ),
                  renderItem: (context, index, o) {
                    return _buildItem(o);
                  },
                  separator: (context, index) {
                    return const SizedBox(height: 24);
                  },
                  empty: Container(
                    margin: EdgeInsets.only(top: 120.h),
                    alignment: Alignment.center,
                    child: const Text(
                      "暂无数据",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ),
              _buildTopUpWidget(),
            ],
          ),
        ],
      ),
    );
  }
}
