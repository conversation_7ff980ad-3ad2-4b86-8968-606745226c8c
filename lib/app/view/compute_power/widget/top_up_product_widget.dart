import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/app_purchase_provider.dart';
import 'package:text_generation_video/app/provider/in_app_purchase/zenai_app_purchase_provider.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/constant.dart';
import '../../../../config/icon_address.dart';
import '../../../navigation/router.dart';
import '../../../provider/member/compute_power_provider.dart';
import '../../../widgets/common/checkbox_agreen.dart';

class TopUpProductWidget extends ConsumerWidget {
  const TopUpProductWidget({
    super.key,
    required this.list,
  });

  final List<CustomAppleProduct> list;

  /// keys点击
  void keysTap(BuildContext context, String key) {
    if (key == "《灵感币充值服务协议》") {
      context.push(
        "/$webPage",
        extra: {"title": "", "url": Constant.memberPolicyUrl},
      );
    }
  }

  // 协议
  Widget _buildPrivacy(BuildContext context, WidgetRef ref, bool agreement) {
    List<String> content = [
      "阅读并同意",
      "《灵感币充值服务协议》",
    ];
    return Container(
      alignment: Alignment.center,
      child: CheckboxAgreen(
        value: agreement,
        onChanged: (value) {
          ref
              .read(zenAiPowerAgreementProvider.notifier)
              .setAgreement(!agreement);
        },
        size: 12,
        selectedIcon: Image.asset(agreementCheck, width: 12),
        unselectedColor: const Color(0xFF222123),
        child: HighlightText(
          data: content,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF8A8D93),
          ),
          keyStyle: const TextStyle(
            color: Color(0xFF30E6B8),
            fontSize: 12,
          ),
          keys: const ["《灵感币充值服务协议》"],
          onTapCallback: (String key) {
            keysTap(context, key);
          },
        ),
      ),
    );
  }

  Widget _buildProductItem(
    WidgetRef ref,
    CustomAppleProduct item,
    CustomAppleProduct? currentProduct,
    double itemWidth,
  ) {
    var select = item.productId == currentProduct?.productId;
    return Stack(
      clipBehavior: Clip.none,
      children: [
        InkWell(
          onTap: () {
            ref
                .read(currentAppleProductPowerProvider.notifier)
                .setCurrentProduct(item);
          },
          child: Container(
            width: itemWidth,
            decoration: BoxDecoration(
              gradient: select
                  ? const LinearGradient(
                      colors: [Color(0xFFF9FFFD), Color(0xFFA6FEDC)],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    )
                  : const LinearGradient(
                      colors: [Color(0xFF29282B), Color(0xFF29282B)]),
              borderRadius: BorderRadius.circular(13),
              border: Border.all(
                color:
                    select ? const Color(0xFF52F0D2) : const Color(0xFF29282B),
                width: 2,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(topUpPowerIcon, width: 22),
                    const SizedBox(width: 5),
                    Text(
                      "${item.package?.powerNum}",
                      style: TextStyle(
                        fontSize: 18,
                        color: select ? Colors.black : Colors.white,
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 9),
                Text(
                  "${item.details?.currencySymbol}${item.details?.rawPrice}",
                  style: TextStyle(
                    fontSize: 14,
                    color: select ? Colors.black : Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (select)
          Positioned(
            top: -2,
            right: -3,
            child: Image.asset(topUpSelectIcon, width: 20),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var currentProduct = ref.watch(currentAppleProductPowerProvider);
    var agreement = ref.watch(zenAiPowerAgreementProvider);
    var itemWidth = (MediaQuery.sizeOf(context).width - 48) / 3;
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        20,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    "灵感值充值",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    "灵感值余额：${ref.watch(
                      userPowerBalanceProvider
                          .select((value) => value?.powerBalance ?? 0),
                    )}",
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF8A8D93),
                    ),
                  ),
                ],
              ),
              InkWell(
                onTap: () {
                  context.pop();
                },
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Image.asset(
                    cancelEditIcon,
                    color: const Color(0xFF8A8D93),
                    width: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 18),
          SizedBox(
            height: itemWidth * 83 / 109,
            child: ListView.separated(
              itemCount: list.length,
              itemBuilder: (context, index) {
                var item = list[index];
                return _buildProductItem(
                  ref,
                  item,
                  currentProduct,
                  itemWidth,
                );
              },
              scrollDirection: Axis.horizontal,
              separatorBuilder: (context, index) {
                return const SizedBox(width: 8);
              },
            ),
          ),
          const SizedBox(height: 26),
          const Text(
            "充值灵感值有效期1年，不支持转赠、提现和退款",
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF8A8D93),
            ),
          ),
          const SizedBox(height: 20),
          GradientButton(
            onPress: () {
              ref.read(zenAiAppPurchaseProvider.notifier).purchasePowerById();
            },
            enable: currentProduct != null && agreement,
            shadow: false,
            margin: const EdgeInsets.symmetric(horizontal: 21),
            padding: const EdgeInsets.symmetric(vertical: 15),
            radius: 26,
            gradient: const LinearGradient(
              colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
            ),
            child: const Text(
              "立即购买",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF18161A),
              ),
            ),
          ),
          const SizedBox(height: 15),
          _buildPrivacy(context, ref, agreement),
        ],
      ),
    );
  }
}

class ProductTest {
  int value;
  int price;

  ProductTest(this.value, this.price);
}

final products = [
  ProductTest(1000, 128),
  ProductTest(1880, 228),
  ProductTest(3680, 398),
];
