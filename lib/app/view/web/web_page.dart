import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: web_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/11/21 11:30
/// @UpdateUser: frankylee
/// @UpdateData: 2023/11/21 11:30
/// @UpdateRemark: 更新说明
class WebPage extends StatefulWidget {
  const WebPage({
    super.key,
    required this.data,
  });

  final Map data;

  @override
  WebPageState createState() => WebPageState();
}

class WebPageState extends State<WebPage> {
  double _progress = 0.0;

  @override
  Widget build(BuildContext context) {
    String? title = widget.data["title"];
    String? url = widget.data["url"];
    Widget body = const SizedBox();
    if (url != null) {
      debugPrint("web-load-url: $url");
      body = InAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(url)),
        initialSettings: InAppWebViewSettings(
          transparentBackground: true,
        ),
        onLoadStart: (controller, url) async {},
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          var uri = navigationAction.request.url!;

          if (![
            "http",
            "https",
            "file",
            "chrome",
            "data",
            "javascript",
            "about"
          ].contains(uri.scheme)) {
            if (await canLaunchUrl(uri)) {
              // Launch the App
              await launchUrl(
                uri,
              );
              // and cancel the request
              return NavigationActionPolicy.CANCEL;
            }
          }

          return NavigationActionPolicy.ALLOW;
        },
        onLoadStop: (controller, url) async {
          setState(() {
            _progress = 0.0;
          });
        },
        onProgressChanged: (controller, progress) {
          setState(() {
            _progress = progress.toDouble();
          });
        },
        onReceivedError: (controller, request, error) {
          setState(() {
            _progress = 0.0;
          });
        },
        onUpdateVisitedHistory: (controller, url, isReload) {},
        onConsoleMessage: (controller, consoleMessage) {},
      );
    }
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          title ?? "",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: Column(
        children: [
          _progress / 100 == 1
              ? const SizedBox()
              : LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  color: const Color(0xFF30E6B8),
                  minHeight: 2.h,
                  value: _progress / 100,
                ),
          Expanded(child: body),
        ],
      ),
    );
  }
}
