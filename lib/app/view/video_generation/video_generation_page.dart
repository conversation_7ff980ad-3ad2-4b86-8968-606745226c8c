import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';
import 'package:text_generation_video/app/provider/video_generation/video_generation_provider.dart';
import 'package:text_generation_video/app/view/text_to_video/widget/text_to_video_widget.dart';
import 'package:text_generation_video/app/view/video_generation/widget/creative_effects_widget.dart';
import 'package:text_generation_video/app/view/video_generation/widget/image_video_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/work_record_ation.dart';
import 'package:text_generation_video/config/icon_address.dart';

class VideoGenerationPage extends ConsumerWidget {
  const VideoGenerationPage({super.key});

  Widget headView(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      title: Text(
        "AI视频",
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.white,
        ),
      ),
      leading: const Leading(
        color: Color(0xFFFFFFFF),
      ),
      actions: [
        WorkRecordAction(workType: WorkRecordType.recent.type),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget _buildTabView(BuildContext context, WidgetRef ref) {
    var tabType =
        ref.watch(videoGenerationProvider.select((value) => value.tabType));
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            ref.read(videoGenerationProvider.notifier).tabTypeChange(1);
          },
          child: Column(
            children: [
              Text(
                "图生视频",
                style: TextStyle(
                  fontSize: 14,
                  color: tabType == 1
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF8A8D93),
                ),
              ),
              const SizedBox(
                height: 11,
              ),
              tabType == 1
                  ? Container(
                      width: 51,
                      height: 3,
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    )
                  : const SizedBox(
                      width: 51,
                      height: 3,
                    )
            ],
          ),
        ),
        InkWell(
          onTap: () {
            ref.read(videoGenerationProvider.notifier).tabTypeChange(2);
          },
          child: Column(
            children: [
              Text(
                "文生视频",
                style: TextStyle(
                  fontSize: 14,
                  color: tabType == 2
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF8A8D93),
                ),
              ),
              const SizedBox(
                height: 11,
              ),
              tabType == 2
                  ? Container(
                      width: 51,
                      height: 3,
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    )
                  : const SizedBox(
                      width: 51,
                      height: 3,
                    )
            ],
          ),
        ),
        InkWell(
          onTap: () {
            ref.read(videoGenerationProvider.notifier).tabTypeChange(3);
          },
          child: Column(
            children: [
              Text(
                "创意特效",
                style: TextStyle(
                  fontSize: 14,
                  color: tabType == 3
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF8A8D93),
                ),
              ),
              const SizedBox(
                height: 11,
              ),
              tabType == 3
                  ? Container(
                      width: 51,
                      height: 3,
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    )
                  : const SizedBox(
                      width: 51,
                      height: 3,
                    )
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var tabType =
        ref.watch(videoGenerationProvider.select((value) => value.tabType));
    // bool buttonState =
    //     this.buttonState(tabType, image, image2, textValue, patternType);
    return Container(
      color: const Color(0xFF18161A),
      child: Stack(
        children: [
          Image.asset(
            headBjImg,
            width: MediaQuery.sizeOf(context).width,
            // height: 197,
          ),
          headView(context),
          Container(
            margin: EdgeInsets.only(top: 102.h),
            child: Column(
              children: [
                SizedBox(
                  width: MediaQuery.sizeOf(context).width,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 37),
                    child: _buildTabView(context, ref),
                  ),
                ),
                Container(
                  width: MediaQuery.sizeOf(context).width,
                  height: 1,
                  color: const Color(0xFF565656),
                  // margin: const EdgeInsets.only(top: 16),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 155.h),
            child: Column(
              children: [
                Expanded(
                  child: tabType == 1
                      ? const ImageVideoWidget()
                      : tabType == 2
                          ? const TextToVideoWidget(caseShowTop: false)
                          : tabType == 3
                              ? const CreativeEffectsWidget()
                              : const SizedBox(),
                ),
                // ButtonWidget(
                //   disableState: buttonState,
                //   onTap: () {
                //     videoGeneration.makeVideosClick();
                //   },
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
