import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/video_generation/video_generation_provider.dart';
import 'package:text_generation_video/app/view/video_generation/widget/button_widget.dart';
import 'package:text_generation_video/app/view/video_generation/widget/creative_effects_widget.dart';
import 'package:text_generation_video/app/view/video_generation/widget/image_video_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

class VideoGenerationPage extends ConsumerWidget {
  const VideoGenerationPage({super.key});

  Widget headView(BuildContext context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20),
            child: Padding(
              padding: const EdgeInsets.only(left: 20),
              child: InkWell(
                onTap: () {
                  context.pop();
                },
                child: Image.asset(whiteArrowLeft, width: 10, height: 18),
              ),
            ),
          ),
          Row(
            children: [
              Image.asset(movieIconImg, width: 17, height: 16),
              const SizedBox(
                width: 4,
              ),
              const Text(
                "AI视频",
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFFFFFFFF),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(
                width: 3,
              ),
              Image.asset(questionMarkIconImg, width: 15, height: 15),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0xFF565656),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Text(
                '生成记录',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFFFFFFFF),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabView(BuildContext context, WidgetRef ref) {
    var tabType =
        ref.watch(videoGenerationProvider.select((value) => value.tabType));
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            ref.read(videoGenerationProvider.notifier).tabTypeChange(1);
          },
          child: Column(
            children: [
              Text(
                "图生视频",
                style: TextStyle(
                  fontSize: 14,
                  color: tabType == 1
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF8A8D93),
                ),
              ),
              const SizedBox(
                height: 11,
              ),
              tabType == 1
                  ? Container(
                      width: 51,
                      height: 3,
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    )
                  : const SizedBox(
                      width: 51,
                      height: 3,
                    )
            ],
          ),
        ),
        InkWell(
          onTap: () {
            ref.read(videoGenerationProvider.notifier).tabTypeChange(2);
          },
          child: Column(
            children: [
              Text(
                "文生视频",
                style: TextStyle(
                  fontSize: 14,
                  color: tabType == 2
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF8A8D93),
                ),
              ),
              const SizedBox(
                height: 11,
              ),
              tabType == 2
                  ? Container(
                      width: 51,
                      height: 3,
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    )
                  : const SizedBox(
                      width: 51,
                      height: 3,
                    )
            ],
          ),
        ),
        InkWell(
          onTap: () {
            ref.read(videoGenerationProvider.notifier).tabTypeChange(3);
          },
          child: Column(
            children: [
              Text(
                "创意特效",
                style: TextStyle(
                  fontSize: 14,
                  color: tabType == 3
                      ? const Color(0xFF30E6B8)
                      : const Color(0xFF8A8D93),
                ),
              ),
              const SizedBox(
                height: 11,
              ),
              tabType == 3
                  ? Container(
                      width: 51,
                      height: 3,
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(1.5),
                      ),
                    )
                  : const SizedBox(
                      width: 51,
                      height: 3,
                    )
            ],
          ),
        ),
      ],
    );
  }

  bool buttonState(tabType, image, image2, textValue, patternType) {
    bool isButtonEnabled = true;
    if (tabType == 1) {
      if (patternType == 1 &&
          image != null &&
          image != "" &&
          textValue != null &&
          textValue != "") {
        isButtonEnabled = false;
      }

      if (patternType == 2 &&
          image != null &&
          image != "" &&
          image2 != null &&
          image2 != "" &&
          textValue != null &&
          textValue != "") {
        isButtonEnabled = false;
      }
    }

    if (tabType == 2) {}

    if (tabType == 3) {
      if (image != null && image != "") {
        isButtonEnabled = false;
      }
    }

    return isButtonEnabled;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var image =
        ref.watch(videoGenerationProvider.select((value) => value.image));
    var image2 =
        ref.watch(videoGenerationProvider.select((value) => value.image2));
    var textValue =
        ref.watch(videoGenerationProvider.select((value) => value.textValue));
    var patternType =
        ref.watch(videoGenerationProvider.select((value) => value.patternType));
    var videoGeneration = ref.read(videoGenerationProvider.notifier);
    var tabType =
        ref.watch(videoGenerationProvider.select((value) => value.tabType));
    bool buttonState =
        this.buttonState(tabType, image, image2, textValue, patternType);
    return Stack(
      children: [
        Image.asset(
          headBjImg,
          width: MediaQuery.sizeOf(context).width,
          // height: 197,
        ),
        Positioned(
          top: 54.h,
          child: headView(context),
        ),
        Container(
          margin: EdgeInsets.only(top: 102.h),
          child: Column(
            children: [
              SizedBox(
                width: MediaQuery.sizeOf(context).width,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 37),
                  child: _buildTabView(context, ref),
                ),
              ),
              Container(
                width: MediaQuery.sizeOf(context).width,
                height: 1,
                color: const Color(0xFF565656),
                // margin: const EdgeInsets.only(top: 16),
              )
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 155.h),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    child: tabType == 1
                        ? const ImageVideoWidget()
                        : tabType == 2
                            ? const SizedBox()
                            : tabType == 3
                                ? const CreativeEffectsWidget()
                                : const SizedBox(),
                  ),
                ),
              ),
              ButtonWidget(
                disableState: buttonState,
                onTap: () {
                  videoGeneration.makeVideosClick();
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
