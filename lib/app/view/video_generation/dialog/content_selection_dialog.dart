import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/config/icon_address.dart';

class ContentSelectionDialog {
  static Future<bool?> contentSelection(
    String? titleText,
    int selectType,
    List data,
    int index,
    Function? onChange,
  ) async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "content_selection_dialog",
      builder: (contexts) {
        return Consumer(
          builder: (context, ref, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      SmartDialog.dismiss(tag: "content_selection_dialog");
                    },
                    child: const Expanded(
                      child: Text(""),
                    ),
                  ),
                ),
                SelectionDialog(
                  data: data,
                  index: index,
                  selectType: selectType,
                  titleText: titleText,
                  onChange: onChange,
                )
              ],
            );
          },
        );
      },
    );
  }
}

class SelectionDialog extends StatefulWidget {
  final String? titleText;
  //数据列表
  final List? data;
  //选择的索引
  final int? index;
  //选择的类型 0视频时长 1视频分辨率
  final int? selectType;
  final Function? onChange;

  const SelectionDialog({
    super.key,
    required this.data,
    this.index = 0,
    this.selectType = 0,
    this.titleText,
    this.onChange,
  });

  @override
  // ignore: library_private_types_in_public_api
  _SelectionDialogState createState() => _SelectionDialogState();
}

class _SelectionDialogState extends State<SelectionDialog> {
  int? _selectedOption = 0; // null表示未选择，1表示选项1，2表示选项2

  @override
  void initState() {
    super.initState();
    _selectedOption = widget.index;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width,
      height: 164,
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(14),
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 29.w,
            height: 4.h,
            margin: const EdgeInsets.only(top: 13),
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(
            height: 13.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 16.w),
                child: SizedBox(
                  width: 15.w,
                ),
              ),
              Text(
                '${widget.titleText}',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFFFFFFFF),
                ),
              ),
              InkWell(
                onTap: () {
                  // childKey
                  if (widget.onChange != null) {
                    widget.onChange!(_selectedOption);
                    SmartDialog.dismiss(tag: "content_selection_dialog");
                  }
                },
                child: Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: Image.asset(
                    tickIconImg,
                    width: 15.w,
                    height: 13.h,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 26,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            width: MediaQuery.sizeOf(context).width,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: widget.selectType == 0
                  ? [
                      ...widget.data!.map((item) {
                        int index = widget.data!.indexOf(item);
                        return InkWell(
                          onTap: () {
                            setState(() {
                              _selectedOption = index; // 更新选中状态
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 73.w, vertical: 16.h),
                            decoration: BoxDecoration(
                              color: _selectedOption == index
                                  ? const Color(0xFF222123)
                                  : const Color(0xFF39373B),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _selectedOption == index
                                    ? const Color(0xFF30E6B8)
                                    : const Color(0xFF39373B),
                                width: _selectedOption == index ? 2 : 0,
                              ),
                            ),
                            child: Text(
                              "${item}s",
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: _selectedOption == index
                                    ? const Color(0xFF30E6B8)
                                    : const Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        );
                      }),
                    ]
                  : [
                      ...widget.data!.map((item) {
                        int index = widget.data!.indexOf(item);
                        return InkWell(
                          onTap: () {
                            setState(() {
                              _selectedOption = index; // 更新选中状态
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 38.w, vertical: 16.h),
                            decoration: BoxDecoration(
                              color: _selectedOption == index
                                  ? const Color(0xFF222123)
                                  : const Color(0xFF39373B),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _selectedOption == index
                                    ? const Color(0xFF30E6B8)
                                    : const Color(0xFF39373B),
                                width: _selectedOption == index ? 2 : 0,
                              ),
                            ),
                            child: Text(
                              "$item",
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: _selectedOption == index
                                    ? const Color(0xFF30E6B8)
                                    : const Color(0xFFFFFFFF),
                              ),
                            ),
                          ),
                        );
                      }),
                    ],
            ),
          )
        ],
      ),
    );
  }
}
