import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:video_player/video_player.dart';

class VideoCaseDialog {
  static Future<bool?> videoCase({
    String? videoUrl,
    String? imageUrl,
    Function? onTap,
  }) async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "video_case_dialog",
      builder: (contexts) {
        return VideoDialog(
          videoUrl: Uri.parse(
            videoUrl ?? "",
          ),
          imageUrl: imageUrl,
          onTap: onTap,
        );
      },
    );
  }
}

// 视频弹窗组件
class VideoDialog extends StatefulWidget {
  final Uri videoUrl;
  final String? imageUrl;
  final Function? onTap;

  const VideoDialog({
    super.key,
    required this.videoUrl,
    this.onTap,
    this.imageUrl,
  });

  @override
  State<VideoDialog> createState() => _VideoDialogState();
}

class _VideoDialogState extends State<VideoDialog> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  // 初始化视频
  Future<void> _initializeVideo() async {
    // 创建网络视频控制器
    _controller = VideoPlayerController.networkUrl(widget.videoUrl)
      ..setLooping(true) // 设置循环播放
      ..setVolume(1.0); // 设置音量

    try {
      // 初始化视频
      await _controller.initialize();

      // 开始播放
      await _controller.play();

      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      debugPrint('视频初始化失败: $e');
      // 可以选择显示错误信息或关闭弹窗
    }
  }

  @override
  void dispose() {
    // 重要：释放控制器资源
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent, // 透明背景
      insetPadding: const EdgeInsets.all(20),
      child: Container(
        width: 269,
        height: 361,
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(12),
        ),
        child: _isInitialized
            ? Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: VideoPlayer(_controller),
                  ),
                  Positioned(
                    top: 10.h,
                    right: 10.w,
                    child: InkWell(
                      onTap: () {
                        SmartDialog.dismiss(tag: "video_case_dialog");
                      },
                      child: Image.asset(
                        closeIconImg,
                        width: 24,
                        height: 24,
                      ),
                    ),
                  ),
                  Positioned(
                    left: 10.w,
                    bottom: 11.h,
                    child: InkWell(
                      onTap: () {
                        if (widget.onTap != null) {
                          widget.onTap?.call();
                        }
                        SmartDialog.dismiss(tag: "video_case_dialog");
                      },
                      child: Container(
                        width: 249,
                        height: 47,
                        decoration: BoxDecoration(
                          color: const Color(0xFF30E6B8),
                          borderRadius: BorderRadius.circular(47),
                        ),
                        child: Center(
                          child: Text(
                            "创作同款",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: const Color(0xFF18161A),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              )
            : const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
