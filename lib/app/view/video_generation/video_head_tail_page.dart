import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';
import 'package:text_generation_video/app/view/video_generation/widget/image_video_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/work_record_ation.dart';
import 'package:text_generation_video/config/icon_address.dart';

class VideoHeadTailPage extends ConsumerWidget {
  const VideoHeadTailPage({super.key});

  Widget headView(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      title: Text(
        "首尾帧",
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.white,
        ),
      ),
      leading: const Leading(
        color: Color(0xFFFFFFFF),
      ),
      actions: [
        WorkRecordAction(workType: WorkRecordType.video.type),
        const SizedBox(width: 16),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      color: const Color(0xFF18161A),
      child: Stack(
        children: [
          Image.asset(
            headBjImg,
            width: MediaQuery.sizeOf(context).width,
            // height: 197,
          ),
          headView(context),
          Container(
            margin: EdgeInsets.only(top: 100.h),
            child: const ImageVideoWidget(
              caseShowTop: true,
              patternType: 2,
            ),
          )
        ],
      ),
    );
  }
}
