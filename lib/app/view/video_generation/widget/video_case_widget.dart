import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/repository/modals/video_generation/video_list_case_data.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/app/view/video_generation/dialog/video_case_dialog.dart';

class VideoCaseWidget extends StatefulWidget {
  final List<VideoListCaseData> listData;
  final int selectedIndex;
  final Function? onTap;

  const VideoCaseWidget({
    super.key,
    required this.listData,
    this.selectedIndex = 0,
    this.onTap,
  });

  @override
  State<VideoCaseWidget> createState() => _VideoCaseWidgetState();
}

class _VideoCaseWidgetState extends State<VideoCaseWidget> {
  int selectedIndex = 0;

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    selectedIndex = widget.selectedIndex;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Image.asset(
              starIconImg,
              width: 15.w,
              height: 15.h,
            ),
            SizedBox(
              width: 2.w,
            ),
            Text(
              "特效案例",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFFFFFFFF),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 5.h,
        ),
        Text(
          "选择特效模版，生成同款视频",
          style: TextStyle(
            fontSize: 11.sp,
            color: const Color(0xFF6F737A),
          ),
        ),
        SizedBox(
          height: 10.h,
        ),
        SizedBox(
          // 必须指定高度，因为横向 ListView 在垂直方向是收缩的
          height: 130.h,
          child: ListView(
            scrollDirection: Axis.horizontal, // 关键属性：设置为水平方向
            children: widget.listData.map((item) {
              int index = widget.listData.indexOf(item);
              return Padding(
                padding: EdgeInsets.only(right: 6.w),
                child: GestureDetector(
                  onTap: () {
                    // setState(() {
                    //   selectedIndex = index;
                    // });
                    // debugPrint('点击了$index');
                    VideoCaseDialog.videoCase(
                      videoUrl: item.videoUrl ?? '',
                      imageUrl: item.cover ?? '',
                      onTap: () {
                        setState(() {
                          selectedIndex = index;
                        });
                        if (widget.onTap != null) {
                          widget.onTap?.call(index);
                        }
                      },
                    );
                  },
                  child: Container(
                    width: 85.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(
                        color: selectedIndex == index
                            ? const Color(0xff30e6b8)
                            : Colors.transparent,
                        width: 2.w,
                      ),
                      image: DecorationImage(
                        image: NetworkImage(item.cover ?? ""),
                        fit: BoxFit.cover,
                      ),
                    ),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 54.h,
                        ),
                        Image.asset(
                          playIconImg,
                          width: 17.w,
                          height: 19.h,
                        ),
                        SizedBox(
                          height: 30.h,
                        ),
                        SizedBox(
                          width: 66.w,
                          child: Text(
                            "${item.caseName}",
                            overflow: TextOverflow.ellipsis, // 超出显示省略号...
                            maxLines: 1,
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: const Color(0xFFFFFFFF),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
