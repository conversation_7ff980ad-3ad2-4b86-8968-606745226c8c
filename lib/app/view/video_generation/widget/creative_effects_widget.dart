import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/video_generation/video_generation_provider.dart';
import 'package:text_generation_video/app/repository/modals/video_generation/video_list_case_data.dart';
import 'package:text_generation_video/app/view/video_generation/widget/video_case_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

class CreativeEffectsWidget extends ConsumerWidget {
  const CreativeEffectsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var image =
        ref.watch(videoGenerationProvider.select((value) => value.image));
    List<VideoListCaseData>? caseList =
        ref.watch(creativeEffectsProvider.select((value) => value.caseData));
    var selectedIndex = ref
        .watch(creativeEffectsProvider.select((value) => value.selectedIndex));
    // VideoCaseDialog.videoCase();
    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      child: Column(
        children: [
          InkWell(
            onTap: () {
              ref.read(videoGenerationProvider.notifier).selectImg(1);
            },
            child: Container(
              width: 343,
              height: 343,
              padding:
                  EdgeInsets.only(top: image != null && image != "" ? 0 : 145),
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: const Color(0xFF565656),
                  width: 1,
                ),
              ),
              child: Center(
                child: image != null && image != ""
                    ? Image.network(
                        image,
                        height: 343,
                        errorBuilder: (context, error, stackTrace) {
                          return Column(
                            children: [
                              SizedBox(
                                height: 145.h,
                              ),
                              Image.asset(uploadIconImg, width: 27, height: 27),
                              const SizedBox(
                                height: 9,
                              ),
                              const Text(
                                "上传参考图1",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF8A8D93),
                                ),
                              ),
                            ],
                          );
                        },
                      )
                    : Column(
                        children: [
                          Image.asset(uploadIconImg, width: 27, height: 27),
                          const SizedBox(
                            height: 9,
                          ),
                          const Text(
                            "上传参考图1",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF8A8D93),
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 16.w,
              top: 20.h,
              right: 16.w,
            ),
            child: VideoCaseWidget(
              listData: caseList ?? [],
              selectedIndex: selectedIndex,
              onTap: (index) {
                ref.read(creativeEffectsProvider.notifier).selectCase(index);
              },
            ),
          ),
        ],
      ),
    );
  }
}
