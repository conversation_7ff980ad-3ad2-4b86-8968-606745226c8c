import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/provider/video_generation/video_generation_provider.dart';
import 'package:text_generation_video/app/repository/modals/video_generation/video_list_case_data.dart';
import 'package:text_generation_video/app/view/video_generation/widget/video_case_widget.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';
import 'package:text_generation_video/config/icon_address.dart';

class CreativeEffectsWidget extends ConsumerWidget {
  const CreativeEffectsWidget({super.key, this.caseShowTop = false});
  final bool caseShowTop;

  bool buttonState(tabType, image) {
    bool isButtonEnabled = false;

    if (tabType == 3 || caseShowTop) {
      if (image != null && image != "") {
        isButtonEnabled = true;
      }
    }

    return isButtonEnabled;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var image =
        ref.watch(videoGenerationProvider.select((value) => value.image));
    List<VideoListCaseData>? caseList =
        ref.watch(creativeEffectsProvider.select((value) => value.caseData));
    var selectedIndex = ref
        .watch(creativeEffectsProvider.select((value) => value.selectedIndex));
    var tabType =
        ref.watch(videoGenerationProvider.select((value) => value.tabType));
    var videoGeneration = ref.read(videoGenerationProvider.notifier);
    bool buttonState = this.buttonState(tabType, image);
    return Stack(
      children: [
        SingleChildScrollView(
          padding: EdgeInsets.only(
            top: 10,
            bottom: MediaQuery.paddingOf(context).bottom + 134,
          ),
          child: SizedBox(
            width: MediaQuery.sizeOf(context).width,
            child: Column(
              children: [
                caseShowTop
                    ? Padding(
                        padding: EdgeInsets.only(
                          left: 16.w,
                          bottom: 20.h,
                          right: 16.w,
                        ),
                        child: VideoCaseWidget(
                          listData: caseList ?? [],
                          selectedIndex: selectedIndex,
                          onTap: (index) {
                            ref
                                .read(creativeEffectsProvider.notifier)
                                .selectCase(index);
                          },
                        ),
                      )
                    : Container(),
                InkWell(
                  onTap: () {
                    ref.read(videoGenerationProvider.notifier).selectImg(1);
                  },
                  child: Container(
                    width: 343,
                    height: 343,
                    padding: EdgeInsets.only(
                        top: image != null && image != "" ? 0 : 145),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2D2C2F),
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: const Color(0xFF565656),
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: image != null && image != ""
                          ? Image.network(
                              image,
                              height: 343,
                              errorBuilder: (context, error, stackTrace) {
                                return Column(
                                  children: [
                                    SizedBox(
                                      height: 145.h,
                                    ),
                                    Image.asset(uploadIconImg,
                                        width: 27, height: 27),
                                    const SizedBox(
                                      height: 9,
                                    ),
                                    const Text(
                                      "上传参考图1",
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF8A8D93),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            )
                          : Column(
                              children: [
                                Image.asset(uploadIconImg,
                                    width: 27, height: 27),
                                const SizedBox(
                                  height: 9,
                                ),
                                const Text(
                                  "上传参考图1",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF8A8D93),
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ),
                !caseShowTop
                    ? Padding(
                        padding: EdgeInsets.only(
                          left: 16.w,
                          top: 20.h,
                          right: 16.w,
                        ),
                        child: VideoCaseWidget(
                          listData: caseList ?? [],
                          selectedIndex: selectedIndex,
                          onTap: (index) {
                            ref
                                .read(creativeEffectsProvider.notifier)
                                .selectCase(index);
                          },
                        ),
                      )
                    : Container(),
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0x2518161A),
                  Color(0xFF18161A),
                  Color(0xFF18161A)
                ],
                // stops: [0.05, 0.06, 1],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                BottomActionBar(
                  buttonText: "生成视频",
                  type: FunctionType.creativeEffectVideo.type,
                  enable: buttonState,
                  onPress: () {
                    videoGeneration.makeVideosClick();
                    // ref.read(textToVideoActionProvider.notifier).textToVideo();
                  },
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
