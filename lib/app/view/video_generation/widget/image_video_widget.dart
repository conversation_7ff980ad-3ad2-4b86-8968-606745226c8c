import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/provider/video_generation/video_generation_provider.dart';
import 'package:text_generation_video/app/repository/modals/video/video_case.dart';
import 'package:text_generation_video/app/view/video_generation/dialog/content_selection_dialog.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';
import 'package:text_generation_video/app/widgets/video_case/video_case_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

class ImageVideoWidget extends ConsumerStatefulWidget {
  const ImageVideoWidget({
    super.key,
    this.caseId,
    this.caseShowTop = false,
    this.patternType = 1,
  });
  final int? caseId;
  final bool caseShowTop;
  final int patternType;

  @override
  ConsumerState<ImageVideoWidget> createState() => ImageVideoWidgetState();
}

class ImageVideoWidgetState extends ConsumerState<ImageVideoWidget> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(videoGenerationProvider.notifier)
          .switchPatternType(widget.patternType);
    });
  }

  @override
  void dispose() {
    // 记得销毁所有控制器
    _controller.dispose();
    super.dispose();
  }

  // 热门案例
  Widget _buildCase() {
    var patternType =
        ref.watch(videoGenerationProvider.select((value) => value.patternType));
    List videoDurationData = ref.watch(
        videoGenerationProvider.select((value) => value.videoDurationData));

    List videoQualityOptions = ref.watch(
        videoGenerationProvider.select((value) => value.videoQualityOptions));

    var videoGeneration = ref.read(videoGenerationProvider.notifier);
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 0, 14, 20),
      child: VideoCaseWidget(
        caseType: patternType == 1 ? 2 : 20,
        initialCase: widget.caseId,
        onTry: (VideoCase caseData) {
          videoGeneration.getImage(caseData.caseImg1);
          if (patternType == 2) {
            videoGeneration.getImage2(caseData.caseImg2);
          }
          _controller.text = caseData.casePrompt ?? "";
          videoGeneration.getInputText(caseData.casePrompt ?? "");
          var durationIndex = videoDurationData.indexOf(caseData.duration);
          var qualityIndex = videoQualityOptions.indexOf(caseData.resolution);
          videoGeneration
              .getVideoDurationIndex(durationIndex == -1 ? 0 : durationIndex);
          videoGeneration
              .getVideoQualityIndex(qualityIndex == -1 ? 0 : qualityIndex);
        },
      ),
    );
  }

  Widget _uploadImgView(BuildContext context, WidgetRef ref) {
    var videoGeneration = ref.read(videoGenerationProvider.notifier);
    String? image =
        ref.watch(videoGenerationProvider.select((value) => value.image));
    String? image2 =
        ref.watch(videoGenerationProvider.select((value) => value.image2));
    var patternType =
        ref.watch(videoGenerationProvider.select((value) => value.patternType));
    return patternType == 1
        ? InkWell(
            onTap: () {
              videoGeneration.selectImg(patternType);
            },
            child: Container(
              width: 343,
              padding: EdgeInsets.symmetric(
                  vertical: (image != null && image != "") ? 0 : 36),
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: const Color(0xFF565656),
                  width: 1,
                ),
              ),
              child: Center(
                child: image != null && image != ""
                    ? Image.network(
                        image,
                        height: 142.h,
                        errorBuilder: (context, error, stackTrace) {
                          return Column(
                            children: [
                              Image.asset(uploadIconImg, width: 27, height: 27),
                              const SizedBox(
                                height: 9,
                              ),
                              const Text(
                                "上传图片",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF8A8D93),
                                ),
                              ),
                              const SizedBox(
                                height: 3,
                              ),
                              const Text(
                                '图片支持jpg/png，文件大小不超过10M',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Color(0xFF8A8D93),
                                ),
                              )
                            ],
                          );
                        },
                      )
                    : Column(
                        children: [
                          Image.asset(uploadIconImg, width: 27, height: 27),
                          const SizedBox(
                            height: 9,
                          ),
                          const Text(
                            "上传图片",
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF8A8D93),
                            ),
                          ),
                          const SizedBox(
                            height: 3,
                          ),
                          const Text(
                            '图片支持jpg/png，文件大小不超过10M',
                            style: TextStyle(
                              fontSize: 10,
                              color: Color(0xFF8A8D93),
                            ),
                          )
                        ],
                      ),
              ),
            ),
          )
        : DashedBorderContainer(
            dashWidth: 4,
            dashSpace: 3,
            strokeWidth: 1,
            color: const Color(0xFF565656),
            radius: 10,
            child: Container(
              width: 343,
              padding: const EdgeInsets.all(10),
              child: Column(
                children: [
                  Row(
                    children: [
                      Image.asset(uploadImage, width: 39, height: 33),
                      const SizedBox(
                        width: 10,
                      ),
                      const SizedBox(
                        width: 274,
                        child: Text(
                          "添加首尾帧图片，AI将根据提示词自动生成过渡视频图片支持jpg/png格式，文件大小不超过10M",
                          style: TextStyle(
                            fontSize: 11,
                            color: Color(0xFF7C7F86),
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          videoGeneration.selectImg(1);
                        },
                        child: imageWidget(image, '添加首帧图片'),
                      ),
                      InkWell(
                        onTap: () {
                          videoGeneration.selectImg(2);
                        },
                        child: imageWidget(image2, '添加尾帧图片'),
                      ),
                    ],
                  )
                ],
              ),
            ),
          );
  }

  Widget imageWidget(String? iamge, String text) {
    return Container(
      width: 156,
      height: 118,
      padding: EdgeInsets.only(top: iamge != null && iamge != "" ? 0 : 33),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: const Color(0xFF565656),
          width: 1,
        ),
      ),
      child: Center(
        child: iamge != null && iamge != ""
            ? Image.network(
                iamge,
                height: 118,
                errorBuilder: (context, error, stackTrace) {
                  return Column(
                    children: [
                      Image.asset(
                        uploadIconImg,
                        width: 27,
                        height: 27,
                      ),
                      const SizedBox(
                        height: 9,
                      ),
                      Text(
                        text,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF8A8D93),
                        ),
                      )
                    ],
                  );
                },
              )
            : Column(
                children: [
                  Image.asset(
                    uploadIconImg,
                    width: 27,
                    height: 27,
                  ),
                  const SizedBox(
                    height: 9,
                  ),
                  Text(
                    text,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF8A8D93),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildCreativityView(BuildContext context, WidgetRef ref) {
    var videoGeneration = ref.read(videoGenerationProvider.notifier);
    String textValue = ref.watch(
        videoGenerationProvider.select((value) => value.textValue ?? ''));
    return Column(
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 343,
          child: Row(
            children: [
              Image.asset(creativityIconImg, width: 15, height: 15),
              const SizedBox(
                width: 2,
              ),
              const Text(
                "创意描述",
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 3,
        ),
        const SizedBox(
          width: 343,
          child: Text(
            '请根据图片内容描述想要生成的画面和动作',
            style: TextStyle(
              fontSize: 10,
              color: Color(0xFF595B60),
            ),
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        Stack(
          children: [
            Container(
              width: 343,
              height: 99,
              padding: const EdgeInsets.only(
                top: 12,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                  color: const Color(0xFF565656),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  TextField(
                      controller: _controller,
                      maxLines: 3,
                      maxLength: 300,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFFFFFFFF),
                      ),
                      decoration: const InputDecoration(
                        isCollapsed: true,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                        border: InputBorder.none,
                        counterText: '',
                        hintText: '例如：一只花斑小狗，从车子后面探出头，对着路人眨眼睛',
                        hintStyle: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF8A8D93),
                        ),
                      ),
                      onChanged: (value) {
                        // 实时监听输入变化
                        videoGeneration.getInputText(value);
                      }),
                  // Text(
                  //   '例如：一只花斑小狗，从车子后面探出头，对着路人眨眼睛',
                  //   style: TextStyle(
                  //     fontSize: 12,
                  //     color: Color(0xFF8A8D93),
                  //   ),
                  // ),
                ],
              ),
            ),
            Positioned(
              right: 16,
              bottom: 12,
              child: Text(
                '清空 ${textValue.length}/300',
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF595B60),
                ),
              ),
            )
          ],
        ),
        const SizedBox(
          height: 12,
        ),
        SizedBox(
          height: 24,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            itemCount: 10,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.only(right: 12),
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                decoration: BoxDecoration(
                  color: const Color(0xFF29282B),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Text(
                  '筋斗云飞走',
                  style: TextStyle(
                    fontSize: 10,
                    color: Color(0xFF8A8D93),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _parameterSettings(BuildContext context, WidgetRef ref) {
    int videoDurationIndex = ref.watch(videoGenerationProvider
        .select((value) => value.videoDurationIndex ?? 0));
    List videoDurationData = ref.watch(
        videoGenerationProvider.select((value) => value.videoDurationData));

    int videoQualityIndex = ref.watch(videoGenerationProvider
        .select((value) => value.videoQualityIndex ?? 0));
    List videoQualityData = ref.watch(
        videoGenerationProvider.select((value) => value.videoQualityData));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(parameterIconImg, width: 15, height: 15),
            const SizedBox(
              width: 2,
            ),
            const Text(
              "参数设置",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFFFFFFFF),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 8,
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                ContentSelectionDialog.contentSelection(
                  '选择视频时长',
                  0,
                  videoDurationData,
                  videoDurationIndex,
                  (index) {
                    ref
                        .read(videoGenerationProvider.notifier)
                        .getVideoDurationIndex(index);
                  },
                );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2C2F),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: const Color(0xFF565656),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(left: 0),
                  child: Row(
                    children: [
                      Text(
                        '视频时长：${videoDurationData[videoDurationIndex] ?? 5}秒',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFFFFFFFF),
                        ),
                      ),
                      const SizedBox(
                        width: 46,
                      ),
                      Image.asset(
                        whiteArrowRight,
                        width: 7,
                        height: 10,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(
              width: 9,
            ),
            InkWell(
              onTap: () {
                ContentSelectionDialog.contentSelection(
                  '选择生成模式',
                  1,
                  videoQualityData,
                  videoQualityIndex,
                  (index) {
                    ref
                        .read(videoGenerationProvider.notifier)
                        .getVideoQualityIndex(index);
                  },
                );
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2C2F),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: const Color(0xFF565656),
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.only(left: 0),
                  child: Row(
                    children: [
                      Text(
                        '生成模式：${videoQualityData[videoQualityIndex] ?? '标准'}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFFFFFFFF),
                        ),
                      ),
                      const SizedBox(
                        width: 46,
                      ),
                      Image.asset(
                        whiteArrowRight,
                        width: 7,
                        height: 10,
                      ),
                    ],
                  ),
                ),
              ),
            )
          ],
        )
      ],
    );
  }

  tabSwitchClick(int index) {
    ref.read(videoGenerationProvider.notifier).switchPatternType(index);
    _controller.text = "";
  }

  Widget _buildModeView(BuildContext context, WidgetRef ref) {
    var patternType =
        ref.watch(videoGenerationProvider.select((value) => value.patternType));
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Image.asset(lightningIconImg, width: 15, height: 15),
            const Text(
              "模式选择",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFFFFFFFF),
              ),
            ),
          ],
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 5),
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(30),
          ),
          child: Row(
            children: [
              InkWell(
                onTap: () {
                  tabSwitchClick(1);
                },
                child: Text(
                  "普通模式",
                  style: TextStyle(
                    fontSize: 12,
                    color: patternType == 1
                        ? const Color(0xFFFFFFFF)
                        : const Color(0xFF8A8D93),
                  ),
                ),
              ),
              const SizedBox(
                width: 12,
              ),
              InkWell(
                onTap: () {
                  tabSwitchClick(2);
                },
                child: Text(
                  "首尾帧",
                  style: TextStyle(
                    fontSize: 12,
                    color: patternType == 2
                        ? const Color(0xFFFFFFFF)
                        : const Color(0xFF8A8D93),
                  ),
                ),
              ),
              const SizedBox(
                width: 12,
              ),
              InkWell(
                onTap: () {
                  ref
                      .read(videoGenerationProvider.notifier)
                      .switchPatternType(3);
                },
                child: Text(
                  "多图参考",
                  style: TextStyle(
                    fontSize: 12,
                    color: patternType == 3
                          ? const Color(0xFFFFFFFF)
                          : const Color(0xFF8A8D93),
                  ),
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  bool buttonState(tabType, image, image2, textValue, patternType) {
    bool isButtonEnabled = false;
    if (tabType == 1) {
      if (patternType == 1 &&
          image != null &&
          image != "" &&
          textValue != null &&
          textValue != "") {
        isButtonEnabled = true;
      }

      if (patternType == 2 &&
          image != null &&
          image != "" &&
          image2 != null &&
          image2 != "" &&
          textValue != null &&
          textValue != "") {
        isButtonEnabled = true;
      }
    }

    if (tabType == 3) {
      if (image != null && image != "") {
        isButtonEnabled = true;
      }
    }

    return isButtonEnabled;
  }

  @override
  Widget build(BuildContext context) {
    var patternType =
        ref.watch(videoGenerationProvider.select((value) => value.patternType));
    var image =
        ref.watch(videoGenerationProvider.select((value) => value.image));
    var image2 =
        ref.watch(videoGenerationProvider.select((value) => value.image2));
    var textValue =
        ref.watch(videoGenerationProvider.select((value) => value.textValue));
    var tabType =
        ref.watch(videoGenerationProvider.select((value) => value.tabType));
    var videoGeneration = ref.read(videoGenerationProvider.notifier);
    bool buttonState =
        this.buttonState(tabType, image, image2, textValue, patternType);
    return Stack(
      children: [
        SingleChildScrollView(
          padding: EdgeInsets.only(
            top: 10,
            bottom: MediaQuery.paddingOf(context).bottom + 134,
          ),
          child: Column(
            children: [
              !widget.caseShowTop
                  ? SizedBox(
                      width: MediaQuery.sizeOf(context).width,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 14),
                        child: _buildModeView(context, ref),
                      ),
                    )
                  : Container(),
              widget.caseShowTop ? _buildCase() : Container(),
              SizedBox(
                height: 16.h,
              ),
              _uploadImgView(context, ref),
              const SizedBox(
                height: 23,
              ),
              _buildCreativityView(context, ref),
              const SizedBox(
                height: 20,
              ),
              _parameterSettings(context, ref),
              const SizedBox(
                height: 20,
              ),
              !widget.caseShowTop ? _buildCase() : Container(),
            ],
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0x2518161A),
                  Color(0xFF18161A),
                  Color(0xFF18161A)
                ],
                // stops: [0.05, 0.06, 1],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                BottomActionBar(
                  buttonText: "生成视频",
                  enable: buttonState,
                  type: patternType == 1
                      ? FunctionType.imageToVideo.type
                      : patternType == 2
                          ? FunctionType.frameGenerationVideo.type
                          : null,
                  onPress: () {
                    videoGeneration.makeVideosClick();
                  },
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}

enum DashedBorderSide { top, right, bottom, left }

class DashedBorderContainer extends StatelessWidget {
  final Widget child;
  final double dashWidth;
  final double dashSpace;
  final double strokeWidth;
  final Color color;
  final double radius;
  final List<DashedBorderSide> sides;

  const DashedBorderContainer({
    super.key,
    required this.child,
    this.dashWidth = 5,
    this.dashSpace = 3,
    this.strokeWidth = 1,
    this.color = Colors.black,
    this.radius = 0,
    this.sides = const [
      DashedBorderSide.top,
      DashedBorderSide.right,
      DashedBorderSide.bottom,
      DashedBorderSide.left
    ],
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _DashedBorderPainter(
        dashWidth: dashWidth,
        dashSpace: dashSpace,
        strokeWidth: strokeWidth,
        color: color,
        radius: radius,
        sides: sides,
      ),
      child: Padding(
        padding: EdgeInsets.all(strokeWidth),
        child: child,
      ),
    );
  }
}

class _DashedBorderPainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final double strokeWidth;
  final Color color;
  final double radius;
  final List<DashedBorderSide> sides;

  _DashedBorderPainter({
    required this.dashWidth,
    required this.dashSpace,
    required this.strokeWidth,
    required this.color,
    required this.radius,
    required this.sides,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(radius),
      ));

    // 绘制虚线
    final PathMetric? pathMetric = path.computeMetrics().firstOrNull;
    if (pathMetric != null) {
      double start = 0;
      while (start < pathMetric.length) {
        final end = (start + dashWidth).clamp(0, pathMetric.length);
        if (sides.contains(DashedBorderSide.top) ||
            sides.contains(DashedBorderSide.right) ||
            sides.contains(DashedBorderSide.bottom) ||
            sides.contains(DashedBorderSide.left)) {
          canvas.drawPath(
            pathMetric.extractPath(start.toDouble(), end.toDouble()),
            paint,
          );
        }
        start += dashWidth + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
