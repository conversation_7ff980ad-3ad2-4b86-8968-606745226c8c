import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/config/icon_address.dart';

class ButtonWidget extends StatelessWidget {
  final Function? onTap;
  final bool disableState;
  const ButtonWidget({
    super.key,
    this.onTap,
    this.disableState = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.sizeOf(context).width,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: const BoxDecoration(
        color: Color(0xFF18161A),
      ),
      child: Column(
        children: [
          SizedBox(
            height: 14.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    inspirationIconImg,
                    width: 17.w,
                    height: 18.h,
                  ),
                  const SizedBox(
                    width: 6,
                  ),
                  const Text(
                    "本次消耗20灵感值",
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFFFFFFFF),
                    ),
                  )
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    '剩余150灵感值',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFFFFFFFF),
                    ),
                  ),
                  const SizedBox(
                    width: 6,
                  ),
                  Image.asset(whiteArrowRight, width: 5, height: 7),
                ],
              )
            ],
          ),
          const SizedBox(
            height: 17,
          ),
          InkWell(
            onTap: () {
              // debugPrint('图片有吗？$image ----$textValue');
              // videoGeneration
              // videoGeneration.makeVideosClick();
              if (!disableState) {
                onTap?.call();
              }
            },
            child: Container(
              width: 343,
              padding: const EdgeInsets.symmetric(
                vertical: 16,
              ),
              decoration: BoxDecoration(
                color: !disableState
                    ? const Color(0xFF30E6B8)
                    : const Color(0xFF236F5E),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: Text(
                  '生成视频',
                  softWrap: false,
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF18161A),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 38,
          ),
        ],
      ),
    );
  }
}
