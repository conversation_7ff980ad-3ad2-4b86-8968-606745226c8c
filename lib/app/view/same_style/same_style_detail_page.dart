import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_example.dart';
import 'package:text_generation_video/app/view/same_style/widget/same_style_example_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';

class SameStyleDetailPage extends ConsumerStatefulWidget {
  const SameStyleDetailPage({super.key, this.data});

  final Map? data;

  @override
  SameStyleDetailPageState createState() => SameStyleDetailPageState();
}

class SameStyleDetailPageState extends ConsumerState<SameStyleDetailPage> {
  @override
  Widget build(BuildContext context) {
    var sameData = widget.data?["sameData"] as SameExample?;
    var tag = widget.data?["tag"];
    debugPrint("SameStyleDetailPageState-tag: $tag");
    return Scaffold(
      backgroundColor: const Color(0xFF18161A),
      body: Column(
        children: [
          Expanded(
            child: SameStyleExampleWidget(sameExample: sameData),
          ),
          _buildBottomAction(context, sameData),
        ],
      ),
    );
  }

  Widget _buildBottomAction(BuildContext context, SameExample? sameData) {
    return Container(
      color: const Color(0xFF18161A),
      padding: EdgeInsets.only(
        top: 10,
        bottom: MediaQuery.paddingOf(context).bottom + 20,
      ),
      child: Row(
        children: [
          const SizedBox(width: 16),
          SizedBox(
            width: 109.w,
            height: 51,
            child: GradientButton(
              onPress: () {
                context.pop();
              },
              radius: 16,
              border: Border.all(color: const Color(0xFF646464), width: 0.2),
              shadow: false,
              gradient: const LinearGradient(
                colors: [Color(0xFF29282B), Color(0xFF29282B)],
              ),
              child: const Text(
                "返回",
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF32E7BB),
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: SizedBox(
              height: 51,
              child: GradientButton(
                onPress: () {
                  var functionType = sameData?.functionType;
                  var caseId = sameData?.caseId;
                  ref
                      .read(sameStyleActionProvider.notifier)
                      .sameAction(functionType, caseId: caseId);
                },
                radius: 16,
                shadow: false,
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF53F9DF),
                    Color(0xFF40EFCA),
                    Color(0xFF34E8BC),
                    Color(0xFF30E6B8),
                  ],
                  stops: [0.0, 0.1, 0.3, 1.0],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                child: const Text(
                  "做同款",
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF18161A),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
    );
  }
}
