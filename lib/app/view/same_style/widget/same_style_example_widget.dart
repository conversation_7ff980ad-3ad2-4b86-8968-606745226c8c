import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../repository/modals/same_style/same_example.dart';
import '../../../widgets/video_play/video_play_widget.dart';

class SameStyleExampleWidget extends ConsumerWidget {
  const SameStyleExampleWidget({
    super.key,
    required this.sameExample,
  });

  final SameExample? sameExample;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // SizedBox(
    //   height: 500,
    //   child: AwarePageView(
    //     itemCount: 1,
    //     itemBuilder: (context, index) {
    //       return Container(
    //         color: index % 2 == 0 ? Colors.amber : Colors.red,
    //       );
    //     },
    //   ),
    // ),
    //
    // var vUrl =
    //     "https://cdn.camera.msmds.cn/aivideo/texttovideo/02175757849574800000000000000000000ffffac1450bf2c9ddc.mp4";
    // return VideoPlayWidget(
    //   videoUrl: vUrl,
    //   firstFrameImage: sameExample?.previewImgUrl,
    // );
    if (sameExample?.dataType == 2) {
      // 图片
      return CachedNetworkImage(
        imageUrl: sameExample?.exampleData ?? "",
        width: MediaQuery.sizeOf(context).width,
        fit: BoxFit.fitWidth,
        placeholder: (c, s) {
          return const SizedBox(height: 120);
        },
        errorWidget: (c, o, s) {
          return const SizedBox(height: 120);
        },
      );
    }
    if (sameExample?.dataType == 3) {
      // 视频
      return VideoPlayWidget(
        videoUrl: sameExample?.exampleData ?? "",
        firstFrameImage: sameExample?.previewImgUrl,
      );
    }
    return SizedBox(height: MediaQuery.paddingOf(context).top);
  }
}
