import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:text_generation_video/config/icon_address.dart';

class ExampleDialog {
  static Future<bool?> example(
    String? imageUrl,
    Function()? onTap,
  ) async {
    return await SmartDialog.show(
      keepSingle: true,
      clickMaskDismiss: false,
      tag: "example_dialog",
      builder: (contexts) {
        return Column(
          children: [
            Expanded(
              child: InkWell(
                onTap: () {
                  SmartDialog.dismiss(tag: "example_dialog");
                },
                child: const Expanded(
                  child: Text(""),
                ),
              ),
            ),
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  width: 375.w,
                  height: 410.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFF222123),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(14.r),
                      topRight: Radius.circular(14.r),
                    ),
                  ),
                ),
                Positioned(
                  top: -44.h,
                  left: 62.w,
                  child: imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(28.r), // 设置圆角
                          child: Image.network(
                            imageUrl,
                            width: 251.w,
                            height: 337.h,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Container(
                          width: 251.w,
                          height: 337,
                          margin: EdgeInsets.only(bottom: 100.h),
                          decoration: BoxDecoration(
                            color: const Color(0xFF545259),
                            borderRadius: BorderRadius.circular(28.r),
                          ),
                        ),
                ),
                Positioned(
                  top: 321.h,
                  left: 16.w,
                  child: InkWell(
                    onTap: () {
                      SmartDialog.dismiss(tag: "example_dialog");
                      onTap!();
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 150.w,
                        vertical: 16.h,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF30E6B8),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Text(
                        "去使用",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF18161A),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 16.h,
                  right: 16.w,
                  child: InkWell(
                    onTap: () {
                      SmartDialog.dismiss(tag: "example_dialog");
                    },
                    child: Image.asset(
                      exampleCloseImg,
                      width: 28.w,
                      height: 28.h,
                    ),
                  ),
                )
              ],
            )
          ],
        );
      },
    );
  }
}
