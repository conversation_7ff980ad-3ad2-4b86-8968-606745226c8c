import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/fashion_shoot/fashion_shoot_provider.dart';
import 'package:text_generation_video/app/view/fashion_shoot/dialog/example_dialog.dart';
import 'package:text_generation_video/config/icon_address.dart';

class ChooseClothingPage extends ConsumerWidget {
  const ChooseClothingPage({super.key});

  Widget headView(BuildContext context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      height: 44.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 20.w),
            child: Padding(
              padding: const EdgeInsets.only(left: 20),
              child: InkWell(
                onTap: () {
                  context.pop();
                },
                child: Image.asset(whiteArrowLeft, width: 10, height: 18),
              ),
            ),
          ),
          Row(
            children: [
              Text(
                "选择服装",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFFFFFFFF),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(
            width: 30.w,
          )
        ],
      ),
    );
  }

  Widget templateView(BuildContext context, WidgetRef ref) {
    var imageData = ref
        .watch(clothesListCaseProvider.select((state) => state.cacheImageData));
    var stateType = ref.watch(fashionShootProvider).stateType;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "我的服装",
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFFFFFFFF),
          ),
        ),
        SizedBox(
          height: 8.h,
        ),
        Wrap(
          spacing: 4.w, // 元素之间的水平间距
          runSpacing: 4.h, // 换行后的垂直间距
          children: [
            InkWell(
              onTap: () {
                ref.read(clothesListCaseProvider.notifier).selectImg();
              },
              child: Container(
                width: 109.w,
                height: 146.h,
                // padding: EdgeInsets.only(
                //   top: iamge != null && iamge != "" ? 0 : 137,
                // ),
                decoration: BoxDecoration(
                  color: const Color(0x3D2D2C2F),
                  borderRadius: BorderRadius.circular(14),
                  border: Border.all(
                    color: const Color(0xFF565656),
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        uploadIconImg,
                        width: 22.w,
                        height: 22.h,
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      Text(
                        "上传服装",
                        style: TextStyle(
                          fontSize: 9.6.sp,
                          color: const Color(0xFF8A8D93),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            ...imageData.map(
              (item) {
                return InkWell(
                  onTap: () {
                    ExampleDialog.example(
                      item,
                      () {
                        ref
                            .read(fashionShootProvider.notifier)
                            .getModelImage(item, stateType);
                        context.pop();
                      },
                    );
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: Image.network(
                      item,
                      width: 109.w,
                      height: 146.h,
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              },
            )
          ],
        )
      ],
    );
  }

  Widget tabView(BuildContext context, WidgetRef ref) {
    List classTabData = ref
        .watch(clothesListCaseProvider.select((state) => state.classTabData));
    int classTabIndex = ref
        .watch(clothesListCaseProvider.select((state) => state.classTabIndex));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "服装样例",
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFFFFFFFF),
          ),
        ),
        SizedBox(
          height: 8.h,
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // classTabData
            SizedBox(
              width: 343.w,
              // 必须指定高度，因为横向 ListView 在垂直方向是收缩的
              height: 25.h,
              child: ListView(
                scrollDirection: Axis.horizontal, // 关键属性：设置为水平方向
                children: classTabData.map((item) {
                  int index = classTabData.indexOf(item);
                  return Padding(
                    padding: EdgeInsets.only(right: 6.w),
                    child: GestureDetector(
                      onTap: () {
                        ref
                            .read(clothesListCaseProvider.notifier)
                            .getClassTabIndex(index);
                      },
                      child: Container(
                        height: 25.h,
                        padding: EdgeInsets.symmetric(
                          horizontal: 11.w,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF29282B),
                          borderRadius: BorderRadius.circular(50.r),
                          border: Border.all(
                            color: classTabIndex == index
                                ? const Color(0xFF979797)
                                : const Color(0xFF29282B),
                            width: 1.w,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            "$item",
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: const Color(0xFF8A8D93),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        )
      ],
    );
  }

  Widget clothingView(BuildContext context, WidgetRef ref) {
    List classCaseData = ref
        .watch(clothesListCaseProvider.select((state) => state.classCaseData));
    int stateType =
        ref.watch(fashionShootProvider.select((state) => state.stateType));
    return Wrap(
      spacing: 4.w, // 元素之间的水平间距
      runSpacing: 4.h, // 换行后的垂直间距
      children: classCaseData.map(
        (e) {
          return InkWell(
            onTap: () {
              ExampleDialog.example(
                e.imageUrl,
                () {
                  ref
                      .read(fashionShootProvider.notifier)
                      .getModelImage(e.imageUrl, stateType);
                  context.pop();
                },
              );
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: Image.network(
                e.imageUrl,
                width: 109.w,
                height: 146.h,
                fit: BoxFit.cover,
              ),
            ),
          );
        },
      ).toList(),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      color: const Color(0xFF18161A),
      child: Stack(
        children: [
          Image.asset(
            fashionHeadBjImg,
            width: MediaQuery.sizeOf(context).width,
            // height: 197,
          ),
          Positioned(
            top: 44.h,
            child: headView(context),
          ),
          Container(
            margin: EdgeInsets.only(top: 94.h),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          templateView(context, ref),
                          SizedBox(
                            height: 20.h,
                          ),
                          tabView(context, ref),
                          SizedBox(
                            height: 10.h,
                          ),
                          clothingView(context, ref),
                          SizedBox(
                            height: 40.h,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
