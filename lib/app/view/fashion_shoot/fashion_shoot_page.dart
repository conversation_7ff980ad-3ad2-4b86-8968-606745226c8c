import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/fashion_shoot/fashion_shoot_provider.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';
import 'package:text_generation_video/app/view/fashion_shoot/dialog/example_dialog.dart';
import 'package:text_generation_video/app/view/video_generation/widget/button_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/work_record_ation.dart';
import 'package:text_generation_video/config/icon_address.dart';

class FashionShootPage extends ConsumerWidget {
  const FashionShootPage({super.key});

  Widget headView(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      title: Text(
        "服装商拍",
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.white,
        ),
      ),
      leading: const Leading(
        color: Color(0xFFFFFFFF),
      ),
      actions: [
        WorkRecordAction(workType: WorkRecordType.image.type),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget imageView(
    String? image,
    String? titleText, {
    deleteTap,
    selectTap,
  }) {
    return Container(
      width: 143.w,
      height: 166.h,
      // padding: EdgeInsets.only(
      //   top: iamge != null && iamge != "" ? 0 : 137,
      // ),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(
          color: const Color(0xFF565656),
          width: 1,
        ),
      ),
      child: image != null && image != ""
          ? Stack(
              children: [
                Positioned(
                  top: 10.h,
                  child: Image.network(
                    image,
                    width: 143.w,
                    height: 130.h,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  child: Container(
                    width: 141.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFF18161A),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(14.r),
                        bottomRight: Radius.circular(14.r),
                      ),
                    ),
                    child: Row(
                      // mainAxisAlignment:
                      //     MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              if (selectTap is Function) {
                                selectTap();
                              }
                            },
                            child: Center(
                              child: Text(
                                "替换",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: const Color(0xFFFFFFFF),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Container(
                          width: 1.w,
                          height: 10.h,
                          color: const Color(0xFFFFFFFF),
                        ),
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              if (deleteTap is Function) {
                                deleteTap();
                              }
                            },
                            child: Center(
                              child: Text(
                                "删除",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: const Color(0xFFFFFFFF),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              ],
            )
          : InkWell(
              onTap: () {
                if (selectTap is Function) {
                  selectTap();
                }
              },
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      uploadIconImg,
                      width: 27,
                      height: 27,
                    ),
                    const SizedBox(
                      height: 9,
                    ),
                    Text(
                      "$titleText",
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF8A8D93),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget templateView(BuildContext context, WidgetRef ref) {
    var modelImage =
        ref.watch(fashionShootProvider.select((state) => state.modelImage));
    var garmentsType =
        ref.watch(fashionShootProvider.select((state) => state.garmentsType));
    var fashionShoot = ref.read(fashionShootProvider.notifier);
    var clothingImage =
        ref.watch(fashionShootProvider.select((state) => state.clothingImage));
    var bottomWearImage = ref
        .watch(fashionShootProvider.select((state) => state.bottomWearImage));
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 151.w,
              height: 383.h,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(clothingBjImg),
                ),
              ),
              child: Column(
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 7.5.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "服装",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        ),
                        Container(
                          height: 28.h,
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          decoration: BoxDecoration(
                            color: const Color(0x21FFFFFF),
                            borderRadius: BorderRadius.circular(14),
                          ),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  fashionShoot.getGarmentsType(1);
                                },
                                child: Text(
                                  "一件",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: garmentsType == 1
                                        ? const Color(0xFFFFFFFF)
                                        : const Color(0xFFAAAAAA),
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: 10.w,
                              ),
                              InkWell(
                                onTap: () {
                                  fashionShoot.getGarmentsType(2);
                                },
                                child: Text(
                                  "两件",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: garmentsType == 2
                                        ? const Color(0xFFFFFFFF)
                                        : const Color(0xFFAAAAAA),
                                  ),
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  garmentsType == 1
                      ? Container(
                          width: 143.w,
                          height: 337.h,
                          // padding: EdgeInsets.only(
                          //   top: iamge != null && iamge != "" ? 0 : 137,
                          // ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF2D2C2F),
                            borderRadius: BorderRadius.circular(14),
                            border: Border.all(
                              color: const Color(0xFF565656),
                              width: 1,
                            ),
                          ),
                          child: clothingImage != null && clothingImage != ""
                              ? Stack(
                                  children: [
                                    Positioned(
                                      top: 32.h,
                                      child: Image.network(
                                        clothingImage,
                                        width: 143.w,
                                        height: 275.h,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Positioned(
                                      bottom: 0,
                                      child: Container(
                                        width: 141.w,
                                        height: 30.h,
                                        decoration: BoxDecoration(
                                          color: const Color(0xFF18161A),
                                          borderRadius: BorderRadius.only(
                                            bottomLeft: Radius.circular(14.r),
                                            bottomRight: Radius.circular(14.r),
                                          ),
                                        ),
                                        child: Row(
                                          // mainAxisAlignment:
                                          //     MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: InkWell(
                                                onTap: () {
                                                  fashionShoot.getStateType(0);
                                                  context.push(
                                                    '/$chooseClothingPage',
                                                  );
                                                },
                                                child: Center(
                                                  child: Text(
                                                    "替换",
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      color: const Color(
                                                          0xFFFFFFFF),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              width: 1.w,
                                              height: 10.h,
                                              color: const Color(0xFFFFFFFF),
                                            ),
                                            Expanded(
                                              child: InkWell(
                                                onTap: () {
                                                  fashionShoot.getModelImage(
                                                    '',
                                                    0,
                                                  );
                                                },
                                                child: Center(
                                                  child: Text(
                                                    "删除",
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      color: const Color(
                                                          0xFFFFFFFF),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                  ],
                                )
                              : InkWell(
                                  onTap: () {
                                    fashionShoot.getStateType(0);
                                    context.push(
                                      '/$chooseClothingPage',
                                    );
                                  },
                                  child: Center(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Image.asset(
                                          uploadIconImg,
                                          width: 27,
                                          height: 27,
                                        ),
                                        const SizedBox(
                                          height: 9,
                                        ),
                                        Text(
                                          "上传服装",
                                          style: TextStyle(
                                            fontSize: 12.sp,
                                            color: const Color(0xFF8A8D93),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                        )
                      : Column(
                          children: [
                            imageView(
                              clothingImage,
                              '选择上装',
                              selectTap: () {
                                fashionShoot.getStateType(0);
                                context.push('/$chooseClothingPage');
                              },
                              deleteTap: () {
                                fashionShoot.getModelImage('', 0);
                              },
                            ),
                            SizedBox(
                              height: 5.h,
                            ),
                            imageView(
                              bottomWearImage,
                              '选择下装',
                              selectTap: () {
                                fashionShoot.getStateType(1);
                                context.push('/$chooseClothingPage');
                              },
                              deleteTap: () {
                                fashionShoot.getModelImage('', 1);
                              },
                            ),
                          ],
                        )
                ],
              ),
            ),
            SizedBox(
              width: 8.w,
            ),
            Container(
              width: 184.w,
              height: 383.h,
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(modelBjImg),
                ),
              ),
              child: Column(
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "模特",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 176.w,
                    height: 337.h,
                    // padding: EdgeInsets.only(
                    //   top: iamge != null && iamge != "" ? 0 : 137,
                    // ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2D2C2F),
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: const Color(0xFF565656),
                        width: 1,
                      ),
                    ),
                    child: modelImage != null && modelImage != ""
                        ? Stack(
                            children: [
                              Positioned(
                                top: 32.h,
                                child: Image.network(
                                  modelImage,
                                  width: 176.w,
                                  height: 275.h,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Positioned(
                                bottom: 0,
                                child: Container(
                                  width: 174.w,
                                  height: 30.h,
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF18161A),
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(14.r),
                                      bottomRight: Radius.circular(14.r),
                                    ),
                                  ),
                                  child: Row(
                                    // mainAxisAlignment:
                                    //     MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: InkWell(
                                          onTap: () {
                                            fashionShoot.getStateType(2);
                                            context.push(
                                              '/$chooseClothingPage',
                                            );
                                          },
                                          child: Center(
                                            child: Text(
                                              "替换",
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: const Color(0xFFFFFFFF),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      Container(
                                        width: 1.w,
                                        height: 10.h,
                                        color: const Color(0xFFFFFFFF),
                                      ),
                                      Expanded(
                                        child: InkWell(
                                          onTap: () {
                                            fashionShoot.getModelImage(
                                              '',
                                              2,
                                            );
                                          },
                                          child: Center(
                                            child: Text(
                                              "删除",
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                color: const Color(0xFFFFFFFF),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          )
                        : InkWell(
                            onTap: () {
                              fashionShoot.getStateType(2);
                              context.push(
                                '/$chooseClothingPage',
                              );
                            },
                            child: Center(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Image.asset(
                                    uploadIconImg,
                                    width: 27,
                                    height: 27,
                                  ),
                                  const SizedBox(
                                    height: 9,
                                  ),
                                  Text(
                                    "选择模特",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: const Color(0xFF8A8D93),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                  )
                ],
              ),
            )
          ],
        )
      ],
    );
  }

  Widget modelProtection(BuildContext context, WidgetRef ref) {
    bool keepUpper =
        ref.watch(fashionShootProvider.select((state) => state.keepUpper));
    bool keepLower =
        ref.watch(fashionShootProvider.select((state) => state.keepLower));
    bool keepFoot =
        ref.watch(fashionShootProvider.select((state) => state.keepFoot));
    bool keepHand =
        ref.watch(fashionShootProvider.select((state) => state.keepHand));
    var fashionShoot = ref.read(fashionShootProvider.notifier);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "模特保护",
          style: TextStyle(
            fontSize: 16.sp,
            color: const Color(0xFFFFFFFF),
          ),
        ),
        SizedBox(
          height: 10.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            selectView(
              textTitle: '上装',
              isSelect: keepUpper,
              onTap: () {
                fashionShoot.getModelProtection(0);
              },
            ),
            selectView(
              textTitle: '下装',
              isSelect: keepLower,
              onTap: () {
                fashionShoot.getModelProtection(1);
              },
            ),
            selectView(
              textTitle: '脚部',
              isSelect: keepFoot,
              onTap: () {
                fashionShoot.getModelProtection(2);
              },
            ),
            selectView(
              textTitle: '手部',
              isSelect: keepHand,
              onTap: () {
                fashionShoot.getModelProtection(3);
              },
            ),
          ],
        )
      ],
    );
  }

  Widget selectView({
    String? textTitle,
    bool isSelect = false,
    Function? onTap,
  }) {
    return InkWell(
      onTap: () {
        onTap!();
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 12.h,
        ),
        decoration: BoxDecoration(
          color: const Color(0xFF2D2C2F),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            isSelect
                ? Image.asset(
                    tickImg,
                    width: 16.w,
                    height: 16.w,
                  )
                : Container(
                    width: 16.w,
                    height: 16.w,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xFF979797),
                        width: 1.w,
                      ),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
            SizedBox(
              width: 8.w,
            ),
            Text(
              "$textTitle",
              style: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFFFFFFFF),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget digitalModel(BuildContext context, WidgetRef ref) {
    List caseList =
        ref.watch(clothesListCaseProvider.select((state) => state.caseList));
    var fashionShoot = ref.read(fashionShootProvider.notifier);
    var modelImage =
        ref.watch(fashionShootProvider.select((state) => state.modelImage));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              "数字模特",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFFFFFFFF),
              ),
            ),
            InkWell(
              onTap: () {
                fashionShoot.getStateType(2);
                context.push('/$chooseClothingPage');
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "全部",
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFFFFFFFF),
                    ),
                  ),
                  SizedBox(
                    width: 7.w,
                  ),
                  Image.asset(
                    whiteArrowRight,
                    width: 4.w,
                    height: 7.h,
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
        SizedBox(
          // 必须指定高度，因为横向 ListView 在垂直方向是收缩的
          height: 130.h,
          child: ListView(
            scrollDirection: Axis.horizontal, // 关键属性：设置为水平方向
            children: caseList.map((item) {
              // int index = caseList.indexOf(item);
              return Padding(
                padding: EdgeInsets.only(right: 6.w),
                child: GestureDetector(
                  onTap: () {
                    ExampleDialog.example(
                      item.imageUrl,
                      () {
                        ref
                            .read(fashionShootProvider.notifier)
                            .getModelImage(item.imageUrl, 2);
                      },
                    );
                  },
                  child: Container(
                    width: 85.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.r),
                      border: Border.all(
                        color: modelImage == item.imageUrl
                            ? const Color(0xff30e6b8)
                            : Colors.transparent,
                        width: 2.w,
                      ),
                      image: DecorationImage(
                        image: NetworkImage(item.imageUrl ?? ""),
                        fit: BoxFit.cover,
                      ),
                    ),
                    child: const Column(
                      children: [
                        SizedBox(),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var garmentsType =
        ref.watch(fashionShootProvider.select((state) => state.garmentsType));
    var clothingImage =
        ref.watch(fashionShootProvider.select((state) => state.clothingImage));
    var bottomWearImage = ref
        .watch(fashionShootProvider.select((state) => state.bottomWearImage));
    var modelImage =
        ref.watch(fashionShootProvider.select((state) => state.modelImage));
    bool state = true;
    if (garmentsType == 1 &&
        clothingImage != null &&
        clothingImage != "" &&
        modelImage != null &&
        modelImage != "") {
      state = false;
    }
    if (garmentsType == 2 &&
        clothingImage != null &&
        clothingImage != "" &&
        modelImage != null &&
        modelImage != "" &&
        bottomWearImage != null &&
        bottomWearImage != "") {
      state = false;
    }
    return Container(
      color: const Color(0xFF18161A),
      child: Stack(
        children: [
          Image.asset(
            fashionHeadBjImg,
            width: MediaQuery.sizeOf(context).width,
            // height: 197,
          ),
          headView(context),
          Container(
            margin: EdgeInsets.only(top: 94.h),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        templateView(context, ref),
                        SizedBox(
                          height: 20.h,
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: modelProtection(context, ref),
                        ),
                        SizedBox(
                          height: 20.h,
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: digitalModel(context, ref),
                        ),
                        SizedBox(
                          height: 40.h,
                        ),
                      ],
                    ),
                  ),
                ),
                ButtonWidget(
                  disableState: state,
                  onTap: () {
                    ref.read(fashionShootProvider.notifier).imageSubmit();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
