import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/account/auth_provider.dart';
import 'package:text_generation_video/app/provider/digital/digital_video_provider.dart';
import 'package:text_generation_video/app/provider/home/<USER>';
import 'package:text_generation_video/app/provider/member/member_provider.dart';
import 'package:text_generation_video/app/view/home/<USER>/me_refresh_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/production_tab_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/video_record_item_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/platform_util.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

class MeWidget extends ConsumerWidget {
  const MeWidget({super.key});

  /// 用户信息
  Widget _buildUserInfo(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    // var powerAccount = ref.watch(userPowerBalanceProvider);
    Widget info = InkWell(
      onTap: () {
        context.go("/$login");
      },
      child: Text(
        "登录/注册",
        style: TextStyle(
          fontSize: 18.sp,
          color: Colors.white,
        ),
      ),
    );
    if (userData != null) {
      info = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${userData.username}",
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.white,
            ),
          ),
          // InkWell(
          //   onTap: () {
          //     context.push('/$computePowerPage');
          //   },
          //   child: Container(
          //     margin: const EdgeInsets.only(top: 5),
          //     padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 5),
          //     decoration: BoxDecoration(
          //       color: const Color(0xFFF2F2F3),
          //       borderRadius: BorderRadius.circular(13),
          //     ),
          //     child: Row(
          //       children: [
          //         Image.asset(
          //           powerIcon,
          //           width: 18,
          //           height: 18,
          //         ),
          //         const SizedBox(width: 4),
          //         Text(
          //           "算力：${powerAccount?.powerBalance ?? 0}>",
          //           style: const TextStyle(
          //             fontSize: 16,
          //             color: Color(0xFF000000),
          //           ),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
        ],
      );
    }
    return Container(
      margin: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
      child: Stack(
        children: [
          // Positioned(
          //   top: 10.h,
          //   right: 24.w,
          //   child: InkWell(
          //     onTap: () {
          //       context.go("/$settingPage");
          //     },
          //     child: Image.asset(
          //       setting,
          //       width: 24.w,
          //       height: 22.h,
          //     ),
          //   ),
          // ),
          Padding(
            padding: EdgeInsets.only(top: 12.h),
            child: Row(
              children: [
                const SizedBox(width: 10),
                Image.asset(
                  avatar,
                  width: 48,
                  height: 48,
                ),
                const SizedBox(width: 15),
                info,
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 会员
  Widget _buildMembership(BuildContext context, WidgetRef ref) {
    if (PlatformUtils.isAndroid) {
      return const SizedBox(height: 30);
    }
    String memberTitle = "ZenAI会员";
    var member = ref.watch(memberInfoProvider);
    var memberType = 0;
    if (member?.hasExpire == true) {
      //过期
      memberType = 2;
      memberTitle = "ZenAI会员(已过期)";
    } else if (member?.hasExpire == false) {
      //会员
      memberType = 1;
    }

    // 背景
    String bg = membershipBg;
    // if (memberType == 0 || memberType == 2) {
    //   bg = membershipBg;
    // } else if (memberType == 1) {
    //   bg = memberVipBg;
    // }

    // 会员状态
    Widget memberState = ShaderMask(
      shaderCallback: (Rect bounds) {
        return const LinearGradient(
          colors: [Color(0xFFC5A57D), Color(0xFF907650)],
        ).createShader(
          Rect.fromLTWH(0, 0, bounds.width, bounds.height),
        );
      },
      child: Text(
        memberTitle,
        style: TextStyle(
          fontSize: 13.sp,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
    // if (memberType == 0 || memberType == 2) {
    //   // 非会员或者过期
    //   memberState = Row(
    //     children: [
    //       Image.asset(
    //         membershipBag,
    //         width: 24.w,
    //         height: 17.h,
    //       ),
    //       Padding(padding: EdgeInsets.only(right: 10.w)),
    //       Text(
    //         memberType == 0 ? '会员VIP' : '会员VIP已过期',
    //         style: TextStyle(
    //           fontSize: 14.sp,
    //           color: Colors.black,
    //         ),
    //       ),
    //     ],
    //   );
    // } else if (memberType == 1) {
    //   // 会员
    //   memberState = Row(
    //     children: [
    //       ,
    //     ],
    //   );
    // }

    // 会员有效期
    Widget memberDur = const SizedBox();
    if (memberType == 1 || memberType == 2) {
      // 已开通会员或者会员过期
      memberDur = Text(
        "会员有效期至${member?.expireTime}",
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF5B4727),
        ),
      );
    } else if (memberType == 0) {
      memberDur = Text(
        "开通会员解锁全部权益",
        style: TextStyle(
          fontSize: 10.sp,
          color: const Color(0xFF5B4727),
        ),
      );
    }

    // 开通按钮或者恢复订阅
    Widget buyButton = const SizedBox();
    if (memberType == 0 || memberType == 2) {
      // 非会员或者过期
      buyButton = GradientButton(
        onPress: () {},
        radius: 15.h,
        border: Border.all(color: const Color(0xFFF0CE97), width: 1),
        gradient: const LinearGradient(
          colors: [Color(0xFFFDEFD8), Color(0xFFFDECD2)],
        ),
        child: Text(
          memberType == 0 ? '立即开通' : '恢复订阅',
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF2E251A),
          ),
        ),
      );
    } else if (memberType == 1) {
      buyButton = GradientButton(
        onPress: () {
          RouterUtil.checkLogin(context, call: () {
            ref.read(homeProvider.notifier).jumpToPage(0);
          });
        },
        radius: 15.h,
        border: Border.all(color: const Color(0xFFF0CE97), width: 1),
        gradient: const LinearGradient(
          colors: [Color(0xFFFDEFD8), Color(0xFFFDECD2)],
        ),
        child: Text(
          "已开通",
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF2E251A),
          ),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.only(top: 18.h, bottom: 32.h),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(bg, width: MediaQuery.sizeOf(context).width - 20),
          SizedBox(
            width: MediaQuery.sizeOf(context).width - 20,
            child: Row(
              children: [
                SizedBox(width: 67.w),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      memberState,
                      const Padding(padding: EdgeInsets.only(bottom: 1)),
                      memberDur,
                    ],
                  ),
                ),
                SizedBox(
                  width: 76.w,
                  height: 24.h,
                  child: buyButton,
                ),
                SizedBox(width: 12.w),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 操作菜单
  Widget _buildMenus(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        _buildMenuItem(powerDetailIcon, "算力明细", () {
          RouterUtil.checkLogin(context, call: () {
            context.push('/$computePowerPage');
          });
        }),
        _buildMenuItem(concatUsIcon, "联系我们", () {
          context.push("/$contactUsPage");
        }),
        _buildMenuItem(meAboutIcon, "关于", () {
          context.push("/$aboutUsPage");
        }),
        _buildMenuItem(setting, "设置", () {
          context.go("/$settingPage");
        }),
      ],
    );
  }

  Widget _buildMenuItem(String icon, String title, Function() onTap) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Column(
          children: [
            Image.asset(icon, width: 20, height: 20),
            const SizedBox(height: 7),
            Text(
              title,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 管理编辑
  // Widget _buildManager(WidgetRef ref) {
  //   var manager = ref.watch(deleteProductProvider);
  //   Widget child;
  //   if (manager) {
  //     child = Row(
  //       children: [
  //         InkWell(
  //           onTap: () {
  //             ref.read(deleteProductProvider.notifier).setManagerState(false);
  //           },
  //           child: Row(
  //             children: [
  //               Text(
  //                 "取消",
  //                 style: TextStyle(
  //                   fontSize: 14.sp,
  //                   color: const Color(0xFF333333),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ],
  //     );
  //   } else {
  //     child = Row(
  //       children: [
  //         InkWell(
  //           onTap: () {
  //             ref.read(deleteProductProvider.notifier).setManagerState(true);
  //           },
  //           child: Row(
  //             children: [
  //               Image.asset(
  //                 managerIcon,
  //                 width: 12.w,
  //                 height: 13.h,
  //               ),
  //               Padding(padding: EdgeInsets.only(right: 3.w)),
  //               Text(
  //                 "管理",
  //                 style: TextStyle(
  //                   fontSize: 14.sp,
  //                   color: const Color(0xFF333333),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //         Padding(padding: EdgeInsets.only(right: 29.w)),
  //         InkWell(
  //           onTap: () {
  //             ref
  //                 .read(creationRecordProvider.notifier)
  //                 .loadData(showRefreshToast: true);
  //           },
  //           child: Row(
  //             children: [
  //               Image.asset(
  //                 refreshIcon,
  //                 width: 12.w,
  //                 height: 13.h,
  //               ),
  //               Padding(padding: EdgeInsets.only(right: 3.w)),
  //               Text(
  //                 "刷新",
  //                 style: TextStyle(
  //                   fontSize: 14.sp,
  //                   color: const Color(0xFF333333),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ],
  //     );
  //   }
  //   return Container(
  //     padding: EdgeInsets.only(left: 15.w, right: 20.w, bottom: 18.h),
  //     child: Row(
  //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //       children: [
  //         Text(
  //           "我的作品",
  //           style: TextStyle(
  //             fontSize: 17.sp,
  //             fontWeight: FontWeight.w600,
  //             color: Colors.black,
  //           ),
  //         ),
  //         child,
  //       ],
  //     ),
  //   );
  // }

  /// 作品item
  // Widget _buildCreationItem(
  //   WidgetRef ref,
  //   BuildContext context,
  //   CreationResult creationResult,
  // ) {
  //   /// 编辑状态
  //   var manager = ref.watch(deleteProductProvider);
  //   var deleteList = ref.watch(deleteProductListProvider);
  //   var deleteItem =
  //       deleteList.indexWhere((element) => element.id == creationResult.id);
  //
  //   /// 是否在删除列表
  //   var inDeleteList = deleteItem != -1;
  //   Widget select = Container();
  //   if (manager) {
  //     select = Container(
  //       width: 12.w,
  //       height: 12.w,
  //       decoration: BoxDecoration(
  //         color: const Color(0xFFFFFFFF),
  //         borderRadius: BorderRadius.circular(6.w),
  //         border: Border.all(color: const Color(0xFFD3D3D3)),
  //       ),
  //     );
  //     if (inDeleteList) {
  //       select = Container(
  //         width: 12.w,
  //         height: 12.w,
  //         decoration: BoxDecoration(
  //           color: const Color(0xFF3072FF),
  //           borderRadius: BorderRadius.circular(6.w),
  //         ),
  //         child: Icon(
  //           Icons.check,
  //           size: 8.w,
  //           color: Colors.white,
  //         ),
  //       );
  //     }
  //   }
  //
  //   if (creationResult.generationState == 18) {
  //     /// 生成失败
  //     return InkWell(
  //       onTap: () {
  //         if (manager) {
  //           /// 选中或者取消选中
  //           ref
  //               .read(deleteProductListProvider.notifier)
  //               .updateDeleteList(creationResult);
  //         }
  //       },
  //       child: Stack(
  //         children: [
  //           Container(
  //             width: 109.w,
  //             height: 119.h,
  //             decoration: BoxDecoration(
  //               color: const Color(0xFFF2F2F3),
  //               borderRadius: BorderRadius.circular(10.r),
  //             ),
  //             child: Column(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 Image.asset(
  //                   creationFail,
  //                   width: 30.w,
  //                   height: 27.h,
  //                 ),
  //                 Padding(padding: EdgeInsets.only(bottom: 2.h)),
  //                 Text(
  //                   "${creationResult.generationState}",
  //                   style: TextStyle(
  //                     fontSize: 11.sp,
  //                     color: const Color(0xFFFF0000),
  //                   ),
  //                 ),
  //                 Padding(padding: EdgeInsets.only(bottom: 4.h)),
  //                 Text(
  //                   "生成失败，",
  //                   style: TextStyle(
  //                     fontSize: 11.sp,
  //                     color: const Color(0xFFA9A8A8),
  //                   ),
  //                 ),
  //                 Text(
  //                   "请重新生成",
  //                   style: TextStyle(
  //                     fontSize: 11.sp,
  //                     color: const Color(0xFFA9A8A8),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //           Positioned(top: 5.h, right: 7.w, child: select),
  //         ],
  //       ),
  //     );
  //   }
  //   if (creationResult.generationState == 19) {
  //     /// 视频合成成功
  //     return Stack(
  //       children: [
  //         InkWell(
  //           onTap: () {
  //             if (manager) {
  //               /// 选中或者取消选中
  //               ref
  //                   .read(deleteProductListProvider.notifier)
  //                   .updateDeleteList(creationResult);
  //             } else {
  //               context.push("/$preview", extra: creationResult);
  //             }
  //           },
  //           child: VideoFirstFrameWidget(creationResult: creationResult),
  //         ),
  //         Positioned(top: 5.h, right: 7.w, child: select),
  //       ],
  //     );
  //   }
  //   return Stack(
  //     children: [
  //       InkWell(
  //         onTap: () {
  //           if (manager) {
  //             /// 选中或者取消选中
  //             ref
  //                 .read(deleteProductListProvider.notifier)
  //                 .updateDeleteList(creationResult);
  //           }
  //         },
  //         child: Container(
  //           width: 109.w,
  //           height: 119.h,
  //           decoration: BoxDecoration(
  //             color: const Color(0xFFF2F2F3),
  //             borderRadius: BorderRadius.circular(10.r),
  //           ),
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: [
  //               Image.asset(
  //                 creationProcess,
  //                 width: 24.w,
  //                 height: 25.h,
  //               ),
  //               Padding(padding: EdgeInsets.only(bottom: 16.h)),
  //               Text(
  //                 "正在生成中,",
  //                 style: TextStyle(
  //                   fontSize: 11.sp,
  //                   color: const Color(0xFFA9A8A8),
  //                 ),
  //               ),
  //               Text(
  //                 "请稍等...",
  //                 style: TextStyle(
  //                   fontSize: 11.sp,
  //                   color: const Color(0xFFA9A8A8),
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ),
  //       ),
  //       Positioned(top: 5.h, right: 7.w, child: select),
  //     ],
  //   );
  // }

  /// 无作品
  Widget _buildEmpty(WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.only(top: 30),
      alignment: Alignment.center,
      child: Column(
        children: [
          Image.asset(
            creationEmpty,
            width: 106,
            height: 121,
          ),
          const Padding(padding: EdgeInsets.only(bottom: 17)),
          const Text(
            "您还没有数字人视频，快去创建一个吧～",
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF949599),
            ),
          ),
          const SizedBox(height: 22),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GradientButton(
                onPress: () {
                  ref.read(homeProvider.notifier).jumpToPage(0);
                },
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 27),
                shadow: false,
                gradient: const LinearGradient(
                  colors: [Color(0xFF00CD6E), Color(0xFF00D873)],
                ),
                child: const Text(
                  "创建数字人视频",
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var crossAxisCount = 4;
    var itemWidth = (MediaQuery.sizeOf(context).width - 50) / crossAxisCount;
    //Stack(
    //       alignment: Alignment.center,
    //       children: [
    //         Column(
    //           children: [
    //             _buildUserInfo(context, ref),
    //             _buildMembership(context, ref),
    //             _buildMenus(context, ref),
    //             SizedBox(
    //               height: 20.h,
    //             ),
    //             _buildManager(ref),
    //             Expanded(
    //               child: Padding(
    //                 padding: EdgeInsets.symmetric(horizontal: 15.w),
    //                 child: CustomListView(
    //                   padding: EdgeInsets.only(bottom: 56.h),
    //                   onLoadMore: () async {
    //                     ref.read(creationRecordProvider.notifier).loadMore();
    //                   },
    //                   data: ref.watch(
    //                     creationRecordProvider.select((value) => value.creations),
    //                   ),
    //                   footerState: ref.watch(
    //                     creationRecordProvider.select((value) => value.loadState),
    //                   ),
    //                   sliverGridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
    //                     mainAxisSpacing: 9.h,
    //                     crossAxisSpacing: 9.w,
    //                     crossAxisCount: 3,
    //                   ),
    //                   renderItem: (context, index, o) {
    //                     // var width = (MediaQuery.sizeOf(context).width-48)/3;
    //                     return _buildCreationItem(ref, context, o);
    //                   },
    //                   empty: _buildEmpty(),
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //         Positioned(bottom: 20.h, child: const DeleteWidget()),
    //       ],
    //     )
    return Stack(
      children: [
        CustomListView(
          sliverHeader: [
            SliverToBoxAdapter(
              child: _buildUserInfo(context, ref),
            ),
            SliverToBoxAdapter(
              child: _buildMembership(context, ref),
            ),
            SliverToBoxAdapter(
              child: _buildMenus(context, ref),
            ),
            const ProductionTabWidget(),
            // _buildManager(ref),
          ],
          padding: const EdgeInsets.symmetric(horizontal: 10),
          sliverGridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            mainAxisSpacing: 16,
            crossAxisSpacing: 10,
            childAspectRatio: 80 / 127,
          ),
          data: ref.watch(
            customizeVideoRecordProvider.select((value) => value.videoList),
          ),
          footerState: ref.watch(
            customizeVideoRecordProvider.select((value) => value.loadState),
          ),
          renderItem: (context, index, o) {
            return VideoRecordItemWidget(
              customizeVideo: o,
              width: itemWidth,
            );
          },
          empty: _buildEmpty(ref),
        ),
        Positioned(
          right: 3,
          bottom: 167 / 813 * MediaQuery.sizeOf(context).height,
          child: const MeRefreshWidget(),
        ),
      ],
    );
  }
}

// class DeleteWidget extends ConsumerWidget {
//   const DeleteWidget({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     var manger = ref.watch(deleteProductProvider);
//     if (!manger) {
//       return Container();
//     }
//     return Row(
//       children: [
//         InkWell(
//           onTap: () {
//             ref.read(deleteProductListProvider.notifier).deleteVideos();
//           },
//           child: Container(
//             width: 228.w,
//             height: 47.h,
//             alignment: Alignment.center,
//             decoration: BoxDecoration(
//               color: Colors.white,
//               border: Border.all(color: const Color(0xFFFF4C4C)),
//               borderRadius: BorderRadius.circular(12.r),
//             ),
//             child: Text(
//               "删除",
//               style: TextStyle(
//                 fontSize: 18.sp,
//                 color: const Color(0xFFFF4C4C),
//               ),
//             ),
//           ),
//         )
//       ],
//     );
//   }
// }

// 创作成功显示的item
// class VideoFirstFrameWidget extends ConsumerWidget {
//   const VideoFirstFrameWidget({
//     super.key,
//     required this.creationResult,
//   });
//
//   final CreationResult creationResult;
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     return ClipRRect(
//       borderRadius: const BorderRadius.all(Radius.circular(12)),
//       child: Container(
//         width: 109.w,
//         height: 119.h,
//         decoration: const BoxDecoration(
//           // color: Colors.white,
//           borderRadius: BorderRadius.all(Radius.circular(16)),
//         ),
//         child: Image.asset(creationSuccessShowing),
//       ),
//     );
//   }
// }
