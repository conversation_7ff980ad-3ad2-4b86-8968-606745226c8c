import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/recommend/home_recommend_provider.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/repository/modals/recommend/category_list.dart';
import 'package:text_generation_video/app/repository/modals/recommend/function_item.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

class GlideWidget extends ConsumerStatefulWidget {
  const GlideWidget({super.key});

  @override
  GlideWidgetState createState() => GlideWidgetState();
}

class GlideWidgetState extends ConsumerState<GlideWidget> {
  late StreamSubscription<List<ConnectivityResult>> subscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (d) {
        subscription = Connectivity().onConnectivityChanged.listen(
          (List<ConnectivityResult> result) {
            debugPrint("onConnectivityChanged: ${result.length}");
            debugPrint("onConnectivityChanged: ${result.first}");
            final hasNetwork = result.any((r) => r != ConnectivityResult.none);
            if (hasNetwork) {
              debugPrint("onConnectivityChanged: refresh");
              ref.read(homeRecommendFunctionProvider.notifier).loadCategory();
            }
          },
        );
      },
    );
  }

  @override
  void dispose() {
    subscription.cancel();
    super.dispose();
  }

  // 全部展开
  void _expandAllFun(List<CategoryList?>? list) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.84,
          maxChildSize: 0.84,
          snap: true,
          expand: false,
          builder: (context, controller) {
            return HomeAllFunWidget(list: list, scrollController: controller);
          },
        );
      },
    );
  }

  // creation header
  Widget _buildHeadBanner(BuildContext context) {
    var itemWidth = (MediaQuery.sizeOf(context).width - 30) / 2;
    var itemHeight = 94 * itemWidth / 170;
    return ref.watch(fetchHomeBannerListProvider).when(
      data: (data) {
        if (data == null || data.isEmpty) {
          return const SizedBox();
        }
        return SliverToBoxAdapter(
          child: Container(
            height: itemHeight,
            margin: const EdgeInsets.fromLTRB(12, 0, 12, 14),
            child: ListView.separated(
              // padding: const EdgeInsets.symmetric(horizontal: 12),
              scrollDirection: Axis.horizontal,
              itemCount: data.length,
              itemBuilder: (BuildContext context, int index) {
                var item = data[index];
                return InkWell(
                  onTap: () {
                    ref
                        .read(sameStyleActionProvider.notifier)
                        .sameAction(item.functionType);
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: item.imgUrl ?? "",
                      width: itemWidth,
                      height: itemHeight,
                      fit: BoxFit.cover,
                      placeholder: (c, s) {
                        return Container(
                          width: itemWidth,
                          height: itemHeight,
                          decoration: const BoxDecoration(
                            color: Color(0xFF29282B),
                          ),
                        );
                      },
                      errorWidget: (c, o, s) {
                        return Container(
                          width: itemWidth,
                          height: itemHeight,
                          decoration: const BoxDecoration(
                            color: Color(0xFF29282B),
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(width: 6);
              },
            ),
          ),
        );
      },
      error: (o, s) {
        return const SliverToBoxAdapter(child: SizedBox());
      },
      loading: () {
        return const SliverToBoxAdapter(child: SizedBox());
      },
    );
  }

  Widget _buildHeadNavi(
    List<FunctionItem?>? funList,
    List<CategoryList?>? list,
  ) {
    if (funList == null || funList.isEmpty) {
      return const SliverToBoxAdapter(child: SizedBox());
    }
    return SliverToBoxAdapter(
      child: Row(
        children: [
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.fromLTRB(8, 10, 8, 20),
              child: Row(
                children: funList.map((e) => _buildHeadNavItem(e)).toList(),
              ),
            ),
          ),
          const SizedBox(width: 8),
          InkWell(
            onTap: () {
              _expandAllFun(list);
            },
            child: Padding(
              padding: const EdgeInsets.fromLTRB(8, 10, 8, 20),
              child: Column(
                children: [
                  Image.asset(allFunIcon, width: 20),
                  const SizedBox(height: 7),
                  const Text(
                    "全部",
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  Widget _buildHeadNavItem(FunctionItem? e) {
    return Container(
      width: 62.w,
      margin: EdgeInsets.symmetric(horizontal: 8.w),
      child: InkWell(
        onTap: () {
          ref
              .read(sameStyleActionProvider.notifier)
              .sameAction(e?.functionType);
        },
        child: Column(
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                CachedNetworkImage(
                  imageUrl: e?.iconUrl ?? "",
                  width: 20,
                  placeholder: (c, s) {
                    return Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF29282B),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      width: 20,
                      height: 20,
                    );
                  },
                  errorWidget: (c, o, s) {
                    return Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF29282B),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      width: 20,
                      height: 20,
                    );
                  },
                ),
                if (e?.functionTag != null && e?.functionTag != 0)
                  Positioned(
                    top: -5,
                    left: 15,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF5BF06B), Color(0xFF30E6B8)],
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        e?.functionTag == 1 ? "HOT" : "NEW",
                        style: const TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.w800,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 7),
            Text(
              "${e?.functionName}",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItem(CategoryList? category) {
    var itemWidth = 97.5.w;
    var itemHeight = 130 * itemWidth / 97.5;
    var funList = category?.functionInfoList;
    if (funList == null || funList.isEmpty) {
      return const SizedBox();
    }
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${category?.categoryName}",
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                  ),
                ),
                InkWell(
                  onTap: () {
                    context.push("/$homeFunListPage", extra: category);
                  },
                  child: const Row(
                    children: [
                      Text(
                        "全部",
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF94979D),
                        ),
                      ),
                      SizedBox(width: 2),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: Color(0xFF94979D),
                        size: 9,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          SizedBox(
            height: itemHeight,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                var item = funList[index];
                return ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: InkWell(
                    onTap: () {
                      ref
                          .read(sameStyleActionProvider.notifier)
                          .sameAction(item?.functionType);
                    },
                    child: Stack(
                      children: [
                        CachedNetworkImage(
                          imageUrl: item?.functionImgUrl ?? "",
                          placeholder: (c, s) {
                            return Container(
                              decoration: const BoxDecoration(
                                color: Color(0xFF29282B),
                              ),
                              width: itemWidth,
                              height: itemHeight,
                            );
                          },
                          errorWidget: (c, o, s) {
                            return Container(
                              decoration: const BoxDecoration(
                                color: Color(0xFF29282B),
                              ),
                              width: itemWidth,
                              height: itemHeight,
                            );
                          },
                        ),
                        Positioned(
                          top: 10,
                          left: 10,
                          child: Row(
                            children: [
                              Image.asset(funDescTag, width: 9.61),
                              const SizedBox(width: 4),
                              Text(
                                "${item?.functionName}",
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Colors.white,
                                  shadows: [
                                    Shadow(
                                      offset: Offset(2.0, 2.0), // 阴影偏移
                                      blurRadius: 6.0, // 模糊程度
                                      color: Colors.black38, // 阴影颜色
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return const SizedBox(width: 10);
              },
              itemCount: funList.length,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var recommendData = ref.watch(homeRecommendFunctionProvider);
    if (recommendData == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    var categoryList = recommendData.categoryList;
    var recommendList = recommendData.homeRecommendList;
    return CustomListView(
      padding: EdgeInsets.only(
        bottom: MediaQuery.paddingOf(context).bottom + 71,
      ),
      sliverHeader: [
        SliverToBoxAdapter(
          child: SizedBox(
            height: MediaQuery.paddingOf(context).top,
          ),
        ),
        _buildHeadBanner(context),
        _buildHeadNavi(recommendList, categoryList),
      ],
      onLoadMore: () async {},
      data: categoryList,
      renderItem: (context, index, o) {
        var item = categoryList?[index];
        return _buildItem(item);
      },
    );
  }
}

// 全部功能弹窗显示
class HomeAllFunWidget extends ConsumerWidget {
  const HomeAllFunWidget({
    super.key,
    required this.list,
    required this.scrollController,
  });

  final List<CategoryList?>? list;

  final ScrollController scrollController;

  Widget _buildItem(
    WidgetRef ref,
    CategoryList? categoryList,
    double itemWidget,
  ) {
    if (categoryList == null ||
        categoryList.functionInfoList == null ||
        categoryList.functionInfoList!.isEmpty) {
      return const SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "${categoryList.categoryName}",
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 5,
          runSpacing: 10,
          children: categoryList.functionInfoList!
              .map(
                (e) => InkWell(
                  onTap: () {
                    ref
                        .read(sameStyleActionProvider.notifier)
                        .sameAction(e?.functionType);
                  },
                  child: Container(
                    width: itemWidget,
                    height: itemWidget,
                    decoration: BoxDecoration(
                      color: const Color(0xFF29282B),
                      borderRadius: BorderRadius.circular(14),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Stack(
                          clipBehavior: Clip.none,
                          children: [
                            CachedNetworkImage(
                              imageUrl: e?.iconUrl ?? "",
                              width: 20,
                            ),
                            if (e?.functionTag != null && e?.functionTag != 0)
                              Positioned(
                                top: -5,
                                left: 15,
                                child: Container(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 4),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFF5BF06B),
                                        Color(0xFF30E6B8)
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    e?.functionTag == 1 ? "HOT" : "NEW",
                                    style: const TextStyle(
                                      fontSize: 9,
                                      fontWeight: FontWeight.w800,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          "${e?.functionName}",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
              .toList(),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var itemWidth = (MediaQuery.sizeOf(context).width - 47) / 4;
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF18161A),
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(14),
          topLeft: Radius.circular(14),
        ),
      ),
      child: CustomScrollView(
        controller: scrollController,
        slivers: [
          SliverToBoxAdapter(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 29,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12, bottom: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF39373B),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ],
            ),
          ),
          SliverPadding(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: MediaQuery.paddingOf(context).bottom + 20,
            ),
            sliver: SliverList.builder(
              itemCount: list?.length ?? 0,
              itemBuilder: (context, index) {
                var item = list?[index];
                return _buildItem(ref, item, itemWidth);
              },
            ),
          ),
        ],
      ),
    );
  }
}
