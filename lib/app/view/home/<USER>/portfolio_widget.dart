import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';
import 'package:text_generation_video/app/view/record/widget/record_list_widget.dart';

import '../../../../config/icon_address.dart';

class PortfolioWidget extends ConsumerWidget {
  const PortfolioWidget({super.key});

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    var editState = ref.watch(currentRecordEditStateProvider);
    Widget icon = editState
        ? Image.asset(cancelEditIcon, width: 8)
        : Image.asset(profileEditIcon, width: 14);
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        MediaQuery.paddingOf(context).top + 11,
        16,
        11,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            "生成记录",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFFFFFFFF),
            ),
          ),
          InkWell(
            onTap: () {
              ref
                  .read(currentRecordEditStateProvider.notifier)
                  .setEditState(!editState);
            },
            child: Row(
              children: [
                icon,
                const SizedBox(width: 4),
                Text(
                  editState ? "取消" : "管理",
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context, ref),
        const Expanded(child: RecordListWidget()),
      ],
    );
  }
}
