import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_style_banner.dart';

class GlideBannerWidget extends ConsumerStatefulWidget {
  const GlideBannerWidget({super.key});

  @override
  GlideBannerWidgetState createState() => GlideBannerWidgetState();
}

class GlideBannerWidgetState extends ConsumerState<GlideBannerWidget> {
  @override
  void initState() {
    super.initState();
  }

  Widget _buildItem(SameStyleBanner banner, double itemWidget) {
    return InkWell(
      onTap: () {
        ref
            .read(sameStyleActionProvider.notifier)
            .sameAction(banner.functionType);
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: CachedNetworkImage(
          imageUrl: banner.imgUrl ?? "",
          width: itemWidget,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var itemWidget = MediaQuery.sizeOf(context).width - 32;
    return ref.watch(fetchSameStyleBannerListProvider).when(
      data: (data) {
        Widget child = const SizedBox();
        if (data != null && data.isNotEmpty) {
          child = CarouselSlider.builder(
            itemCount: data.length,
            itemBuilder: (context, index, view) {
              var item = data[index];
              return _buildItem(item, itemWidget);
            },
            options: CarouselOptions(
              aspectRatio: 3.42 / 1,
              autoPlay: data.length > 1,
              enableInfiniteScroll: data.length > 1,
              viewportFraction: 1,
            ),
          );
        }
        return SliverToBoxAdapter(
          child: child,
        );
      },
      error: (o, s) {
        return const SliverToBoxAdapter(child: SizedBox());
      },
      loading: () {
        return const SliverToBoxAdapter(child: SizedBox());
      },
    );
  }
}
