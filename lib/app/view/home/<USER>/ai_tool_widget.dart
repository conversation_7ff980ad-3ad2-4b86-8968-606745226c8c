import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/home/<USER>';
import 'package:text_generation_video/app/view/home/<USER>/main_item_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/main_tab_widget.dart';
import 'package:text_generation_video/app/view/home/<USER>/mian_banner_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';

class AiToolWidget extends ConsumerStatefulWidget {
  const AiToolWidget({super.key});

  @override
  AiToolWidgetState createState() => AiToolWidgetState();
}

class AiToolWidgetState extends ConsumerState<AiToolWidget> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomListView(
        sliverHeader: const [
          MainBannerWidget(),
          MainTabWidget(),
        ],
        data: ref.watch(mainIntelligentListDataProvider),
        padding: const EdgeInsets.symmetric(horizontal: 10),
        sliverGridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 10,
          crossAxisSpacing: 10,
          childAspectRatio: 172 / 134,
        ),
        renderItem: (context, index, o) {
          return MainItemWidget(intelligent: o);
        },
      ),
    );
  }
}
