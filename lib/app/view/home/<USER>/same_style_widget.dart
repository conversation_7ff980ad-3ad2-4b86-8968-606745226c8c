import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_example.dart';
import 'package:text_generation_video/app/view/home/<USER>/same_style_tab_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:waterfall_flow/waterfall_flow.dart';

import '../../../../config/icon_address.dart';
import '../widget/glide_banner_widget.dart';

class SameStyleWidget extends ConsumerStatefulWidget {
  const SameStyleWidget({super.key});

  @override
  SameStyleWidgetState createState() => SameStyleWidgetState();
}

class SameStyleWidgetState extends ConsumerState<SameStyleWidget> {
  int crossAxisCount = 2;
  double crossAxisSpacing = 11.0;
  double mainAxisSpacing = 14.0;

  // item widget
  Widget _buildItem(SameExample? item, double itemWidth) {
    return InkWell(
      onTap: () {
        context.push(
          "/$sameStyleDetailPage",
          extra: {"tag": "same_style_${item?.id}", "sameData": item},
        );
      },
      child: Container(
        alignment: Alignment.center,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedNetworkImage(
                imageUrl: item?.previewImgUrl ?? "",
                width: itemWidth,
                fit: BoxFit.fitWidth,
                placeholder: (context, s) {
                  return const SizedBox(height: 80);
                },
                errorWidget: (context, o, s) {
                  return const SizedBox(height: 80);
                },
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "${item?.exampleName}",
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFFFFFFFF),
              ),
            ),
            const SizedBox(height: 7),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: CachedNetworkImage(
                        imageUrl: "${item?.authorHeadImg}",
                        width: 18,
                        errorWidget: (c, o, s) {
                          return const SizedBox(
                            width: 18,
                            height: 18,
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 5),
                    Text(
                      "${item?.authorName}",
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF8A8D93),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Image.asset(unCollectionIcon, width: 14),
                    const SizedBox(width: 4),
                    Text(
                      "${item?.collectionCount}",
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF8A8D93),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  // empty widget
  Widget _emptyWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: 120.h),
        Image.asset(recordEmptyIcon),
        const SizedBox(height: 10),
        const Text(
          "暂无内容",
          style: TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        SizedBox(height: MediaQuery.paddingOf(context).top + 40),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    var itemWidth = (MediaQuery.sizeOf(context).width - 32 - mainAxisSpacing) /
        crossAxisCount;
    var sameCategoryList = ref.watch(sameStyleCategoryListProvider);
    return SafeArea(
      bottom: false,
      child: CustomListView(
        onRefresh: () async {
          await ref.read(sameStyleExampleListProvider.notifier).loadData();
        },
        onLoadMore: () async {
          await ref.read(sameStyleExampleListProvider.notifier).loadMore();
        },
        data: ref.watch(
          sameStyleExampleListProvider.select((value) => value.exampleList),
        ),
        footerState: ref.watch(
          sameStyleExampleListProvider.select((value) => value.loadState),
        ),
        isMessage: true,
        padding: EdgeInsets.fromLTRB(
          16,
          0,
          16,
          MediaQuery.paddingOf(context).bottom + 91,
        ),
        sliverWaterfallFlowDelegate:
            SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: crossAxisSpacing,
          mainAxisSpacing: mainAxisSpacing,
          collectGarbage: (List<int> garbages) {
            // print('collect garbage : $garbages');
          },
          viewportBuilder: (int firstIndex, int lastIndex) {
            // print('viewport : [$firstIndex,$lastIndex]');
          },
        ),
        sliverHeader: [
          const GlideBannerWidget(),
          SliverPersistentHeader(
            pinned: true,
            delegate: SameStyleTabHeaderDelegate(
              child: DefaultTabController(
                length: sameCategoryList?.length ?? 0,
                child: SameStyleTabWidget(
                  sameCategoryList: sameCategoryList ?? [],
                ),
              ),
              maxHeight: 50,
              minHeight: 50,
            ),
          ),
        ],
        renderItem: (context, index, o) {
          return _buildItem(o, itemWidth);
        },
        empty: _emptyWidget(),
      ),
    );
  }
}

class SameStyleTabHeaderDelegate extends SliverPersistentHeaderDelegate {
  SameStyleTabHeaderDelegate({
    required this.child,
    required this.maxHeight,
    required this.minHeight,
  });

  final Widget child;
  final double minHeight;
  final double maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SameStyleTabHeaderDelegate oldDelegate) {
    return oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.child != child;
  }
}

// 自定义裁剪器，实现从中心向外扩散或向内收缩的矩形裁剪
class RadialRectClipper extends CustomClipper<Path> {
  final double progress; // 0.0 到 1.0

  RadialRectClipper(this.progress);

  @override
  Path getClip(Size size) {
    final path = Path();

    // 根据progress计算裁剪矩形的insets (内边距)
    // 当progress为0时，inset最大，裁剪区域最小 (中心点)
    // 当progress为1时，inset为0，裁剪区域最大 (完整尺寸)
    final double insetX = size.width / 2 * (1.0 - progress);
    final double insetY = size.height / 2 * (1.0 - progress);

    path.addRect(
      Rect.fromLTRB(
        insetX,
        insetY,
        size.width - insetX,
        size.height - insetY,
      ),
    );

    return path;
  }

  @override
  bool shouldReclip(covariant RadialRectClipper oldClipper) {
    return oldClipper.progress != progress;
  }
}
