import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../provider/record/record_provider.dart';

class PortfolioTabWidget extends ConsumerStatefulWidget {
  const PortfolioTabWidget({
    super.key,
    this.categoryType,
  });

  final int? categoryType;

  @override
  PortfolioTabWidgetState createState() => PortfolioTabWidgetState();
}

class PortfolioTabWidgetState extends ConsumerState<PortfolioTabWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    _tabController = TabController(length: portfolioTabs.length, vsync: this);

    WidgetsBinding.instance.addPostFrameCallback((d) {
      var index =
          portfolioTabs.indexWhere((e) => e.type == widget.categoryType);
      var currentIndex = index != -1 ? index : 0;
      _tabController.animateTo(currentIndex);
      ref
          .read(currentRecordTabProvider.notifier)
          .setCurrentTab(portfolioTabs[currentIndex]);
    });
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget _buildItemTab(BuildContext context, PortfolioTab item) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(item.name),
    );
  }

  @override
  Widget build(BuildContext context) {
    var currentTab = ref.watch(currentRecordTabProvider);
    var index = portfolioTabs.indexWhere((e) => e.type == currentTab.type);
    var currentIndex = index != -1 ? index : 0;
    return TabBar(
      controller: _tabController..animateTo(currentIndex),
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      dividerHeight: 0,
      padding: const EdgeInsets.symmetric(horizontal: 11),
      labelPadding: const EdgeInsets.symmetric(horizontal: 5),
      indicator: const BoxDecoration(),
      labelColor: const Color(0xFFFFFFFF),
      unselectedLabelColor: const Color(0xFFA1A1A1),
      tabs: portfolioTabs.map((e) => _buildItemTab(context, e)).toList(),
      onTap: (index) {
        var item = portfolioTabs[index];
        ref.read(currentRecordTabProvider.notifier).setCurrentTab(item);
      },
    );
  }
}
