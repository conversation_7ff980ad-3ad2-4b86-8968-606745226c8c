import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/repository/modals/same_style/same_category.dart';

import '../../../widgets/top_sheet/top_sheet.dart';

class SameStyleTabWidget extends ConsumerStatefulWidget {
  const SameStyleTabWidget({
    super.key,
    required this.sameCategoryList,
  });

  final List<SameCategory> sameCategoryList;

  @override
  SameStyleTabWidgetState createState() => SameStyleTabWidgetState();
}

class SameStyleTabWidgetState extends ConsumerState<SameStyleTabWidget> {
  // 顶部显示全部tab
  void showTopTabSelect(TabController controller) {
    showModalTopSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.fromLTRB(
            16,
            MediaQuery.paddingOf(context).top + 12,
            16,
            16,
          ),
          decoration: const BoxDecoration(
            color: Color(0xFF29282B),
            borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(14),
              bottomLeft: Radius.circular(14),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    "全部频道",
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      context.pop();
                    },
                    child: const Icon(
                      Icons.keyboard_arrow_up_rounded,
                      color: Color(0xFFFFFFFF),
                      size: 22,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 14),
              Wrap(
                spacing: 10,
                runSpacing: 10,
                children: widget.sameCategoryList
                    .map((e) => _buildTopItemTab(e, controller))
                    .toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTopItemTab(SameCategory tab, TabController controller) {
    return InkWell(
      onTap: () {
        ref.read(currentSameStyleCategoryProvider.notifier).setCategory(tab);
        var index = widget.sameCategoryList.indexWhere((e) => e.id == tab.id);
        controller.animateTo(index != -1 ? index : 0);
        context.pop();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        decoration: BoxDecoration(
          color: const Color(0xFF18161A),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(tab.categoryName ?? ""),
      ),
    );
  }

  Widget _buildItemTab(BuildContext context, SameCategory item) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 12),
      child: Text(item.categoryName ?? ""),
    );
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(currentSameStyleCategoryProvider.notifier);
    if (widget.sameCategoryList.isEmpty) {
      return const SizedBox();
    }
    var c = DefaultTabController.of(context);
    return Container(
      color: const Color(0xFF18161A),
      child: Row(
        children: [
          Expanded(
            child: TabBar(
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              dividerHeight: 0,
              indicator: const BoxDecoration(),
              indicatorSize: TabBarIndicatorSize.tab,
              labelColor: const Color(0xFFFFFFFF),
              unselectedLabelColor: const Color(0xFF8A8D93),
              labelPadding: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              tabs: widget.sameCategoryList
                  .map(
                    (e) => _buildItemTab(context, e),
                  )
                  .toList(),
              onTap: (index) {
                debugPrint("index: $index");
                var item = widget.sameCategoryList[index];
                ref
                    .read(currentSameStyleCategoryProvider.notifier)
                    .setCategory(item);
              },
            ),
          ),
          InkWell(
            onTap: () {
              showTopTabSelect(c);
            },
            child: Container(
              padding: const EdgeInsets.only(left: 16, right: 14),
              child: const Icon(
                Icons.keyboard_arrow_down_rounded,
                color: Color(0xFF94979D),
                size: 22,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SameStyleTab {
  int id;
  String? name;
  IconData? icon;

  SameStyleTab(this.id, {this.name, this.icon});
}
