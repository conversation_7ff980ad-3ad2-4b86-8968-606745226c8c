import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/repository/modals/main/intelligent.dart';

import '../../../../utils/router_util.dart';
import '../../../provider/agent/conversation_provider.dart';

class MainItemWidget extends ConsumerWidget {
  const MainItemWidget({
    super.key,
    required this.intelligent,
  });

  final Intelligent intelligent;

  // 解析配置并跳转
  void _jump(BuildContext context, WidgetRef ref) {
    try {
      var jumpType = intelligent.appType;
      var jumpParam = jsonDecode(intelligent.appParam!);
      var jump = jumpParam["jump"];
      switch (jumpType) {
        case 1:
          // 内部页面
          var url = jump["url"];
          var needLogin = jump["needLogin"] as bool;
          if (needLogin) {
            RouterUtil.checkLogin(context, call: () {
              context.push('/$url');
            });
          } else {
            context.push('/$url');
          }
          break;
        case 2:
          // 智能体
          var id = jump["id"];
          var title = intelligent.appTitle;
          var needLogin = jump["needLogin"] as bool;
          RouterUtil.checkLogin(context, call: () {
            ref
                .read(currentConversationProvider.notifier)
                .jumpConversation(id, title, needLogin);
          });
          break;
      }
    } catch (e) {
      debugPrint("$e");
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return InkWell(
      onTap: () {
        _jump(context, ref);
      },
      child: Container(
        width: 172.w,
        height: 134.h,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color(0xFF224E4C),
              Color(0xFF1F393A),
              Color(0xFF1D272B),
            ],
            stops: [0.0, 0.3, 1.0],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(7),
        ),
        child: Column(
          children: [
            Padding(
              padding:
                  const EdgeInsets.only(left: 10, right: 10, top: 9, bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "${intelligent.appTitle}",
                      style: const TextStyle(fontSize: 16, color: Colors.white),
                    ),
                  ),
                  if (intelligent.markImgUrl != null)
                    Image.network(
                      intelligent.markImgUrl ?? "",
                      height: 15,
                      errorBuilder: (c, o, s) {
                        return Container();
                      },
                    ),
                ],
              ),
            ),
            const Divider(
              indent: 10,
              endIndent: 10,
              height: 0.6,
              color: Color(0xFFDBEAE5),
            ),
            Expanded(
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Text(
                    "${intelligent.appText}",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF828282),
                    ),
                  ),
                ),
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 37.h,
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(left: 11),
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFF2F383F),
                          Color(0xFF252F34),
                          Color(0xFF223034),
                        ],
                      ),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(7),
                      ),
                    ),
                    child: Text(
                      "${intelligent.useNum}使用",
                      style: const TextStyle(
                        fontSize: 13,
                        color: Color(0xFFFFFFFF),
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 37.w,
                  height: 37.h,
                  decoration: const BoxDecoration(
                    color: Color(0xFF2E363E),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(7),
                    ),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
