import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/repository/modals/account/member.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../utils/router_util.dart';
import '../../../navigation/router.dart';
import '../../../provider/account/auth_provider.dart';
import '../../../provider/member/compute_power_provider.dart';
import '../../../provider/member/member_provider.dart';

class GlideMeWidget extends ConsumerWidget {
  const GlideMeWidget({super.key});

  Widget _buildHeader(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: EdgeInsets.fromLTRB(
          16,
          MediaQuery.paddingOf(context).top + 11,
          0,
          11,
        ),
        child: const Text(
          "我的",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFFFFFFFF),
          ),
        ),
      ),
    );
  }

  // 用户信息
  Widget _buildUserInfo(BuildContext context, WidgetRef ref) {
    var userData = ref.watch(authProvider);
    var member = ref.watch(memberInfoProvider);
    var memberType = 0;
    if (member?.hasExpire == true) {
      //过期
      memberType = 2;
    } else if (member?.hasExpire == false) {
      //会员
      memberType = 1;
    }

    // 未登录
    Widget info = InkWell(
      onTap: () {
        context.go("/$login");
      },
      child: const Text(
        "登录/注册",
        style: TextStyle(
          fontSize: 14,
          color: Color(0xFF15085A),
        ),
      ),
    );
    if (userData != null) {
      info = Text(
        "${userData.username}",
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF15085A),
        ),
      );
    }

    // 有效期显示和剩余灵感值
    Widget powerValueWidget = Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          info,
          const SizedBox(height: 2),
          if (memberType == 1 || memberType == 2)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "有效期至${member?.expireTime}",
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF857BAC),
                  ),
                ),
                if (memberType == 1)
                  Container(
                    padding: const EdgeInsets.only(right: 5),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFFB3EBFF),
                          Color(0xFF92A3F1),
                          Color(0xFF895BE2),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      children: [
                        Image.asset(
                          powerValueIcon,
                          width: 15,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          "灵感值：${ref.watch(
                            userPowerBalanceProvider
                                .select((value) => value?.powerBalance ?? 0),
                          )}",
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
        ],
      ),
    );

    return SliverToBoxAdapter(
      child: GestureDetector(
        onTap: () {
          RouterUtil.checkLogin(context, call: () {
            context.push("/$memberPage");
          });
        },
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Container(
              margin: const EdgeInsets.fromLTRB(16, 0, 16, 17),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: CustomPaint(
                  painter: MultiGradientPainter(),
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                              width: 42,
                              height: 42,
                              decoration: BoxDecoration(
                                color: const Color(0xFF29282B),
                                borderRadius: BorderRadius.circular(21),
                                border: Border.all(
                                  color: const Color(0xFF646464),
                                  width: 0.2,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(tabAiChat, width: 31),
                                ],
                              ),
                            ),
                            const SizedBox(width: 14),
                            powerValueWidget,
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildTagItem("赠送灵感值"),
                            _buildTagItem("无广告"),
                            _buildTagItem("解锁全部功能"),
                          ],
                        ),
                        const SizedBox(height: 20),
                        _memberButton(context, member),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            if (memberType == 1)
              Positioned(
                top: -25,
                right: 20,
                child: Image.asset(memberTag, width: 63.32),
              ),
          ],
        ),
      ),
    );
  }

  // 会员按钮
  Widget _memberButton(BuildContext context, Member? member) {
    Widget memberBtnText = const Text(
      "开通VIP，解锁全部功能",
      style: TextStyle(
        fontSize: 12,
        color: Color(0xFF020202),
      ),
    );
    if (member?.hasExpire == true) {
      //过期
      memberBtnText = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text(
            "VIP已过期，特权功能已锁定",
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF020202),
            ),
          ),
          const SizedBox(width: 6),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 1),
            decoration: BoxDecoration(
              gradient: const LinearGradient(colors: [
                Color(0xFFB3EBFF),
                Color(0xFF92A3F1),
                Color(0xFF895BE2),
              ]),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Text(
              "恢复订阅",
              style: TextStyle(
                fontSize: 10,
                color: Colors.white,
              ),
            ),
          ),
        ],
      );
    } else if (member?.hasExpire == false) {
      //会员
      memberBtnText = const Text(
        "尊贵的VIP，已解锁全部功能",
        style: TextStyle(
          fontSize: 12,
          color: Color(0xFF020202),
        ),
      );
    }
    return GradientButton(
      onPress: () {
        RouterUtil.checkLogin(context, call: () {
          context.push("/$memberPage");
        });
      },
      radius: 12,
      boxShadow: [
        BoxShadow(
          color: const Color(0xFF6F2F6F).withValues(alpha: 0.21),
          offset: const Offset(0, 6),
          blurRadius: 12,
          spreadRadius: 0,
        ),
      ],
      padding: const EdgeInsets.symmetric(vertical: 12),
      gradient: const LinearGradient(colors: [
        Color(0xFFFFF1FC),
        Color(0xFFFFFBE9),
        Color(0xFFFFFDF7),
        Color(0xFFDCF0F8),
      ]),
      child: memberBtnText,
    );
  }

  Widget _buildTagItem(String title) {
    return Row(
      children: [
        Container(
          width: 13,
          height: 13,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFF857BAC), width: 0.8),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.check,
            size: 9,
            color: Color(0xFF857BAC),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          title,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF4C3F91),
          ),
        ),
      ],
    );
  }

  Widget _buildActionItem(String icon, String title, Function() onPress) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.fromLTRB(16, 0, 16, 2),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onPress,
          child: Ink(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            decoration: BoxDecoration(
              color: const Color(0xFF222123),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Image.asset(icon, width: 20),
                    const SizedBox(width: 17),
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFFFFFFFF),
                      ),
                    ),
                  ],
                ),
                const Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Color(0xFFFFFFFF),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        Image.asset(
          glideMeHeaderBg,
          width: MediaQuery.sizeOf(context).width,
          fit: BoxFit.fitWidth,
        ),
        CustomScrollView(
          slivers: [
            _buildHeader(context),
            _buildUserInfo(context, ref),
            _buildActionItem(myWorkIcon, "我的作品", () {
              RouterUtil.checkLogin(context, call: () {
                context.go('/$workRecordPage');
              });
            }),
            _buildActionItem(inspirationIcon, "灵感值", () {
              RouterUtil.checkLogin(context, call: () {
                context.go('/$computePowerPage');
              });
            }),
            _buildActionItem(contactIcon, "联系我们", () {
              context.go("/$contactUsPage");
            }),
            _buildActionItem(settingIcon, "设置", () {
              context.go("/$settingPage");
            }),
          ],
        ),
      ],
    );
  }
}

class MultiGradientPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // 第一段渐变 (0,0) → (0.5,1)
    final shader1 = ui.Gradient.linear(
      const Offset(0, 0), // (0,0)
      Offset(size.width, size.height), // (0.5,1)
      [
        const Color(0xFFF4F4F6),
        const Color(0xFFEDF8FF),
        const Color(0xFFD5E4F1),
        const Color(0xFFBEC8E7),
        const Color(0xFFC0A4E7),
        const Color(0xFFD3A5E6),
        const Color(0xFFE6AFEE),
      ],
      [0.0, 0.1, 0.2, 0.5, 0.7, 0.9, 1.0],
    );

    final paint1 = Paint()..shader = shader1;
    final path1 = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width, 0)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();
    canvas.drawPath(path1, paint1);

    // 渐变
    var radius = size.width * 1.2;
    final shader2 = ui.Gradient.radial(
      Offset(size.width, 0),
      radius,
      [
        const Color(0xFFF4F4F6), // 中心亮
        const Color(0xFFEDF8FF),
        const Color(0xFFD5E4F1),
        const Color(0xFFBEC8E7).withValues(alpha: 0.1),
        const Color(0xFFC0A4E7).withValues(alpha: 0.1),
        const Color(0xFFD3A5E6).withValues(alpha: 0.1),
        const Color(0xFFE6AFEE).withValues(alpha: 0.1),
      ],
      [0.0, 0.1, 0.2, 0.5, 0.7, 0.9, 1.0],
    );
    final paintRadial = Paint()..shader = shader2;
    canvas.drawCircle(Offset(size.width, 0), radius, paintRadial);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
