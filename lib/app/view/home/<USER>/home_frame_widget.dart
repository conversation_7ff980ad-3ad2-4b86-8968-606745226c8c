import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';

import '../../../../config/icon_address.dart';

/// 首页模版
/// pageView组件添加NotificationListener滑动监听驱动底部Tab bar动画
/// bottomTabBar底部TabBar
/// action底部TabBar旁边的组件，占用固定宽度，其余由bottomTabBar占用
class HomeFrameWidget extends ConsumerStatefulWidget {
  const HomeFrameWidget({
    super.key,
    required this.pageView,
    required this.bottomTabBar,
    this.action,
  });

  final Widget pageView;
  final Widget bottomTabBar;
  final Widget? action;

  @override
  HomeFrameWidgetState createState() => HomeFrameWidgetState();
}

class HomeFrameWidgetState extends ConsumerState<HomeFrameWidget>
    with SingleTickerProviderStateMixin {
  // 底部bottom tab动画
  late final AnimationController _barCtrl;
  late final Animation<Offset> _slide; // 位置动画：往下滑出
  bool _hidden = false; // 当前是否已隐藏，避免重复触发

  @override
  void initState() {
    super.initState();

    _barCtrl = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 240),
    );
    _slide = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, 1), // 向下滑出屏幕
    ).animate(CurvedAnimation(parent: _barCtrl, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _barCtrl.dispose();
    super.dispose();
  }

  void _hide() {
    if (!_hidden) {
      _hidden = true;
      _barCtrl.forward();
    }
  }

  void _show() {
    if (_hidden) {
      _hidden = false;
      _barCtrl.reverse();
    }
  }

  bool _onScroll(ScrollNotification n) {
    // 只处理“垂直”滚动；忽略 PageView 的“水平”切换
    if (n.metrics.axis != Axis.vertical) return false;

    // 开始/进行中：隐藏 (n is ScrollStartNotification)
    // 这里先取消开始滑动判断
    if (n is UserScrollNotification) {
      // (UserScrollNotification 包含拖拽和惯性阶段；direction idle 的情况后面会显示)
      _hide();
    }

    // 结束/空闲：显示
    if (n is ScrollEndNotification ||
        (n is UserScrollNotification && n.direction == ScrollDirection.idle)) {
      _show();
    }
    return false; // 不拦截
  }

  @override
  Widget build(BuildContext context) {
    var editState = ref.watch(currentRecordEditStateProvider);
    Widget action = const SizedBox();
    if (widget.action != null) {
      action = Padding(
        padding: const EdgeInsets.only(left: 10),
        child: widget.action,
      );
    }
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        debugPrint("onWillPop-----$didPop");
        SystemNavigator.pop();
      },
      child: Scaffold(
        body: Stack(
          children: [
            NotificationListener<ScrollNotification>(
              onNotification: _onScroll,
              child: widget.pageView,
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                transform: Matrix4.translationValues(0, editState ? 200 : 0, 0),
                child: SlideTransition(
                  position: _slide,
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.paddingOf(context).bottom + 16,
                    ),
                    child: Row(
                      children: [
                        const SizedBox(width: 16),
                        Expanded(child: widget.bottomTabBar),
                        action,
                        const SizedBox(width: 16),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class TabItemData {
  String title;
  String icon;
  String activeIcon;
  int index;

  TabItemData(this.index, this.title, this.activeIcon, this.icon);
}

// 首页tab列表
final homeTabs = [
  TabItemData(0, "作品", glideHomeActive, glideHome),
  TabItemData(1, "同款", glideSameActive, glideSame),
  TabItemData(2, "作品", glideProductActive, glideProduct),
  TabItemData(3, "我的", glideMeActive, glideMe),
];

class HomeTabBarItem extends StatelessWidget {
  const HomeTabBarItem({
    super.key,
    required this.tabItemData,
    this.currentIndex = 0,
  });

  final TabItemData tabItemData;
  final int? currentIndex;

  @override
  Widget build(BuildContext context) {
    var select = currentIndex == tabItemData.index;
    Widget child;
    if (select) {
      child = Image.asset(tabItemData.activeIcon, width: 22, height: 22);
    } else {
      child = Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(tabItemData.icon, width: 18, height: 18),
          const SizedBox(height: 4),
          Text(
            tabItemData.title,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.white,
            ),
          ),
        ],
      );
    }
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14),
      ),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (child, animation) {
          // 淡入淡出 + 上下平移动画
          final offsetAnimation = Tween<Offset>(
            begin: const Offset(0, 0.1), // 从下往上滑入
            end: Offset.zero,
          ).animate(animation);

          final scaleAnimation = Tween<double>(
            begin: 0.9,
            end: 1.0,
          ).animate(animation);

          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: offsetAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            ),
          );
        },
        child: child,
      ),
    );
  }
}

// 首页tab背景
class HomeTabBg extends StatelessWidget {
  const HomeTabBg({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: homeTabs
          .map(
            (e) => Expanded(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 2.5),
                decoration: BoxDecoration(
                  color: const Color(0xFF18161A),
                  borderRadius: BorderRadius.circular(14),
                  border: Border.all(
                    color: const Color(0xFF3A3A3A),
                    width: 0.2,
                  ),
                ),
              ),
            ),
          )
          .toList(),
    );
  }
}
