import 'package:flutter/material.dart';
import 'package:text_generation_video/app/provider/audio_img_to_video/audio_case_provider.dart';
import 'package:text_generation_video/config/icon_address.dart';

/// 上传形象建议弹窗
class UploadPhotoDialogWidget extends StatelessWidget {
  final PageType pageType;
  final VoidCallback onTakePhoto;
  final VoidCallback onSelectPhoto;

  const UploadPhotoDialogWidget({
    super.key,
    required this.pageType,
    required this.onTakePhoto,
    required this.onSelectPhoto,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            "上传形象建议",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          Image.asset(syncSingingImgAdivce, width: double.infinity, height: 360),
          const SizedBox(height: 16),
          _buildDisclaimerText(),
          const SizedBox(height: 16),
          _buildButtons(context, onTakePhoto, onSelectPhoto),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildDisclaimerText() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: const Text(
        "本功能将可能采取敏感信息用于AI合成，如上传非本人信息、请确保已获得权利人的明确许可且不会用于违法违规及侵权行为。",
        style: TextStyle(
          fontSize: 12,
          color: Colors.white54,
        ),
      ),
    );
  }

  Widget _buildButtons(
    BuildContext context,
    VoidCallback? onTakePhoto,
    VoidCallback? onSelectPhoto,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 140,
          child: _buildButton(
            "直接拍摄",
            const Color(0xff234C44),
            () {
              Navigator.pop(context);
              onTakePhoto?.call();
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildButton(
            "相册倒入",
            const Color(0xFF30E6B8),
            () {
              Navigator.pop(context);
              onSelectPhoto?.call();
            },
            isMain: true,
          ),
        ),
      ],
    );
  }

  Widget _buildButton(String text, Color color, VoidCallback onTap,
      {bool isMain = false}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 140,
        height: 56,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(16),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: isMain ? Colors.black : const Color(0xff30E6B8),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
