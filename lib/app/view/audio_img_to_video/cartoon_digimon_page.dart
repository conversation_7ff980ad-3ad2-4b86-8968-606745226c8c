import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/view/audio_img_to_video/widget/cartoon_digimon_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import '../../provider/record/record_provider.dart';
import '../../widgets/common/work_record_ation.dart';

class CartoonDigimonPage extends ConsumerWidget {
  const CartoonDigimonPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "卡通数字人",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          WorkRecordAction(workType: WorkRecordType.video.type),
          const SizedBox(width: 16),
        ],
      ),
      body: const CartoonDigimonWidget(),
    );
  }
}
