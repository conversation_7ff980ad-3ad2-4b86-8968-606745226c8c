import 'dart:async';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/repository/modals/record/record_detail.dart';
import 'package:text_generation_video/app/repository/modals/record/upload_detail.dart';

import '../../../../config/icon_address.dart';

class RecordCompareWidget extends ConsumerStatefulWidget {
  const RecordCompareWidget({
    super.key,
    this.recordDetail,
    this.uploadDetail,
  });

  final RecordDetail? recordDetail;
  final UploadDetail? uploadDetail;

  @override
  RecordCompareWidgetState createState() => RecordCompareWidgetState();
}

class RecordCompareWidgetState extends ConsumerState<RecordCompareWidget> {
  bool showRecord = true;

  Future<double?> _getImage(ImageProvider provider) async {
    final completer = Completer<ui.Image>();
    final stream = provider.resolve(const ImageConfiguration());
    late final ImageStreamListener listener;
    listener = ImageStreamListener((ImageInfo info, bool _) {
      completer.complete(info.image);
      stream.removeListener(listener);
    });
    stream.addListener(listener);

    final image = await completer.future;
    return image.width / image.height;
  }

  Widget _buildCompare() {
    String? recordUrl = widget.recordDetail?.workContent;
    String? uploadUrl = widget.uploadDetail?.uploadContent;
    return Stack(
      children: [
        Positioned.fill(
          child: CachedNetworkImage(
            imageUrl: uploadUrl ?? "",
            errorWidget: (context, o, s) {
              return const SizedBox();
            },
          ),
        ),
        Positioned.fill(
          child: Opacity(
            opacity: showRecord ? 1.0 : 0.0,
            child: CachedNetworkImage(
              imageUrl: recordUrl ?? "",
              imageBuilder: (context, imageProvider) {
                return LayoutBuilder(
                  builder: (context, constraints) {
                    var maxWidth = constraints.maxWidth;
                    var maxHeight = constraints.maxHeight;
                    return Image(
                      image: imageProvider,
                      fit: BoxFit.contain,
                      frameBuilder: (
                        context,
                        child,
                        frame,
                        wasSynchronouslyLoaded,
                      ) {
                        if (frame == null) {
                          return const SizedBox(); // 加载中
                        }
                        return FutureBuilder<double?>(
                          future: _getImage(imageProvider),
                          builder: (context, snapshot) {
                            if (!snapshot.hasData) return child;
                            final asp = snapshot.data!;
                            // 计算适合可用空间的最佳尺寸
                            double width = maxWidth;
                            double height = width / asp;

                            // 如果计算的高度超过可用高度，则调整宽度
                            if (height > maxHeight) {
                              height = maxHeight;
                              width = height * asp;
                            }
                            return Stack(
                              alignment: Alignment.center,
                              children: [
                                SizedBox(
                                  width: width,
                                  height: height,
                                  child: Image.asset(
                                    mattingTransparentBg,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                child,
                              ],
                            );
                          },
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
        ),
        // Positioned.fill(
        //   child: Opacity(
        //     opacity: showRecord ? 1.0 : 0.0,
        //     child: Container(
        //       decoration: const BoxDecoration(
        //           image: DecorationImage(
        //         image: AssetImage(mattingTransparentBg),
        //         fit: BoxFit.fill,
        //       )),
        //       child: CachedNetworkImage(
        //         imageUrl: recordUrl ?? "",
        //         errorWidget: (context, o, s) {
        //           return const SizedBox();
        //         },
        //       ),
        //     ),
        //   ),
        // )
      ],
    );
  }

  // 对比按钮
  Widget _buildCompareBtn() {
    return Padding(
      padding: const EdgeInsets.only(top: 19, bottom: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTapUp: (d) {
              setState(() {
                showRecord = true;
              });
            },
            onTapDown: (d) {
              setState(() {
                showRecord = false;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 14,
                vertical: 5,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Row(
                children: [
                  Image.asset(recordCompareIcon, width: 15),
                  const SizedBox(width: 7),
                  const Text(
                    "前后对比",
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(child: _buildCompare()),
        _buildCompareBtn(),
      ],
    );
  }
}
