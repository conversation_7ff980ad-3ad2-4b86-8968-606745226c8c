import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/repository/modals/record/record_detail.dart';
import 'package:text_generation_video/app/repository/modals/record/upload_detail.dart';

import '../../../../config/icon_address.dart';

class RecordCompareWidget extends ConsumerStatefulWidget {
  const RecordCompareWidget({
    super.key,
    this.recordDetail,
    this.uploadDetail,
  });

  final RecordDetail? recordDetail;
  final UploadDetail? uploadDetail;

  @override
  RecordCompareWidgetState createState() => RecordCompareWidgetState();
}

class RecordCompareWidgetState extends ConsumerState<RecordCompareWidget> {
  bool showRecord = true;

  Widget _buildCompare() {
    String? recordUrl = widget.recordDetail?.workContent;
    String? uploadUrl = widget.uploadDetail?.uploadContent;
    return Stack(
      children: [
        Positioned.fill(
          child: CachedNetworkImage(
            imageUrl: uploadUrl ?? "",
            errorWidget: (context, o, s) {
              return const SizedBox();
            },
          ),
        ),
        Positioned.fill(
          child: Opacity(
            opacity: showRecord ? 1.0 : 0.0,
            child: CachedNetworkImage(
              imageUrl: recordUrl ?? "",
              errorWidget: (context, o, s) {
                return const SizedBox();
              },
            ),
          ),
        )
      ],
    );
  }

  // 对比按钮
  Widget _buildCompareBtn() {
    return Padding(
      padding: const EdgeInsets.only(top: 19, bottom: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          GestureDetector(
            onTapUp: (d) {
              setState(() {
                showRecord = true;
              });
            },
            onTapDown: (d) {
              setState(() {
                showRecord = false;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 14,
                vertical: 5,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFF2D2C2F),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Row(
                children: [
                  Image.asset(recordCompareIcon, width: 15),
                  const SizedBox(width: 7),
                  const Text(
                    "前后对比",
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(child: _buildCompare()),
        _buildCompareBtn(),
      ],
    );
  }
}
