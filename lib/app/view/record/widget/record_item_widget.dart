import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';
import 'package:text_generation_video/app/repository/modals/record/work_record.dart';
import 'package:text_generation_video/config/icon_address.dart';

class RecordItemWidget extends ConsumerWidget {
  const RecordItemWidget({
    super.key,
    required this.workRecord,
    required this.itemWidth,
  });

  final WorkRecord workRecord;

  final double itemWidth;

  Widget _buildContent() {
    if (workRecord.state == 1) {
      // 生成中
      return Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            recordWorkingHolder,
            width: itemWidth,
            fit: BoxFit.fitWidth,
          ),
          Column(
            children: [
              Image.asset(recordWorkingIcon, width: 28),
              const SizedBox(height: 12),
              const Text(
                "生成中…",
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                "预估需要1-15分钟",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
              const Text(
                "请耐心等待",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      );
    }
    if (workRecord.state == 2) {
      // 生成成功
      var recordDetail = workRecord.recordDetailList?.first;
      if (recordDetail == null) return const SizedBox();
      var workType = recordDetail.workType;
      Widget content = const SizedBox();
      if (workType == 2) {
        // 图片
        content = ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: CachedNetworkImage(
            imageUrl: recordDetail.workContent ?? "",
            width: itemWidth,
            fit: BoxFit.fitWidth,
            placeholder: (c, s) {
              return Image.asset(
                recordWorkingHolder,
                width: itemWidth,
                fit: BoxFit.fitWidth,
              );
            },
            errorWidget: (context, o, s) {
              return const SizedBox(height: 80);
            },
          ),
        );
      }
      if (workType == 3) {
        // 视频
        content = Stack(
          alignment: Alignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: CachedNetworkImage(
                imageUrl: workRecord.previewImgUrl ?? "",
                width: itemWidth,
                fit: BoxFit.fitWidth,
                placeholder: (c, s) {
                  return Image.asset(
                    recordWorkingHolder,
                    width: itemWidth,
                    fit: BoxFit.fitWidth,
                  );
                },
                errorWidget: (context, o, s) {
                  return const SizedBox(height: 80);
                },
              ),
            ),
            Image.asset(recordWorkingPlay, width: 36),
          ],
        );
      }
      return content;
    }
    if (workRecord.state == 3) {
      // 生成失败
      return Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            recordWorkingHolder,
            width: itemWidth,
            fit: BoxFit.fitWidth,
          ),
          Column(
            children: [
              Image.asset(recordWorkingIcon, width: 28),
              const SizedBox(height: 12),
              const Text(
                "生成失败",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      );
    }
    return Container();
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var editState = ref.watch(currentRecordEditStateProvider);
    var deleteList = ref.watch(deleteRecordListProvider);
    bool inDelete = deleteList.any((e) => e.id == workRecord.id);
    return InkWell(
      onTap: () {
        if (editState && workRecord.state != 1) {
          // 编辑状态，选择或者反选
          ref.read(deleteRecordListProvider.notifier).addOrRemove(workRecord);
        } else {
          if (workRecord.state == 2) {
            context.push(
              "/$recordDetailPage",
              extra: {
                "workRecord": workRecord,
                "tag": "record_item_${workRecord.id}"
              },
            );
          }
        }
      },
      child: Stack(
        children: [
          Container(
            alignment: Alignment.center,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildContent(),
                const SizedBox(height: 10),
                Text(
                  "${workRecord.createTime}",
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF8A8D93),
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  "${workRecord.workName}",
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ],
            ),
          ),
          if (editState && workRecord.state != 1)
            Positioned(
              right: 10,
              top: 10,
              child: Container(
                width: 19,
                height: 19,
                decoration: BoxDecoration(
                  color: inDelete
                      ? const Color(0xFF30E6B8)
                      : const Color(0x27000000),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color:
                        inDelete ? Colors.transparent : const Color(0xFFFDFDFD),
                    width: 1,
                  ),
                ),
                child: inDelete
                    ? const Icon(
                        Icons.check,
                        size: 14,
                        color: Color(
                          0xFF18161A,
                        ),
                      )
                    : null,
              ),
            ),
        ],
      ),
    );
  }
}
