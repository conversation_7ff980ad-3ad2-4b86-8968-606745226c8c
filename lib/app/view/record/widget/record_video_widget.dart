import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/creation/creation_provider.dart';
import 'package:text_generation_video/app/repository/modals/record/record_detail.dart';
import 'package:text_generation_video/app/widgets/common/checkbox_agreen.dart';
import 'package:text_generation_video/app/widgets/video_play/video_play_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/constant.dart';
import '../../../../config/icon_address.dart';
import '../../../navigation/router.dart';

class RecordVideoWidget extends ConsumerStatefulWidget {
  const RecordVideoWidget({
    super.key,
    required this.recordDetail,
    required this.firstFrameImage,
  });

  final RecordDetail? recordDetail;
  final String? firstFrameImage;

  @override
  RecordVideoWidgetState createState() => RecordVideoWidgetState();
}

class RecordVideoWidgetState extends ConsumerState<RecordVideoWidget> {

  final List<String> keys = ["《视频使用公约》"];

  /// keys点击
  void keysTap(String key) {
    if (key == keys.first) {
      context.push(
        "/$webPage",
        extra: {"title": "", "url": Constant.videoUsePrivacy},
      );
    }
  }

  // 协议
  Widget _buildPrivacy() {
    List<String> content = [
      "我已阅读并同意",
      keys.first,
    ];
    return Container(
      padding: const EdgeInsets.only(top: 46, bottom: 10, left: 16),
      child: CheckboxAgreen(
        value: ref.watch(videoUserPrivacyProvider),
        onChanged: (value) {
          ref.read(videoUserPrivacyProvider.notifier).setPrivacy(value);
        },
        size: 12,
        selectedIcon: Image.asset(agreementCheck, width: 12),
        unselectedColor: const Color(0xFF222123),
        child: HighlightText(
          data: content,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF8A8D93),
          ),
          keyStyle: const TextStyle(
            color: Color(0xFF30E6B8),
            fontSize: 12,
          ),
          keys: keys,
          onTapCallback: (String key) {
            keysTap(key);
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Center(
            child: VideoPlayWidget(
              videoUrl: widget.recordDetail?.workContent ?? '',
              firstFrameImage: widget.firstFrameImage,
            ),
          ),
        ),
        _buildPrivacy(),
      ],
    );
  }
}
