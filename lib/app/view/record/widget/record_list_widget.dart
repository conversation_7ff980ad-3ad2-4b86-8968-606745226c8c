import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:waterfall_flow/waterfall_flow.dart';

import '../../../../config/icon_address.dart';
import '../../../provider/record/record_provider.dart';
import '../../home/<USER>/portfolio_tab_widget.dart';
import 'record_item_widget.dart';

class RecordListWidget extends ConsumerStatefulWidget {
  const RecordListWidget({
    super.key,
    this.categoryType,
  });

  final int? categoryType;

  @override
  RecordListWidgetState createState() => RecordListWidgetState();
}

class RecordListWidgetState extends ConsumerState<RecordListWidget> {
  int crossAxisCount = 2;
  double crossAxisSpacing = 11.0;
  double mainAxisSpacing = 14.0;

  void _deleteDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Color(0xFF222123),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(14),
              topRight: Radius.circular(14),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              const Text(
                "确定要删除吗",
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 20),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2C2F),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () {
                        context.pop();
                        ref
                            .read(deleteRecordListProvider.notifier)
                            .deleteRecord();
                      },
                      child: const Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 20),
                            child: Text(
                              "确认",
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    const Divider(
                      color: Color(0xFF565656),
                      height: 0.6,
                      indent: 16,
                      endIndent: 16,
                    ),
                    InkWell(
                      onTap: () {
                        context.pop();
                      },
                      child: const Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: 20),
                            child: Text(
                              "取消",
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFFF4565F),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
            ],
          ),
        );
      },
    );
  }

  // empty widget
  Widget _emptyWidget() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(recordEmptyIcon),
        const SizedBox(height: 10),
        const Text(
          "暂无内容",
          style: TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
        ),
        SizedBox(height: MediaQuery.paddingOf(context).top + 40),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    var editState = ref.watch(currentRecordEditStateProvider);
    var itemWidth = (MediaQuery.sizeOf(context).width - 32 - mainAxisSpacing) /
        crossAxisCount;
    RecordListResult result = ref.watch(recordPortfolioListProvider);

    Widget child = Center(
      child: _emptyWidget(),
    );

    if (result.workRecordList != null && result.workRecordList!.isNotEmpty) {
      child = Column(
        children: [
          Expanded(
            child: CustomListView(
              onRefresh: () async {
                await ref.read(recordPortfolioListProvider.notifier).loadData();
              },
              onLoadMore: () async {
                await ref.read(recordPortfolioListProvider.notifier).loadMore();
              },
              data: result.workRecordList,
              footerState: result.loadState,
              isMessage: true,
              padding: EdgeInsets.fromLTRB(
                16,
                0,
                16,
                MediaQuery.paddingOf(context).bottom + 91,
              ),
              header: const Padding(
                padding: EdgeInsets.fromLTRB(16, 10, 0, 16),
                child: Text(
                  "作品将为你保存7天，请及时下载",
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF8A8D93),
                  ),
                ),
              ),
              renderItem: (context, index, o) {
                return RecordItemWidget(workRecord: o, itemWidth: itemWidth);
              },
              sliverWaterfallFlowDelegate:
                  SliverWaterfallFlowDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: crossAxisSpacing,
                mainAxisSpacing: mainAxisSpacing,
                collectGarbage: (List<int> garbages) {
                  // print('collect garbage : $garbages');
                },
                viewportBuilder: (int firstIndex, int lastIndex) {
                  // print('viewport : [$firstIndex,$lastIndex]');
                },
              ),
            ),
          ),
          if (editState)
            InkWell(
              onTap: _deleteDialog,
              child: Container(
                color: Colors.black,
                padding: EdgeInsets.only(
                  top: 16,
                  bottom: MediaQuery.paddingOf(context).bottom + 25,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(deleteEditIcon, width: 15),
                    const SizedBox(width: 6),
                    const Text(
                      "删除",
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      );
    }

    return Column(
      children: [
        PortfolioTabWidget(categoryType: widget.categoryType),
        Expanded(child: child),
      ],
    );
  }
}
