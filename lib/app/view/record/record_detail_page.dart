import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/repository/modals/record/record_detail.dart';
import 'package:text_generation_video/app/repository/modals/record/upload_detail.dart';
import 'package:text_generation_video/app/view/record/widget/record_compare_widget.dart';
import 'package:text_generation_video/app/view/record/widget/record_video_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';
import '../../provider/creation/creation_provider.dart';
import '../../repository/modals/record/work_record.dart';
import '../../widgets/appbar/leading.dart';

class RecordDetailPage extends ConsumerWidget {
  const RecordDetailPage({
    super.key,
    this.data,
  });

  final Map? data;

  Widget _buildContent(WorkRecord? workRecord) {
    var detailList = workRecord?.recordDetailList;
    RecordDetail? recordDetail;
    int? workType;
    if (detailList != null && detailList.isNotEmpty) {
      recordDetail = detailList.first;
      workType = detailList.first.workType;
    }
    if (workType == 2) {
      // 图片
      var uploadDetailList = workRecord?.uploadDetailList;
      UploadDetail? uploadDetail;
      if (uploadDetailList != null && uploadDetailList.isNotEmpty) {
        uploadDetail = uploadDetailList.first;
      }
      return RecordCompareWidget(
        recordDetail: recordDetail,
        uploadDetail: uploadDetail,
      );
    }
    if (workType == 3) {
      // 视频
      return RecordVideoWidget(
        recordDetail: recordDetail,
        firstFrameImage: workRecord?.previewImgUrl,
      );
    }
    return const SizedBox();
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var workRecord = data?["workRecord"] as WorkRecord?;
    var videoUsePrivacy = ref.watch(videoUserPrivacyProvider);
    var detailList = workRecord?.recordDetailList;
    RecordDetail? recordDetail;
    int? workType;
    if (detailList != null && detailList.isNotEmpty) {
      recordDetail = detailList.first;
      workType = detailList.first.workType;
    }
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: Column(
        children: [
          Expanded(child: _buildContent(workRecord)),
          GradientButton(
            onPress: () {
              if (workType == 2) {
                // 下载图片
                ref
                    .read(creationVideoProvider.notifier)
                    .downloadPhoto(recordDetail?.workContent);
              } else if (workType == 3) {
                // 下载视频
                ref
                    .read(creationVideoProvider.notifier)
                    .downloadVideo(recordDetail?.workContent);
              }
            },
            enable: workType != 3 || (workType == 3 && videoUsePrivacy),
            margin: EdgeInsets.fromLTRB(
              16,
              0,
              16,
              MediaQuery.paddingOf(context).bottom + 20,
            ),
            padding: const EdgeInsets.symmetric(vertical: 15),
            shadow: false,
            radius: 16,
            gradient: const LinearGradient(
              colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
            ),
            child: const Text(
              "保存到相册",
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF18161A),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
