import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/view/record/widget/record_list_widget.dart';

import '../../../config/icon_address.dart';
import '../../provider/record/record_provider.dart';
import '../../widgets/appbar/leading.dart';

class WorkRecordPage extends ConsumerWidget {
  const WorkRecordPage({
    super.key,
    this.categoryType,
  });

  final int? categoryType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var editState = ref.watch(currentRecordEditStateProvider);
    Widget icon = editState
        ? Image.asset(cancelEditIcon, width: 8)
        : Image.asset(profileEditIcon, width: 14);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        debugPrint("onWillPop-----$didPop");
        ref.read(currentRecordEditStateProvider.notifier).setEditState(false);
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            "我的作品",
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          leading: const Leading(
            color: Colors.white,
          ),
          actions: [
            InkWell(
              onTap: () {
                ref
                    .read(currentRecordEditStateProvider.notifier)
                    .setEditState(!editState);
              },
              child: Row(
                children: [
                  icon,
                  const SizedBox(width: 4),
                  Text(
                    editState ? "取消" : "管理",
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: RecordListWidget(categoryType: categoryType),
      ),
    );
  }
}
