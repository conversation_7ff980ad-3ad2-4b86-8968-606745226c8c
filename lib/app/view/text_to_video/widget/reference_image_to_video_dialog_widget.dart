import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/prefs_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

class ReferenceImageToVideoDialogWidget extends ConsumerStatefulWidget {
  const ReferenceImageToVideoDialogWidget({super.key});

  @override
  ConsumerState<ReferenceImageToVideoDialogWidget> createState() => _ReferenceImageToVideoDialogWidgetState();
}

class _ReferenceImageToVideoDialogWidgetState extends ConsumerState<ReferenceImageToVideoDialogWidget> {
  bool _dontShowAgain = false;

  @override
  void initState() {
    super.initState();
    _dontShowAgain = PrefsUtil().getBool(PrefsKeys.imageCropDialogDontShow) ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        0,
        16,
        MediaQuery.paddingOf(context).bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: Color(0xFF222123),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(14),
          topRight: Radius.circular(14),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 12),
          Container(
            width: 29,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFF39373B),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            "请从图片中裁剪出你想参考的主体",
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 26),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(14),
              color: Colors.grey,
            ),
            width: double.infinity,
            height: 190,
          ),
          const SizedBox(height: 16),
          InkWell(
            onTap: () {
              setState(() {
                _dontShowAgain = !_dontShowAgain;
              });
            },
            child: Row(
              children: [
                _dontShowAgain
                    ? Image.asset(correctExamIcon, width: 16)
                    : Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.white,
                            width: 1,
                          ),
                        ),
                      ),
                const SizedBox(width: 6),
                const Text(
                  "已掌握使用技巧，不再提示",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          GradientButton(
            onPress: () {
              PrefsUtil().setBool(PrefsKeys.imageCropDialogDontShow, _dontShowAgain);
              Navigator.pop(context);
            },
            padding: const EdgeInsets.symmetric(vertical: 15),
            radius: 16,
            shadow: false,
            gradient: const LinearGradient(
              colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
            ),
            child: const Text(
              "去剪裁",
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF18161A),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
