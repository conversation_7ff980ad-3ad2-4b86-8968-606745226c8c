import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:extended_image/extended_image.dart';
import 'package:path_provider/path_provider.dart';
import 'package:text_generation_video/app/view/text_to_video/widget/reference_image_to_video_dialog_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/utils/toast_util.dart';
import 'package:text_generation_video/utils/prefs_util.dart';
import 'package:ui_widgets/ui_widgets.dart';
import 'package:image_editor/image_editor.dart';

class ImageCropPage extends ConsumerStatefulWidget {
  const ImageCropPage({
    super.key,
    required this.imagePath,
    required this.imageIndex,
  });

  final String imagePath;
  final int imageIndex;

  @override
  ConsumerState<ImageCropPage> createState() => _ImageCropPageState();
}

class _ImageCropPageState extends ConsumerState<ImageCropPage> {
  final GlobalKey<ExtendedImageEditorState> _editorKey =
      GlobalKey<ExtendedImageEditorState>();
  bool _isProcessing = false;

  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bool shouldSkipDialog =
          PrefsUtil().getBool(PrefsKeys.imageCropDialogDontShow) ?? false;
      if (!shouldSkipDialog && mounted) {
        showTipsBottomSheet();
      }
    });
  }

  void showTipsBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return const ReferenceImageToVideoDialogWidget();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          centerTitle: true,
          toolbarHeight: 44.h,
          title: Text(
            "选择参考图和主体",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
          leading: const Leading(
            color: Colors.white,
          )),
      body: Column(
        children: [
          Expanded(child: _buildCropImage()),
          const SizedBox(height: 16),
          Text(
            "请裁剪出你要参考的主题\n每张参考图最多只能有一个参考体",
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          GradientButton(
            onPress: _isProcessing ? null : _handleConfirm,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 15),
            radius: 16,
            shadow: false,
            gradient: const LinearGradient(
              colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
            ),
            child: _isProcessing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFF18161A)),
                    ),
                  )
                : const Text(
                    "确定",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF18161A),
                    ),
                  ),
          ),
          SizedBox(height: MediaQuery.paddingOf(context).bottom + 16),
        ],
      ),
    );
  }

  Widget _buildCropImage() {
    return ExtendedImage.file(
      File(widget.imagePath),
      mode: ExtendedImageMode.editor,
      fit: BoxFit.contain,
      extendedImageEditorKey: _editorKey,
      cacheRawData: true,
      initEditorConfigHandler: (state) {
        return EditorConfig(
          maxScale: 8.0,
          cropRectPadding: const EdgeInsets.all(20.0),
          hitTestSize: 5,
          cropAspectRatio: CropAspectRatios.custom,
          initCropRectType: InitCropRectType.layoutRect,
          initialCropAspectRatio: CropAspectRatios.ratio4_3,
          cropLayerPainter: const EditorCropLayerPainter(),
          cornerColor: Colors.white,
          lineColor: Colors.white,
        );
      },
    );
  }

  //  处理确认剪裁
  Future<void> _handleConfirm() async {
    if (_isProcessing) return; // 避免重复点击

    final state = _editorKey.currentState;
    if (state == null) {
      ToastUtil.showToast("编辑器未初始化");
      return;
    }

    setState(() => _isProcessing = true);

    try {
      // 原始图片数据
      final Uint8List img = state.rawImageData;

      // 裁剪区域
      final Rect? cropRect = state.getCropRect();

      // 组装裁剪参数
      final option = ImageEditorOption();
      if (state.editAction?.needCrop == true && cropRect != null) {
        option.addOption(ClipOption.fromRect(cropRect));
      }

      // 执行裁剪
      final Uint8List? result = await ImageEditor.editImage(
        image: img,
        imageEditorOption: option,
      );

      if (result == null) {
        ToastUtil.showToast('图片裁剪失败');
        return;
      }

      // 保存为临时文件
      final dir = await getTemporaryDirectory();
      final path =
          "${dir.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.png";
      final file = File(path);
      await file.writeAsBytes(result);

      if (mounted) Navigator.of(context).pop(file.path);
    } catch (e, s) {
      debugPrint("图片裁剪异常: $e\n$s");
      ToastUtil.showToast('剪裁图片失败，请重试');
    } finally {
      if (mounted) setState(() => _isProcessing = false);
    }
  }
}
