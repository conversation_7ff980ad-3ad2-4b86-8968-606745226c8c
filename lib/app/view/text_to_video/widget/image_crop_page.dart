import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:ui_widgets/ui_widgets.dart';

class ImageCropPage extends ConsumerWidget {
  const ImageCropPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
          backgroundColor: Colors.transparent,
          centerTitle: true,
          toolbarHeight: 44.h,
          title: Text(
            "选择参考图和主体",
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
            ),
          ),
          leading: const Leading(
            color: Colors.white,
          )),
      body: Column(
        children: [
          Expanded(child: _buildCropImage()),
          const SizedBox(height: 16),
          Text(
            "请裁剪出你要参考的主题\n每张参考图最多只能有一个参考体",
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          GradientButton(
            onPress: () {},
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 15),
            radius: 16,
            shadow: false,
            gradient: const LinearGradient(
              colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
            ),
            child: const Text(
              "确定",
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF18161A),
              ),
            ),
          ),
          SizedBox(height: MediaQuery.paddingOf(context).bottom),
        ],
      ),
    );
  }

  Widget _buildCropImage() {
    return Container(
      color: Colors.grey,
    );
  }
}
