import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart'
    show FunctionType;
import 'package:text_generation_video/app/provider/text_to_video/reference_image_upload_provider.dart';
import 'package:text_generation_video/app/provider/text_to_video/text_to_video_provider.dart';
import 'package:text_generation_video/app/view/text_to_video/dialog/text_to_video_dialog.dart';
import 'package:text_generation_video/app/view/text_to_video/widget/image_crop_page.dart';
import 'package:text_generation_video/app/widgets/audio/dashed_border.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';
import 'package:text_generation_video/app/widgets/image_upload/multi_image_upload_panel.dart';
import 'package:text_generation_video/app/widgets/video_case/video_case_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

class ReferenceImageToVideoWidget extends ConsumerStatefulWidget {
  const ReferenceImageToVideoWidget({
    super.key,
    this.caseShowTop = true,
    this.caseId,
  });

  final bool caseShowTop;
  final int? caseId;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ReferenceImageToVideoWidgetState();
}

class _ReferenceImageToVideoWidgetState
    extends ConsumerState<ReferenceImageToVideoWidget> {
  final FocusNode _focusNode = FocusNode();
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  // 处理图片选择和跳转到剪裁页面
  Future<void> _handleImagePick(int index) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image == null) {
      return;
    }
    // 跳转到剪裁页面，传递图片路径和索引
    if (mounted) {
      final result = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) => ImageCropPage(
            imagePath: image.path,
            imageIndex: index,
          ),
        ),
      );
      // 如果用户确认了剪裁，result 会包含剪裁后的图片路径
      if (result != null) {
        // 使用剪裁后的图片路径进行上传
        ref
            .read(referenceImageUploadProvider.notifier)
            .uploadImage(index, result);
      } else {
        // 取消了剪裁，直接上传原图
        ref
            .read(referenceImageUploadProvider.notifier)
            .uploadImage(index, image.path);
      }
    }
  }

  // 文本输入
  Widget _buildTextInput() {
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 0, 14, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image.asset(textToVideoCreativeIcon, width: 15, height: 15),
              const SizedBox(width: 2),
              const Text(
                "创意模式",
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          const Text(
            "请根据文字描述您想生成的视频内容",
            style: TextStyle(
              fontSize: 11,
              color: Color(0xFF6F737A),
            ),
          ),
          const SizedBox(height: 11),
          Container(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
            decoration: BoxDecoration(
              color: const Color(0xFF2D2C2F),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: const Color(0xFF565656), width: 0.6),
            ),
            child: TextField(
              onTapOutside: (event) {
                _focusNode.unfocus();
              },
              controller: ref.watch(videoEditTextProvider),
              focusNode: _focusNode,
              maxLength: 300,
              maxLines: 10,
              style: const TextStyle(fontSize: 12, color: Colors.white),
              buildCounter: (
                BuildContext context, {
                required int currentLength,
                required bool isFocused,
                required int? maxLength,
              }) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () {
                        ref.read(videoEditTextProvider.notifier).clean();
                      },
                      child: const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 6),
                        child: Text(
                          "清空",
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF595B60),
                          ),
                        ),
                      ),
                    ),
                    Text(
                      "$currentLength/$maxLength",
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF595B60),
                      ),
                    ),
                  ],
                );
              },
              decoration: const InputDecoration(
                border: InputBorder.none,
                hintText: "例如：一只花斑小狗，从车子后面探出头，对着路人眨眼睛，尾巴摇摆不停",
                hintStyle: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF8A8D93),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMultipleImageUpload() {
    final uploads = ref.watch(referenceImageUploadProvider);
    final notifier = ref.read(referenceImageUploadProvider.notifier);

    return Container(
      margin: const EdgeInsets.fromLTRB(14, 0, 14, 20),
      child: CustomPaint(
        painter: DashedBorderPainter(
          dashLength: 3,
          gapLength: 3,
          strokeWidth: 1,
          color: const Color(0xFF565656),
          radius: 14,
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Image.asset(multipleImageUpload, width: 40),
                  const SizedBox(width: 10),
                  const Expanded(
                    child: Text(
                      "最少上传1张参考图，最多上传4张参考图\n图片支持jpg/png格式，文件大小不超过10M",
                      style: TextStyle(
                        fontSize: 11,
                        color: Color(0xFF7C7F86),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              MultiImageUploadPanel(
                items: uploads,
                addLabel: '上传参考图',
                aspectRatio: 1.5,
                onPick: _handleImagePick,
                onRemove: notifier.removeImage,
                onRetry: _handleImagePick,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 参数设置
  Widget _buildParameter() {
    var crossAxisCount = 2;
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 0, 14, 20),
      child: Column(
        children: [
          Row(
            children: [
              Image.asset(textToVideoParameterIcon, width: 15, height: 15),
              const SizedBox(width: 2),
              const Text(
                "参数设置",
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFFFFFFFF),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          GridView(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 167 / 45,
            ),
            children: [
              _buildParameterItem(
                "视频比例：",
                ref.watch(
                  videoScaleParamProvider.select((value) => value.scale),
                ),
                () {
                  VideoScaleDialog.showVideoScaleDialog(context);
                },
              ),
              _buildParameterItem(
                "视屏时长：",
                ref.watch(
                  videoDurationParamProvider.select((value) => value.duration),
                ),
                () {
                  VideoDurationDialog.showVideoDurationDialog(context);
                },
              ),
              _buildParameterItem(
                "生成模式：",
                ref.watch(
                  videoClarityParamProvider.select((value) => value.clarity),
                ),
                () {
                  VideoClarityDialog.showVideoClarityDialog(context);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildParameterItem(
    String prefix,
    String value,
    Function() onPress,
  ) {
    return InkWell(
      onTap: onPress,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: const Color(0xFF2D2C2F),
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: const Color(0xFF565656), width: 0.6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  prefix,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ],
            ),
            const Icon(
              Icons.arrow_forward_ios_rounded,
              color: Colors.white,
              size: 14,
            ),
          ],
        ),
      ),
    );
  }

  // 热门案例
  Widget _buildCase() {
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 0, 14, 20),
      child: VideoCaseWidget(
        caseType: 3,
        initialCase: widget.caseId,
        onTry: (videoCase) {
          ref.read(referenceImageUploadProvider.notifier).caseValue(videoCase);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: SingleChildScrollView(
            padding: EdgeInsets.only(
              top: 10,
              bottom: MediaQuery.paddingOf(context).bottom + 134,
            ),
            child: Column(
              children: widget.caseShowTop
                  ? [
                      _buildCase(),
                      _buildMultipleImageUpload(),
                      _buildTextInput(),
                      _buildParameter(),
                    ]
                  : [
                      _buildMultipleImageUpload(),
                      _buildTextInput(),
                      _buildParameter(),
                      _buildCase(),
                    ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            // padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0x2518161A),
                  Color(0xFF18161A),
                  Color(0xFF18161A)
                ],
                // stops: [0.05, 0.06, 1],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: BottomActionBar(
              type: FunctionType.fusionVideo.type,
              onPress: () => ref
                  .read(referenceImageUploadProvider.notifier)
                  .handleGenerate(),
              buttonText: "生成视频",
            ),
          ),
        ),
      ],
    );
  }
}
