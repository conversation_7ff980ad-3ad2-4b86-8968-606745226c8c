import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';
import 'package:text_generation_video/app/view/text_to_video/widget/reference_image_to_video_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/work_record_ation.dart';

class ReferenceImageToVideoPage extends ConsumerWidget {
  const ReferenceImageToVideoPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "多图参考",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          WorkRecordAction(workType: WorkRecordType.video.type),
          const SizedBox(width: 16),
        ],
      ),
      body: const ReferenceImageToVideoWidget(),
    );
  }
}
