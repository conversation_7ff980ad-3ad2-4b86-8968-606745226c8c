import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: jewelry_aigc
/// @Package: app.view.login.widget
/// @ClassName: text_field_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/2/21 17:15
/// @UpdateUser: frankylee
/// @UpdateData: 2024/2/21 17:15
/// @UpdateRemark: 更新说明
class TextFieldWidget extends ConsumerStatefulWidget {
  const TextFieldWidget({
    super.key,
    required this.hintText,
    this.isPass = false,
    this.isVerification = false,
    this.textInputType,
    this.inputFormatters,
    this.validator,
    this.onSaved,
    this.onChange,
    this.suffixWidget,
    this.verificationWidget,
  });

  /// 输入框hint
  final String hintText;

  /// 是否是密码框
  final bool? isPass;

  /// 是否是验证码输入框
  final bool? isVerification;

  /// 输入文本类型，可以配合```inputFormatters```
  /// 实现只允许输入数字
  /// eg:
  /// textInputType: TextInputType.number,
  /// inputFormatters: <TextInputFormatter>[
  ///    FilteringTextInputFormatter.digitsOnly,
  /// ],
  final TextInputType? textInputType;

  final List<TextInputFormatter>? inputFormatters;

  /// 表单验证
  final String? Function(String?)? validator;

  /// 保存表单
  final void Function(String?)? onSaved;

  /// 输入变动
  final void Function(String)? onChange;

  /// 后缀
  final Widget? suffixWidget;

  /// 验证码组件
  final Widget? verificationWidget;

  @override
  TextFieldWidgetState createState() => TextFieldWidgetState();
}

class TextFieldWidgetState extends ConsumerState<TextFieldWidget> {
  bool obscureText = false;

  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    obscureText = widget.isPass ?? false;
  }

  /// 显示文字
  Widget suffixWidget() {
    Widget child = Container();

    /// 后缀
    if (widget.suffixWidget != null) {
      child = widget.suffixWidget!;
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        child,
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      // 不设置初始值，没有输入内容直接调用Form.of(context).validate()会返回true
      // 导致无法实现验证预期效果
      initialValue: "",
      validator: widget.validator,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      onSaved: widget.onSaved,
      builder: (FormFieldState<String> state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                color: const Color(0xFF29282B),
                border: Border.all(
                  color: const Color(0xFF565656),
                  width: 0.6,
                ),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      onTapOutside: (event) {
                        _focusNode.unfocus();
                      },
                      focusNode: _focusNode,
                      // 不在这里设置validator，因为我们在外层FormField中已经设置了
                      validator: null,
                      autovalidateMode: AutovalidateMode.disabled,
                      onSaved: null,
                      onChanged: (value) {
                        state.didChange(value);
                        if (widget.onChange != null) {
                          widget.onChange!(value);
                        }
                      },
                      inputFormatters: widget.inputFormatters,
                      keyboardType: widget.textInputType,
                      maxLength: widget.isVerification == true ? 6 : null,
                      obscureText: obscureText,
                      obscuringCharacter: "*",
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF94979D),
                      ),
                      cursorColor: const Color(0xFF00D873),
                      maxLines: 1,
                      decoration: InputDecoration(
                        contentPadding:
                            const EdgeInsets.fromLTRB(20, 14, 20, 14),
                        isCollapsed: true,
                        border: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        errorBorder: InputBorder.none,
                        focusedErrorBorder: InputBorder.none,
                        counterText: "",
                        errorStyle: const TextStyle(
                          height: 0,
                          color: Colors.transparent,
                        ),
                        hintStyle: const TextStyle(
                          fontSize: 16,
                          color: Color(0xFF94979D),
                        ),
                        hintText: widget.hintText,
                        suffix: suffixWidget(),
                      ),
                    ),
                  ),
                  widget.verificationWidget ?? const SizedBox(),
                ],
              ),
            ),
            if (state.hasError)
              Padding(
                padding: const EdgeInsets.only(top: 10, left: 20),
                child: Text(
                  state.errorText ?? "",
                  style: const TextStyle(
                    color: Color(0xFFF4565F),
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
