import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../provider/account/verification_provider.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: verification_code_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/6/7 11:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/6/7 11:45
/// @UpdateRemark: 更新说明
class VerificationCodeWidget extends StatefulWidget {
  const VerificationCodeWidget({super.key, required this.getVerCode});

  final void Function()? getVerCode;

  @override
  VerificationCodeWidgetState createState() => VerificationCodeWidgetState();
}

class VerificationCodeWidgetState extends State<VerificationCodeWidget> {
  Verification? _verification;

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        _verification = ref.watch(verificationProvider.notifier);
        int ms = ref.watch(verificationProvider);
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 2,
              height: 23,
              color: const Color(0xFF565656),
            ),
            InkWell(
              onTap: ms == 0 ? widget.getVerCode : null,
              child: Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  ms == 0 ? "获取验证码" : "重新发送(${ms}s)",
                  style: TextStyle(
                    fontSize: 16,
                    color: ms == 0
                        ? const Color(0xFF30E6B8)
                        : const Color(0xFF8A8D93),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _verification?.stop();
    super.dispose();
  }
}
