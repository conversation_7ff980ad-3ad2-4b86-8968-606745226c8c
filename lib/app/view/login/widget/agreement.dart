import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/widgets/common/checkbox_agreen.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../../../config/constant.dart';
import '../../../../config/icon_address.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: kusaiapp
/// @Package:
/// @ClassName: agreement
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/12/15 10:25
/// @UpdateUser: frankylee
/// @UpdateData: 2023/12/15 10:25
/// @UpdateRemark: 更新说明

typedef OnChange = void Function(bool value);

class Agreement extends StatefulWidget {
  const Agreement({
    super.key,
    required this.onChange,
    this.initValue = false,
  });

  final OnChange onChange;

  final bool? initValue;

  @override
  AgreementState createState() => AgreementState();
}

class AgreementState extends State<Agreement> {
  /// keys点击
  void keysTap(String key) {
    if (key == "《隐私政策》") {
      context.push(
        "/$webPage",
        extra: {"title": "用户隐私政策", "url": Constant.privacyPolicyUrl},
      );
    } else if (key == "《用户协议》") {
      context.push(
        "/$webPage",
        extra: {"title": "用户协议", "url": Constant.userAgreementUrl},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    List<String> content = [
      "我已阅读并同意",
      "《隐私政策》",
      "和",
      "《用户协议》",
    ];
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        CheckboxAgreen(
          value: widget.initValue ?? false,
          onChanged: (value) {
            widget.onChange.call(value);
          },
          size: 12,
          selectedIcon: Image.asset(agreementCheck, width: 12),
          unselectedColor: const Color(0xFF222123),
          child: HighlightText(
            data: content,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF8A8D93),
            ),
            keyStyle: const TextStyle(
              color: Color(0xFF30E6B8),
              fontSize: 12,
            ),
            keys: const [
              "《隐私政策》",
              "《用户协议》",
            ],
            onTapCallback: (String key) {
              keysTap(key);
            },
          ),
        ),
      ],
    );
  }
}
