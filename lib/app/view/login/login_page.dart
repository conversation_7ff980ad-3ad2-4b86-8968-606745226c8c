import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/account/verification_provider.dart';
import 'package:text_generation_video/app/view/login/dialog/login_privacy_dialog.dart';
import 'package:text_generation_video/app/view/login/widget/text_field_widget.dart';
import 'package:text_generation_video/app/view/login/widget/verification_code_widget.dart';
import 'package:text_generation_video/common/ext/ext.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../provider/account/auth_provider.dart';
import '../../widgets/appbar/leading.dart';
import 'widget/agreement.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: login_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/20 15:56
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/20 15:56
/// @UpdateRemark: 更新说明
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  LoginPageState createState() => LoginPageState();
}

class LoginPageState extends ConsumerState<LoginPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((d) {
      ref.read(authProvider.notifier).logout();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 44.h,
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: const SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(26, 41, 0, 31),
              child: Text(
                "手机号登录",
                style: TextStyle(
                  fontSize: 20,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            LoginWidget(),
          ],
        ),
      ),
    );
  }
}

class LoginWidget extends ConsumerWidget {
  const LoginWidget({super.key});

  /// 登录
  void _login(BuildContext context, WidgetRef ref) async {
    if (Form.of(context).validate()) {
      var agree = ref.read(agreementAgreeProvider);
      if (!agree) {
        var result = await LoginPrivacyDialog.showPrivacy();
        if (result == false) {
          return;
        }
      }

      /// 验证通过提交数据
      if (context.mounted) {
        Form.of(context).save();
        var result = await ref.read(authProvider.notifier).login();
        if (context.mounted) {
          if (result != null) {
            context.pop();
          }
        }
      }
    }
  }

  /// 获取验证码
  void _getVerCode(BuildContext context, WidgetRef ref) async {
    var result = await ref.read(verificationProvider.notifier).getVerifyCode();
    if (result) {
      ref.read(verificationProvider.notifier).start();
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Form(
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 26),
            child: TextFieldWidget(
              hintText: "请输入手机号码",
              textInputType: TextInputType.number,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChange: (value) {
                ref.watch(loginPhoneProvider.notifier).setPhone(value);
              },
              onSaved: (value) {
                ref.read(loginPhoneProvider.notifier).setPhone(value);
              },
              validator: (value) {
                if (value != null && value.trim().isEmpty) {
                  return "手机号不能为空";
                }
                if (value != null && !value.isValidPhone) {
                  return "手机号码格式不正确，请重新输入";
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 24),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 26),
            child: TextFieldWidget(
              hintText: "请输入验证码",
              isVerification: true,
              textInputType: TextInputType.number,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.digitsOnly,
              ],
              onSaved: (value) {
                ref.read(loginVerCodeProvider.notifier).setVerCode(value);
              },
              validator: (value) {
                if (value != null && value.trim().isEmpty) {
                  return "验证码不能为空";
                }
                return null;
              },
              verificationWidget: Consumer(
                builder: (context, ref, child) {
                  return VerificationCodeWidget(
                    getVerCode: () => _getVerCode(context, ref),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 46),
          Consumer(
            builder: (context, ref, child) {
              return GradientButton(
                radius: 25,
                margin: const EdgeInsets.symmetric(horizontal: 26),
                padding: const EdgeInsets.symmetric(vertical: 14),
                onPress: () => _login(context, ref),
                gradient: const LinearGradient(
                  colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                ),
                shadow: false,
                child: const Text(
                  "登录",
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF18161A),
                  ),
                ),
              );
            },
          ),
          const LoginAgreement(),
        ],
      ),
    );
  }
}

class LoginAgreement extends ConsumerWidget {
  const LoginAgreement({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, left: 16, right: 16),
      child: Agreement(
        initValue: ref.watch(agreementAgreeProvider),
        onChange: (value) {
          ref.read(agreementAgreeProvider.notifier).setAgree(value);
        },
      ),
    );
  }
}
