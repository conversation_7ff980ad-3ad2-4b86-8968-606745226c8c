import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting
/// @ClassName: collect_message_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/18 17:29
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/18 17:29
/// @UpdateRemark: 更新说明
class CollectMessagePage extends ConsumerWidget {
  const CollectMessagePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "个人信息清单",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 10.h, 12.w, 16.h),
              child: Text(
                "收集个人信息清单明示我们非常重视信息安全,并采取一切合理可行的措施,保护您的个人信息。 ",
                style: TextStyle(
                  fontSize: 15.sp,
                  color: const Color(0xFFFFFFFF),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 4.h),
              child: Text(
                "1、设备信息",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFFFFFFFF),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: const Color(0xFFC2C2C2),
                    fontSize: 15.sp,
                  ),
                  children: const [
                    TextSpan(
                      text: "目的:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text:
                          "用来作为我们服务的虚拟用户唯一标识(信息采用MD5加密传输和存储)、了解产品机型适配性、保障账号安全与系统运行安全、判断账号风险、保障政策服务、统计及排查异常崩溃信息",
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: const Color(0xFFC2C2C2),
                    fontSize: 15.sp,
                  ),
                  children: const [
                    TextSpan(
                      text: "场景:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: "APP启动,同意隐私政策之后",
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: const Color(0xFFC2C2C2),
                    fontSize: 15.sp,
                  ),
                  children: const [
                    TextSpan(
                      text: "信息内容:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text:
                          "网络信息(WLAN接入点、基站等。传感器信息、IP地址、接入网络的方式、网络质量数据、移动网络信息)、设备信息(用户标识ID、设备标识符(IMEI/OAID/Android ID等)、操作系统的设置信息、设备硬件信息",
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: Text(
                "日志信息",
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFFFFFFFF),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 0),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: const Color(0xFFC2C2C2),
                    fontSize: 15.sp,
                  ),
                  children: const [
                    TextSpan(
                      text: "收集方式:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: "同意隐私政策后自动收集",
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 24.h, 12.w, 4.h),
              child: Text(
                "图片、视频信息",
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFFFFFFFF),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: const Color(0xFFC2C2C2),
                    fontSize: 15.sp,
                  ),
                  children: const [
                    TextSpan(
                      text: "目的:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: "用于用户使用相关图片、视频功能或提交意见反馈附件",
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: const Color(0xFFC2C2C2),
                    fontSize: 15.sp,
                  ),
                  children: const [
                    TextSpan(
                      text: "场景:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: "使用app对应图片、视频功能和意见反馈选择附件",
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                      color: const Color(0xFFC2C2C2), fontSize: 15.sp),
                  children: const [
                    TextSpan(
                      text: "信息内容:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: "用户自行选择的图片或视频",
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.fromLTRB(12.w, 0, 12.w, 6.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: const Color(0xFFC2C2C2),
                    fontSize: 15.sp,
                  ),
                  children: const [
                    TextSpan(
                      text: "收集方式:",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextSpan(
                      text: "用户使用有选择/上传图片或视频的功能后收集(非必要场景不会收集)",
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
