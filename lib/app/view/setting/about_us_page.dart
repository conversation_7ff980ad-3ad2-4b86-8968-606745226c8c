import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/platform_util.dart';
import 'package:text_generation_video/utils/toast_util.dart';

import '../../provider/home/<USER>';
import '../../widgets/appbar/leading.dart';

/// Copyright (C), 2021-2024, <PERSON><PERSON>
/// @ProjectName: text_generation_video
/// @Package: app.view.setting
/// @ClassName: about_us_page
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2024/4/11 11:23
/// @UpdateUser: frankylee
/// @UpdateData: 2024/4/11 11:23
/// @UpdateRemark: 更新说明
class AboutUsPage extends ConsumerWidget {
  const AboutUsPage({super.key});

  Widget _buildItem(String title, Widget sub, Function()? onPress) {
    Widget arrow = Icon(
      Icons.arrow_forward_ios_rounded,
      color: onPress != null ? const Color(0xFFB8B4B4) : Colors.transparent,
      size: 14.r,
    );
    return InkWell(
      onTap: onPress,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.white,
              ),
            ),
            Row(
              children: [
                sub,
                arrow,
              ],
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "关于我们",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Column(
              children: [
                Padding(padding: EdgeInsets.only(top: 29.h)),
                Image.asset(
                  aboutUsIconAndroid,
                  width: 69.w,
                  height: 69.h,
                ),
                Text(
                  PlatformUtils.isAndroid ? "Ai Glide" : "ZenAi",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                  ),
                ),
                Padding(padding: EdgeInsets.only(top: 22.h)),
                _buildItem(
                  "联系我们",
                  Text(
                    "<EMAIL>",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFFB8B4B4),
                    ),
                  ),
                  () {
                    Clipboard.setData(
                      const ClipboardData(text: "<EMAIL>"),
                    );
                    ToastUtil.showToast("已复制邮箱");
                  },
                ),
                Divider(
                  indent: 21.w,
                  endIndent: 21.w,
                  height: 1,
                  color: const Color(0xFF333333),
                ),
                _buildItem(
                  "版本号",
                  ref.watch(fetchVersionProvider).when(
                    data: (value) {
                      return Text(
                        "$value",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFFB8B4B4),
                        ),
                      );
                    },
                    error: (o, s) {
                      return Container();
                    },
                    loading: () {
                      return Container();
                    },
                  ),
                  null,
                ),
              ],
            ),
          ),
          // Container(
          //   margin: EdgeInsets.only(
          //     bottom: MediaQuery.paddingOf(context).bottom + 45.h,
          //   ),
          //   child: Text(
          //     "粤ICP备18068972号-9",
          //     style: TextStyle(
          //       fontSize: 14.sp,
          //       color: const Color(0xFF333333),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
