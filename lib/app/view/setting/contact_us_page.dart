import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../widgets/appbar/leading.dart';

class ContactUsPage extends ConsumerWidget {
  const ContactUsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "客服",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: Column(
        children: [
          const SizedBox(height: 30),
          const Text(
            "截图二维码，添加客服",
            style: TextStyle(fontSize: 16, color: Colors.white),
          ),
          const SizedBox(height: 30),
          const Text(
            "微信客服，为您服务",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
          const SizedBox(height: 30),
          const Divider(
            height: 0.6,
            color: Color(0xFF333333),
          ),
          const SizedBox(height: 29),
          CachedNetworkImage(
            imageUrl: "http://cdn.dyunwl.com/kefuweixin.png",
            width: 145,
            height: 145,
            errorWidget: (context, url, o) {
              return const SizedBox(
                width: 145,
                height: 145,
              );
            },
          ),
          const SizedBox(height: 27),
          const Text(
            "扫码二维码，添加我的企业微信",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
        ],
      ),
    );
  }
}
