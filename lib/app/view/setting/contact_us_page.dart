import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/provider/creation/creation_provider.dart';
import 'package:text_generation_video/config/constant.dart';

import '../../widgets/appbar/leading.dart';

class ContactUsPage extends ConsumerWidget {
  const ContactUsPage({super.key});

  void _longPressQrCode(BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Color(0xFF222123),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(14),
              topLeft: Radius.circular(14),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              InkWell(
                onTap: () {
                  context.pop();
                  ref
                      .read(creationVideoProvider.notifier)
                      .downloadPhoto(Constant.serviceQrCode);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: const BoxDecoration(
                    color: Color(0xFF2D2C2F),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("保存图片"),
                    ],
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Divider(
                  color: Color(0xFF565656),
                  height: 0.6,
                  indent: 16,
                  endIndent: 16,
                ),
              ),
              InkWell(
                onTap: () {
                  context.pop();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: const BoxDecoration(
                    color: Color(0xFF2D2C2F),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("取消"),
                    ],
                  ),
                ),
              ),
              SizedBox(height: MediaQuery.paddingOf(context).bottom),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 44.h,
        title: Text(
          "客服",
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
      ),
      body: Column(
        children: [
          const SizedBox(height: 30),
          const Text(
            "截图二维码，添加客服",
            style: TextStyle(fontSize: 16, color: Colors.white),
          ),
          const SizedBox(height: 30),
          const Text(
            "微信客服，为您服务",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
          const SizedBox(height: 30),
          const Divider(
            height: 0.6,
            color: Color(0xFF333333),
          ),
          const SizedBox(height: 29),
          GestureDetector(
            onLongPress: () {
              _longPressQrCode(context, ref);
            },
            child: CachedNetworkImage(
              imageUrl: Constant.serviceQrCode,
              width: 145,
              height: 145,
              errorWidget: (context, url, o) {
                return const SizedBox(
                  width: 145,
                  height: 145,
                );
              },
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            "长按图片保存到相册",
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF999999),
            ),
          ),
          const SizedBox(height: 27),
          const Text(
            "扫码二维码，添加客服微信",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
        ],
      ),
    );
  }
}
