import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/picture_dancing/picture_dancing_provider.dart';
import 'package:text_generation_video/app/provider/record/record_provider.dart';
import 'package:text_generation_video/app/view/picture_dancing/widget/dance_video_case_widget.dart';
import 'package:text_generation_video/app/view/video_generation/widget/button_widget.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/common/work_record_ation.dart';
import 'package:text_generation_video/config/icon_address.dart';

class PictureDancingPage extends ConsumerWidget {
  const PictureDancingPage({super.key});

  Widget headView(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      title: Text(
        "图片跳舞",
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.white,
        ),
      ),
      leading: const Leading(
        color: Color(0xFFFFFFFF),
      ),
      actions: [
        WorkRecordAction(workType: WorkRecordType.image.type),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget templateView(BuildContext context, WidgetRef ref) {
    var iamge =
        ref.watch(pictureDancingProvider.select((value) => value.image));
    var dancingTag =
        ref.watch(pictureDancingProvider.select((value) => value.dancingTag));
    var videoList =
        ref.watch(pictureDancingProvider.select((value) => value.videoList));
    var videoIndex =
        ref.watch(pictureDancingProvider.select((value) => value.videoIndex));
    var tagIndex =
        ref.watch(pictureDancingProvider.select((value) => value.index));
    return Column(
      children: [
        DanceVideoCaseWidget(
          tagData: dancingTag ?? [],
          listData: videoList ?? [],
          tagIndex: tagIndex,
          selectedIndex: videoIndex,
          onTap: (index) {
            ref.watch(pictureDancingProvider.notifier).getVideoIndex(index);
          },
          onTagTap: (index) {
            ref.watch(pictureDancingProvider.notifier).getTagIndex(index);
          },
        ),
        SizedBox(
          height: 15.h,
        ),
        InkWell(
          onTap: () {
            ref.read(pictureDancingProvider.notifier).selectImg();
          },
          child: Container(
            width: 343,
            height: 343,
            padding: EdgeInsets.only(
              top: iamge != null && iamge != "" ? 0 : 137,
            ),
            decoration: BoxDecoration(
              color: const Color(0xFF2D2C2F),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(
                color: const Color(0xFF565656),
                width: 1,
              ),
            ),
            child: Center(
              child: iamge != null && iamge != ""
                  ? Image.network(
                      iamge,
                      height: 343,
                      errorBuilder: (context, error, stackTrace) {
                        return Column(
                          children: [
                            Image.asset(
                              uploadIconImg,
                              width: 27,
                              height: 27,
                            ),
                            const SizedBox(
                              height: 9,
                            ),
                            Text(
                              "上传图片",
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: const Color(0xFF8A8D93),
                              ),
                            ),
                            Text(
                              "仅支持人物照片跳舞",
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: const Color(0xFF8A8D93),
                              ),
                            ),
                            Text(
                              "按要求上传图片将获得更好效果",
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: const Color(0xFF8A8D93),
                              ),
                            ),
                          ],
                        );
                      },
                    )
                  : Column(
                      children: [
                        Image.asset(
                          uploadIconImg,
                          width: 27,
                          height: 27,
                        ),
                        const SizedBox(
                          height: 9,
                        ),
                        Text(
                          "上传图片",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFF8A8D93),
                          ),
                        ),
                        Text(
                          "仅支持人物照片跳舞",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: const Color(0xFF8A8D93),
                          ),
                        ),
                        Text(
                          "按要求上传图片将获得更好效果",
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: const Color(0xFF8A8D93),
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var iamge =
        ref.watch(pictureDancingProvider.select((value) => value.image));
    return Container(
      color: const Color(0xFF18161A),
      child: Stack(
        children: [
          Image.asset(
            headBjImg,
            width: MediaQuery.sizeOf(context).width,
            // height: 197,
          ),
          headView(context),
          Container(
            margin: EdgeInsets.only(top: 94.h),
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        templateView(context, ref),
                        SizedBox(
                          height: 40.h,
                        ),
                      ],
                    ),
                  ),
                ),
                ButtonWidget(
                  disableState: iamge != null && iamge != "" ? false : true,
                  onTap: () {
                    ref
                        .read(pictureDancingProvider.notifier)
                        .videoDancingEffects();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
