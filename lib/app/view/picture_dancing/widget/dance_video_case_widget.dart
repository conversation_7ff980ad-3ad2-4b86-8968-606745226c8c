import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/app/view/video_generation/dialog/video_case_dialog.dart';

class DanceVideoCaseWidget extends StatefulWidget {
  final List tagData;
  final int tagIndex;
  final Function? onTagTap;
  final List listData;
  final int selectedIndex;
  final Function? onTap;

  const DanceVideoCaseWidget({
    super.key,
    required this.listData,
    required this.tagData,
    this.tagIndex = 0,
    this.selectedIndex = 0,
    this.onTap,
    this.onTagTap,
  });

  @override
  State<DanceVideoCaseWidget> createState() => _DanceVideoCaseWidgetState();
}

class _DanceVideoCaseWidgetState extends State<DanceVideoCaseWidget> {
  int selectedIndex = 0;
  int tagIndex = 0;

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    // selectedIndex = widget.selectedIndex;
    // tagIndex = widget.tagIndex;
  }

  @override
  Widget build(BuildContext context) {
    selectedIndex = widget.selectedIndex;
    tagIndex = widget.tagIndex;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            SizedBox(
              width: 16.w,
            ),
            Text(
              "跳舞模版",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFFFFFFFF),
              ),
            ),
            widget.listData.isNotEmpty
                ? Row(
                    children: [
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 7.w),
                        width: 1.w,
                        height: 12.h,
                        color: const Color(0xFF565656),
                      ),
                      Text(
                        "已选择${widget.listData[selectedIndex].caseName}",
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF6F737A),
                        ),
                      )
                    ],
                  )
                : const SizedBox(),
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: SizedBox(
            // 必须指定高度，因为横向 ListView 在垂直方向是收缩的
            height: 24.h,
            child: ListView(
              scrollDirection: Axis.horizontal, // 关键属性：设置为水平方向
              children: widget.tagData.map((item) {
                int index = widget.tagData.indexOf(item);
                return Padding(
                  padding: EdgeInsets.only(right: 6.w),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        tagIndex = index;
                      });
                      if (widget.onTagTap != null) {
                        widget.onTagTap!(index);
                      }
                      // debugPrint('点击了$index');
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                        left: 10.w,
                        right: 10.w,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30.r),
                        color: const Color(0xFFFFFFFF).withValues(alpha: 0.2),
                        border: Border.all(
                          color: tagIndex == index
                              ? const Color(0xFFFFFFFF)
                              : Colors.transparent,
                          width: 0.4.w,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          "$item",
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFFFFFFFF),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        SizedBox(
          height: 10.h,
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: SizedBox(
            // 必须指定高度，因为横向 ListView 在垂直方向是收缩的
            height: 130.h,
            child: ListView(
              scrollDirection: Axis.horizontal, // 关键属性：设置为水平方向
              children: widget.listData.map((item) {
                int index = widget.listData.indexOf(item);
                return Padding(
                  padding: EdgeInsets.only(right: 6.w),
                  child: GestureDetector(
                    onTap: () {
                      // setState(() {
                      //   selectedIndex = index;
                      // });
                      // debugPrint('点击了$index');
                      VideoCaseDialog.videoCase(
                        videoUrl: item.videoUrl ?? '',
                        imageUrl: item.cover ?? '',
                        onTap: () {
                          setState(() {
                            selectedIndex = index;
                          });
                          if (widget.onTap != null) {
                            widget.onTap?.call(index);
                          }
                        },
                      );
                    },
                    child: Container(
                      width: 85.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        border: Border.all(
                          color: selectedIndex == index
                              ? const Color(0xff30e6b8)
                              : Colors.transparent,
                          width: 2.w,
                        ),
                        image: DecorationImage(
                          image: NetworkImage(item.cover ?? ""),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 54.h,
                          ),
                          Image.asset(
                            playIconImg,
                            width: 17.w,
                            height: 19.h,
                          ),
                          SizedBox(
                            height: 30.h,
                          ),
                          SizedBox(
                            width: 66.w,
                            child: Text(
                              "${item.caseName}",
                              overflow: TextOverflow.ellipsis, // 超出显示省略号...
                              maxLines: 1,
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: const Color(0xFFFFFFFF),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}
