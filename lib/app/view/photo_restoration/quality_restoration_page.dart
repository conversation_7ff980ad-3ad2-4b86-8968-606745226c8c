import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../provider/photo_repair/photo_repair_provider.dart';
import '../../widgets/appbar/leading.dart';
import '../../widgets/consumption/consumption_display_widget.dart';
import '../../widgets/video_case/photo_compare_widget.dart';

class QualityRestorationPage extends ConsumerStatefulWidget {
  const QualityRestorationPage({super.key});

  @override
  QualityRestorationPageState createState() => QualityRestorationPageState();
}

class QualityRestorationPageState
    extends ConsumerState<QualityRestorationPage> {
  // 对比组件
  Widget _buildCompare() {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0x21000000),
            Color(0x4518161A),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        border: Border.all(
          color: const Color(0x50FFFFFF),
          width: 0.6,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: OldPhotoCompare(
        before: Stack(
          fit: StackFit.expand,
          children: [
            Image.asset(qualityPhotoDemo, fit: BoxFit.cover),
            Positioned(
              top: 8,
              left: 8,
              child: Image.asset(restorationBeforeTip, width: 52),
            ),
          ],
        ),
        after: Stack(
          fit: StackFit.expand,
          children: [
            Image.asset(qualityPhotoRestorationDemo, fit: BoxFit.cover),
            Positioned(
              top: 8,
              right: 8,
              child: Image.asset(restorationAfterTip, width: 68),
            ),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        initialPosition: 0.5, // 0.0=全显示before, 1.0=全显示after
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Image.asset(
            qualityPhotoRestorationBg,
            width: MediaQuery.sizeOf(context).width,
            fit: BoxFit.fitWidth,
          ),
          Column(
            children: [
              AppBar(
                backgroundColor: Colors.transparent,
                centerTitle: true,
                toolbarHeight: 44.h,
                leading: const Leading(
                  color: Colors.white,
                ),
                actions: [
                  InkWell(
                    onTap: () {
                      context.push("/$workRecordPage", extra: 2);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 5),
                      decoration: BoxDecoration(
                        color: const Color(0x30FFFFFF),
                        borderRadius: BorderRadius.circular(13),
                      ),
                      child: const Text(
                        "生成记录",
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
              ),
              Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Image.asset(
                    qualityRestorationHeader,
                    width: MediaQuery.sizeOf(context).width,
                  ),
                  const Text(
                    "AI超清修复，智能生成更精细",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: _buildCompare(),
              ),
              const SizedBox(height: 60),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: ConsumptionDisplayWidget(consumption: 20),
              ),
              GradientButton(
                onPress: () {
                  ref
                      .read(photoRepairActionProvider.notifier)
                      .commitQualityPhotoRepair();
                },
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.symmetric(vertical: 15),
                radius: 16,
                shadow: false,
                gradient: const LinearGradient(
                  colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                ),
                child: const Text(
                  "立即修复",
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF18161A),
                  ),
                ),
              ),
              SizedBox(height: MediaQuery.paddingOf(context).bottom + 60),
            ],
          )
        ],
      ),
    );
  }
}
