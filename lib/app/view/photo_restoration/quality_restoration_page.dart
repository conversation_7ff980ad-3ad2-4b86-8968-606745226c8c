import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';
import 'package:text_generation_video/config/icon_address.dart';

import '../../provider/photo_repair/photo_repair_provider.dart';
import '../../provider/record/record_provider.dart';
import '../../widgets/appbar/leading.dart';
import '../../widgets/common/work_record_ation.dart';
import '../../widgets/video_case/photo_compare_widget.dart';

class QualityRestorationPage extends ConsumerStatefulWidget {
  const QualityRestorationPage({super.key});

  @override
  QualityRestorationPageState createState() => QualityRestorationPageState();
}

class QualityRestorationPageState
    extends ConsumerState<QualityRestorationPage> {
  // 对比组件
  Widget _buildCompare() {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0x21000000),
            Color(0x4518161A),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        border: Border.all(
          color: const Color(0x50FFFFFF),
          width: 0.6,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: OldPhotoCompare(
        before: Stack(
          fit: StackFit.expand,
          children: [
            Image.asset(qualityPhotoDemo, fit: BoxFit.cover),
            Positioned(
              top: 8,
              left: 8,
              child: Image.asset(restorationBeforeTip, width: 52),
            ),
          ],
        ),
        after: Stack(
          fit: StackFit.expand,
          children: [
            Image.asset(qualityPhotoRestorationDemo, fit: BoxFit.cover),
            Positioned(
              top: 8,
              right: 8,
              child: Image.asset(restorationAfterTip, width: 68),
            ),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        initialPosition: 0.5, // 0.0=全显示before, 1.0=全显示after
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Image.asset(
            qualityPhotoRestorationBg,
            width: MediaQuery.sizeOf(context).width,
            fit: BoxFit.fitWidth,
          ),
          Column(
            children: [
              AppBar(
                backgroundColor: Colors.transparent,
                centerTitle: true,
                toolbarHeight: 44.h,
                leading: const Leading(
                  color: Colors.white,
                ),
                actions: [
                  WorkRecordAction(workType: WorkRecordType.image.type),
                  const SizedBox(width: 16),
                ],
              ),
              Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Image.asset(
                    qualityRestorationHeader,
                    width: MediaQuery.sizeOf(context).width,
                  ),
                  const Text(
                    "AI超清修复，智能生成更精细",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: _buildCompare(),
              ),
              const SizedBox(height: 60),
              BottomActionBar(
                buttonText: "立即修复",
                type: FunctionType.qualityEnhance.type,
                onPress: () {
                  ref
                      .read(photoRepairActionProvider.notifier)
                      .commitQualityPhotoRepair();
                },
              ),
            ],
          )
        ],
      ),
    );
  }
}
