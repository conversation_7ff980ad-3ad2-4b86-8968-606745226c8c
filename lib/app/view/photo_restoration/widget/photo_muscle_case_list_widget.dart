import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_muscle_provider.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_muscle.dart';

class PhotoMuscleCaseListWidget extends ConsumerWidget {
  const PhotoMuscleCaseListWidget({super.key});

  Widget _buildCaseItem(PhotoMuscleCaseItem caseItem, bool isSelected, VoidCallback onTap) {
    var itemWidth = 85.81.w;
    var itemHeight = 115.h;
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: isSelected ? const Color(0xFF30E6B8) : null,
            ),
            child: Container(
              margin: const EdgeInsets.all(2),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(isSelected ? 10 : 12),
                child: CachedNetworkImage(
                  imageUrl: caseItem.caseImage ?? "",
                  width: itemWidth,
                  height: itemHeight,
                  fit: BoxFit.cover,
                  errorWidget: (_, o, s) {
                    return Container(
                      width: itemWidth,
                      height: itemHeight,
                      color: Colors.grey.shade800,
                    );
                  },
                ),
              ),
            ),
          ),
          Container(
            width: itemWidth,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            margin: const EdgeInsets.only(bottom: 2),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7),
                ],
              ),
            ),
            child: Text(
              caseItem.caseName ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaseList(WidgetRef ref) {
    final provider = ref.watch(photoMuscleProvider);
    return provider.when(
      data: (state) {
        if (state.currentCases.isEmpty) {
          return SizedBox(
            height: 115.h,
            child: const Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: Text(
                  "暂无模版数据",
                  style: TextStyle(color: Colors.white60),
                ),
              ),
            ),
          );
        }
        return SizedBox(
          height: 115.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: state.currentCases.length,
            itemBuilder: (context, index) {
              final caseItem = state.currentCases[index];
              final isSelected = state.currentSelectedCase?.id == caseItem.id;
              return Container(
                margin: EdgeInsets.only(
                    right: index < state.currentCases.length - 1 ? 6 : 0),
                child: _buildCaseItem(
                  caseItem,
                  isSelected,
                  () => ref
                      .read(photoMuscleProvider.notifier)
                      .setCaseItem(caseItem),
                ),
              );
            },
          ),
        );
      },
      loading: () {
        return SizedBox(
            height: 115.h,
            child: const Center(child: CircularProgressIndicator()));
      },
      error: (err, stack) {
        return SizedBox(
            height: 115.h, child: const Center(child: Text('无法加载模板')));
      },
    );
  }

  Widget _buildCaseListHeader(WidgetRef ref) {
    final state = ref.watch(photoMuscleProvider).value;
    final initialIndex = state?.currentGender == 2 ? 1 : 0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          "选择增肌强度",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFF565656), width: 0.6),
          ),
          child: DefaultTabController(
            initialIndex: initialIndex,
            length: 2,
            child: TabBar(
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              dividerHeight: 0,
              indicator: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: const Color(0xFF464548),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey,
              labelPadding: EdgeInsets.zero,
              padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              tabs: [_buildItemTab("男"), _buildItemTab("女")],
              onTap: (index) {
                ref.read(photoMuscleProvider.notifier).setGender(index + 1);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemTab(String name) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
      child: Text(name, style: const TextStyle(fontSize: 14)),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCaseListHeader(ref),
        const SizedBox(height: 12),
        _buildCaseList(ref),
      ],
    );
  }
}
