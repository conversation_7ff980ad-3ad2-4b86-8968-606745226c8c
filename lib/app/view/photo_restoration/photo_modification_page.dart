import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/view/photo_restoration/widget/modification_demo_widget.dart';
import 'package:text_generation_video/app/view/photo_restoration/widget/modification_upload_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';

import '../../widgets/appbar/leading.dart';
import '../../widgets/consumption/consumption_display_widget.dart';

class PhotoModificationPage extends ConsumerStatefulWidget {
  const PhotoModificationPage({super.key, this.caseId});

  final int? caseId;

  @override
  PhotoModificationPageState createState() => PhotoModificationPageState();
}

class PhotoModificationPageState extends ConsumerState<PhotoModificationPage> {
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  // 文本输入
  Widget _buildTextInput() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFF565656), width: 0.6),
      ),
      child: TextField(
        onTapOutside: (event) {
          _focusNode.unfocus();
        },
        controller: ref.watch(modificationEditTextProvider),
        focusNode: _focusNode,
        maxLength: 300,
        maxLines: 5,
        style: const TextStyle(fontSize: 14, color: Colors.white),
        decoration: const InputDecoration(
          // counter: SizedBox(),
          counterText: "",
          border: InputBorder.none,
          isDense: true,
          // 紧凑模式
          contentPadding: EdgeInsets.zero,
          // 去掉默认 padding
          hintText: "请输入改图命令，例如：将背景改完马尔代夫海边",
          hintStyle: TextStyle(
            fontSize: 14,
            color: Color(0xFF8A8D93),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "AI改图",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          InkWell(
            onTap: () {
              context.push("/$workRecordPage", extra: 2);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
              decoration: BoxDecoration(
                color: const Color(0x30FFFFFF),
                borderRadius: BorderRadius.circular(13),
              ),
              child: const Text(
                "生成记录",
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          const SizedBox(height: 12),
          const Expanded(child: ModificationUploadWidget()),
          const SizedBox(height: 16),
          ModificationDemoWidget(caseId: widget.caseId),
          const SizedBox(height: 9),
          _buildTextInput(),
          const SizedBox(height: 12),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: ConsumptionDisplayWidget(consumption: 20),
          ),
          GradientButton(
            onPress: () {
              ref.read(photoModificationCurrentProvider.notifier).commit();
            },
            // enable: false,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 15),
            radius: 16,
            shadow: false,
            gradient: const LinearGradient(
              colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
            ),
            child: const Text(
              "开始改图",
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF18161A),
              ),
            ),
          ),
          SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
        ],
      ),
    );
  }
}
