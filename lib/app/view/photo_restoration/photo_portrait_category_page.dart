import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_portrait_provide.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/tab/tab_indicator_capsule.dart';

class PhotoPortraitCategoryPage extends ConsumerStatefulWidget {
  const PhotoPortraitCategoryPage({
    super.key,
    this.categoryId,
  });

  /// 要显示的分类ID
  final int? categoryId;

  @override
  ConsumerState<PhotoPortraitCategoryPage> createState() =>
      _PhotoPortraitCategoryPageState();
}

class _PhotoPortraitCategoryPageState
    extends ConsumerState<PhotoPortraitCategoryPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isTabControllerInitialized = false;

  @override
  void initState() {
    super.initState();
    // 初始化时先用默认值，等数据加载后再更新
    _tabController = TabController(length: 1, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 根据 categoryId 计算初始索引
  int _calculateInitialIndex(List<PhotoPortraitCategory> categories) {
    if (widget.categoryId == null) return 0;

    final index = categories.indexWhere((cat) => cat.id == widget.categoryId);
    return index >= 0 ? index : 0;
  }

  /// 更新 TabController
  void _updateTabController(List<PhotoPortraitCategory> categories) {
    final newLength = categories.length;
    final newInitialIndex = _calculateInitialIndex(categories);

    // 只在第一次初始化或长度发生变化时更新
    if (!_isTabControllerInitialized || _tabController.length != newLength) {
      _tabController.dispose();
      _tabController = TabController(
        length: newLength,
        vsync: this,
        initialIndex: newInitialIndex,
      );
      _isTabControllerInitialized = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoryAsync = ref.watch(fetchphotoPortraitCategoryListProvider);

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        toolbarHeight: 44.h,
        title: categoryAsync.when(
          data: (categories) {
            if (categories != null && categories.isNotEmpty) {
              _updateTabController(categories);
              return _buildCategoryNavigation(context, ref, categories);
            }
            return const Text("分类", style: TextStyle(color: Colors.white));
          },
          loading: () =>
              const Text("加载中...", style: TextStyle(color: Colors.white)),
          error: (error, stack) =>
              const Text("分类", style: TextStyle(color: Colors.white)),
        ),
        leading: const Leading(color: Colors.white),
        actions: [
          categoryAsync.when(
            data: (categories) {
              if (categories == null || categories.isEmpty) {
                return const SizedBox.shrink();
              }
              return GestureDetector(
                onTap: () => _showCategorySheet(context, categories),
                child: const Icon(
                  Icons.format_list_bulleted,
                  color: Colors.white,
                  size: 24,
                ),
              );
            },
            loading: () => const SizedBox.shrink(),
            error: (error, stack) => const SizedBox.shrink(),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: categoryAsync.when(
        data: (categories) {
          if (categories == null || categories.isEmpty) {
            return const Center(
              child: Text(
                "暂无分类数据",
                style: TextStyle(color: Colors.white60),
              ),
            );
          }

          _updateTabController(categories);

          return RefreshIndicator(
            onRefresh: () async {
              ref.invalidate(fetchphotoPortraitCategoryListProvider);
              await ref.read(fetchphotoPortraitCategoryListProvider.future);
            },
            child: TabBarView(
              controller: _tabController,
              physics: const BouncingScrollPhysics(),
              children: categories.map((category) {
                return _buildCategoryContent(context, category);
              }).toList(),
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                "加载失败",
                style: TextStyle(color: Colors.red),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(fetchphotoPortraitCategoryListProvider);
                },
                child: const Text("重试"),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示分类选择弹窗
  void _showCategorySheet(
      BuildContext context, List<PhotoPortraitCategory> categories) {
    showTopSheet(
      context,
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                "全部频道",
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              GestureDetector(
                child: const Icon(Icons.keyboard_arrow_up,
                    color: Colors.white, size: 24),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 标签
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: categories.map((category) {
              return _buildTag(
                category.caseName ?? "",
                isSelected:
                    _tabController.index == categories.indexOf(category),
                onTap: () {
                  _tabController.animateTo(categories.indexOf(category));
                  Navigator.pop(context);
                },
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String text,
      {bool isSelected = false, required VoidCallback onTap}) {
    return InkWell(
      onTap: () => onTap(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.teal : Colors.grey,
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  /// 构建分类导航栏
  Widget _buildCategoryNavigation(
    BuildContext context,
    WidgetRef ref,
    List<PhotoPortraitCategory> categories,
  ) {
    final tabs = categories.map(
      (e) => _buildCategoryTab(context, e.caseName ?? ""),
    );
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      labelPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      dividerHeight: 0,
      labelStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      unselectedLabelStyle:
          const TextStyle(fontSize: 16, fontWeight: FontWeight.normal),
      indicator: const CapsuleTabIndicator(
          color: Color(0xFF30E6B8), bottomMargin: 2, height: 4, width: 16),
      tabs: tabs.toList(),
    );
  }

  /// 构建单个分类标签
  Widget _buildCategoryTab(
    BuildContext context,
    String name,
  ) {
    return Center(
      child: Text(
        name,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.white,
        ),
      ),
    );
  }

  /// 构建分类内容
  Widget _buildCategoryContent(
      BuildContext context, PhotoPortraitCategory category) {
    final details = category.details ?? [];

    if (details.isEmpty) {
      return const Center(
        child: Text(
          "该分类暂无内容",
          style: TextStyle(color: Colors.white54),
        ),
      );
    }

    return GridView.builder(
      padding: EdgeInsets.all(16.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: details.length,
      itemBuilder: (context, index) {
        final detail = details[index];
        return _buildCategoryDetailItem(context, detail);
      },
    );
  }

  /// 构建分类详情项
  Widget _buildCategoryDetailItem(
      BuildContext context, PhotoPortraitCategoryDetail detail) {
    return InkWell(
      onTap: () => _handleDetailItemTap(detail),
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: CachedNetworkImage(
            imageUrl: detail.caseImage ?? "",
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            placeholder: (context, url) => Container(
              color: Colors.grey[800],
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey[800],
              child: const Center(
                child: Icon(
                  Icons.image_not_supported,
                  color: Colors.white54,
                  size: 32,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void showTopSheet(BuildContext context, Widget child) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.transparent,
      barrierLabel: '',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation1, animation2) {
        return Align(
          alignment: Alignment.topCenter,
          child: Material(
            borderRadius: BorderRadius.circular(12),
            color: const Color(0xFF222123),
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                16,
                MediaQuery.paddingOf(context).top + 16,
                16,
                16,
              ),
              child: child,
            ),
          ),
        );
      },
      transitionBuilder: (context, animation1, animation2, child) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, -1), // 从顶部
            end: Offset.zero,
          ).animate(animation1),
          child: child,
        );
      },
    );
  }

  /// 处理详情项点击
  void _handleDetailItemTap(PhotoPortraitCategoryDetail detail) {
    // TODO: 跳转到写真生成页面
    debugPrint("选择写真: ${detail.caseTitle}，ID: ${detail.id}");
    context.push('/$photoPortraitDetailPage', extra: detail.id);
  }
}
