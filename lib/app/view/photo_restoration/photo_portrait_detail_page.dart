import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_portrait_provide.dart';
import 'package:text_generation_video/app/repository/modals/photo/photo_portrait.dart';
import 'package:text_generation_video/app/view/photo_restoration/dialog/photo_portrait_bottom_sheet.dart';
import 'package:text_generation_video/app/widgets/appbar/leading.dart';
import 'package:text_generation_video/app/widgets/consumption/consumption_display_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

class PhotoPortraitDetailPage extends ConsumerWidget {
  const PhotoPortraitDetailPage({super.key, this.detail, this.caseId});

  final int? caseId;
  final PhotoPortraitCategoryDetail? detail;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 如果传入了detail参数，直接使用；否则通过provider请求数据
    Widget bodyWidget;
    if (detail != null) {
      // 使用传入的detail数据
      bodyWidget = _buildDetailContent(context, ref, detail);
    } else {
      // 通过provider请求数据
      final detailAsync = ref.watch(fetchphotoPortraitDetailProvider(caseId ?? 0));
      bodyWidget = detailAsync.when(
        data: (fetchedDetail) => _buildDetailContent(context, ref, fetchedDetail),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => const Center(child: Text('无法加载详情数据')),
      );
    }

    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          centerTitle: true,
          toolbarHeight: 44.h,
          leading: const Leading(
            color: Colors.white,
          ),
          actions: [
            InkWell(
              onTap: () {
                context.push("/$workRecordPage", extra: 2);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                decoration: BoxDecoration(
                  color: const Color(0x30FFFFFF),
                  borderRadius: BorderRadius.circular(13),
                ),
                child: const Text(
                  "生成记录",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
        ),
        body: bodyWidget
      );
  }

  Widget _buildDetailContent(BuildContext context, WidgetRef ref,
      PhotoPortraitCategoryDetail? detail) {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: SizedBox(
                    width: double.infinity,
                    child: CachedNetworkImage(
                      imageUrl: detail?.caseImage ?? "",
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey,
                        child: const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey,
                        child: const Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.white54,
                            size: 32,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                _buildImageSection(ref),
              ],
            ),
          ),
        ),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: ConsumptionDisplayWidget(consumption: 20),
        ),
        GradientButton(
          onPress: () {
            ref
                .read(photoPortraitUploadImageProvider.notifier)
                .commit(detail?.casePrompt ?? "");
          },
          // enable: false,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(vertical: 15),
          radius: 16,
          shadow: false,
          gradient: const LinearGradient(
            colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
          ),
          child: const Text(
            "立即制作",
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF18161A),
            ),
          ),
        ),
        SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
      ],
    );
  }

  Widget _buildImageSection(WidgetRef ref) {
    final uploadImage = ref.watch(photoPortraitUploadImageProvider);
    final history = ref.watch(photoHistoryProvider);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            "请添加一张图片",
            style: TextStyle(
              fontSize: 12,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 65,
            child: history.when(
              data: (imgList) {
                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: imgList.length + 1,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return GestureDetector(
                        onTap: () => _showUploadBottomSheet(context, ref),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              color: const Color(0xFF2D2C2F)),
                          child: Image.asset(modificationSelectIcon),
                        ),
                      );
                    }
                    final item = imgList[index - 1];
                    return GestureDetector(
                      onTap: () => _showImageOptions(context, ref, item),
                      child: _buildImageItem(item, item == uploadImage),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (err, stack) => const Center(child: Text('无法加载历史记录')),
            ),
          ),
        ],
      ),
    );
  }

  void _showUploadBottomSheet(BuildContext context, WidgetRef ref) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PhotoPortraitUploadBottomSheet(
        onConfirm: () {
          RouterUtil.checkLogin(
            context,
            call: () {
              ref.read(photoPortraitUploadImageProvider.notifier).setImage();
            },
          );
          Navigator.of(context).pop();
        },
        onCancel: () {
          Navigator.of(context).pop();
        },
        child: GridView(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: 10,
            crossAxisSpacing: 10,
            childAspectRatio: 1,
          ),
          children: [
            _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/63/2025-09-16/68c8d233e4b0dab42db58088.jpg",
                title: "正确示例",
                isError: false),
            _buildSampleImg(
                imgUrl:
                    "https://cdn.camera.msmds.cn/63/2025-09-16/68c8d233e4b0dab42db58088.jpg",
                title: "错误示例",
                isError: true),
          ],
        ),
      ),
    );
  }

  Widget _buildSampleImg(
      {required String imgUrl, required String title, required bool isError}) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: CachedNetworkImage(
            imageUrl: imgUrl,
            fit: BoxFit.cover,
            height: double.infinity,
            errorWidget: (_, o, s) {
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: const Color(0xFF454348),
                ),
              );
            },
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 40,
          child: ClipRRect(
            borderRadius:
                const BorderRadius.vertical(bottom: Radius.circular(12)),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.white.withAlpha(20),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          left: 0,
          bottom: 10,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    isError ? errorExamWhiteIcon : correctExamIcon,
                    width: 18,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    title,
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showImageOptions(BuildContext context, WidgetRef ref, String imageUrl) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: const Color(0xFF2D2C2F),
      builder: (BuildContext context) {
        return PhotoPortraitSelectImgBottomSheet(
          imgUrl: imageUrl,
          onConfirm: () {
            ref
                .read(photoPortraitUploadImageProvider.notifier)
                .setCurrentImageUrl(imageUrl);
          },
          onDelete: () {
            ref
                .read(photoPortraitUploadImageProvider.notifier)
                .removeImage(imageUrl);
          },
        );
      },
    );
  }

  Widget _buildImageItem(String imageUrl, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: isSelected ? const Color(0xFF30E6B8) : Colors.transparent,
          width: 2,
        ),
      ),
      child: SizedBox(
          child: ClipRRect(
        borderRadius: BorderRadius.circular(isSelected ? 13 : 15),
        child: CachedNetworkImage(
          width: 65,
          imageUrl: imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey,
            child: const Center(
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey,
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                color: Colors.white54,
                size: 32,
              ),
            ),
          ),
        ),
      )),
    );
  }
}
