import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:text_generation_video/app/provider/same_style/same_style_provider.dart';
import 'package:text_generation_video/app/widgets/common/bottom_action_bar.dart';

import '../../provider/photo_repair/photo_repair_provider.dart';
import '../../provider/record/record_provider.dart';
import '../../widgets/appbar/leading.dart';
import '../../widgets/common/work_record_ation.dart';
import 'widget/matting_demo_widget.dart';

class PhotoMattingPage extends ConsumerWidget {
  const PhotoMattingPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        centerTitle: true,
        toolbarHeight: 44.h,
        title: const Text(
          "AI抠图",
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
          ),
        ),
        leading: const Leading(
          color: Colors.white,
        ),
        actions: [
          WorkRecordAction(workType: WorkRecordType.image.type),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 21),
            const MatingDemoWidget(),
            const SizedBox(height: 25),
            BottomActionBar(
              buttonText: "立即抠图",
              type: FunctionType.smartMatting.type,
              onPress: () {
                ref
                    .read(photoRepairActionProvider.notifier)
                    .saliencySegSubmitTask();
              },
            ),
          ],
        ),
      ),
    );
  }
}
