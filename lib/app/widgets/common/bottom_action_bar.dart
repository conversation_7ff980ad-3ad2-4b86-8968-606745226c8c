import 'package:flutter/material.dart';
import 'package:text_generation_video/app/widgets/consumption/consumption_display_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';

/// 一个通用的底部操作栏 Widget，通常用于页面底部，包含消耗提示和操作按钮。
/// [consumption] 是本次操作需要消耗的数量，默认为 0。
/// [onPress] 是点击按钮时的回调函数。
/// [buttonText] 是按钮上显示的文本，默认为 "立即制作"。
/// [enable] 控制按钮是否可点击，默认为 true。
class BottomActionBar extends StatelessWidget {
  const BottomActionBar({
    super.key,
    this.consumption = 0,
    required this.onPress,
    this.buttonText = "立即制作",
    this.enable = true,
  });

  final int consumption;
  final VoidCallback onPress;
  final String buttonText;
  final bool enable;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ConsumptionDisplayWidget(consumption: consumption),
        ),
        GradientButton(
          onPress: onPress,
          enable: enable,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(vertical: 15),
          radius: 16,
          shadow: false,
          gradient: const LinearGradient(
            colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
          ),
          child: Text(
            buttonText,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF18161A),
            ),
          ),
        ),
        SizedBox(height: MediaQuery.paddingOf(context).bottom + 20),
      ],
    );
  }
}
