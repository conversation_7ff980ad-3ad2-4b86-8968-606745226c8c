import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/member/member_provider.dart';
import 'package:text_generation_video/app/widgets/consumption/consumption_display_widget.dart';
import 'package:ui_widgets/ui_widgets.dart';

/// 一个通用的底部操作栏 Widget，通常用于页面底部，包含消耗提示和操作按钮。
/// [consumption] 是本次操作需要消耗的数量。
/// [type] 是本次操作需要消耗的类型，如果传入了类型则会忽略 consumption 的值。
/// [onPress] 是点击按钮时的回调函数。
/// [buttonText] 是按钮上显示的文本，默认为 "立即制作"。
/// [enable] 控制按钮是否可点击，默认为 true。
class BottomActionBar extends ConsumerWidget {
  const BottomActionBar({
    super.key,
    this.type,
    this.consumption,
    required this.onPress,
    this.buttonText = "立即制作",
    this.enable = true,
  }) : assert(consumption != null || type != null, "类型为空时，则需要传入对应的算力消耗值");

  final int? consumption;
  final int? type;
  final VoidCallback onPress;
  final String buttonText;
  final bool enable;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ConsumptionDisplayWidget(consumption: consumption, type: type),
        ),
        GradientButton(
          onPress: () async {
            var checkMember = await ref
                .read(checkMemberProvider.notifier)
                .checkMemberAndPower(
                  consumerPowerItem: consumption,
                  type: type,
                );
            if (!checkMember) return;
            onPress();
          },
          enable: enable,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(vertical: 15),
          radius: 16,
          shadow: false,
          gradient: const LinearGradient(
            colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
          ),
          child: Text(
            buttonText,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF18161A),
            ),
          ),
        ),
        const SizedBox(height: 6),
        const Text("内容由AI生成，仅供参考",
            style: TextStyle(fontSize: 9, color: Color(0xFFA2A2A2))),
        SizedBox(height: MediaQuery.paddingOf(context).bottom),
      ],
    );
  }
}
