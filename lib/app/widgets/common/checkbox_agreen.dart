import 'package:flutter/material.dart';

/// Copyright (C), 2021-2022, <PERSON><PERSON>
/// @ProjectName: msmdsapp-flutter
/// @Package:
/// @ClassName: msm_checkbox
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2022/9/20 18:39
/// @UpdateUser: frankylee
/// @UpdateData: 2022/9/20 18:39
/// @UpdateRemark: checkbox

class CheckboxAgreen extends StatefulWidget {
  const CheckboxAgreen({
    super.key,
    required this.onChanged,
    this.value = false,
    this.child,
    this.size = 13,
    this.radio = 7,
    this.selectedIcon,
    this.selectedColor = const Color(0xFF52C41A),
    this.unselectedColor = const Color(0xFFE4E4E4),
  });

  final Function(bool) onChanged;

  final bool value;

  final Widget? child;

  final double size;

  final double radio;

  final Widget? selectedIcon;

  final Color selectedColor;

  final Color unselectedColor;

  @override
  MsmCheckboxState createState() => MsmCheckboxState();
}

class MsmCheckboxState extends State<CheckboxAgreen> {
  @override
  Widget build(BuildContext context) {
    Widget child = widget.child ?? Container();

    Widget checkIcon = const SizedBox();
    if (widget.value && widget.selectedIcon != null) {
      checkIcon = widget.selectedIcon!;
    } else {
      checkIcon = Container(
        width: widget.size,
        height: widget.size,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: widget.value ? widget.selectedColor : widget.unselectedColor,
          borderRadius: BorderRadius.circular(widget.radio),
          border: Border.all(
            color: widget.value ? widget.selectedColor : Colors.white,
          ),
        ),
        child:
        widget.value
            ? const Icon(Icons.check, size: 10, color: Colors.white)
            : Container(),
      );
    }

    return GestureDetector(
      onTap: () {
        widget.onChanged.call(!widget.value);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          checkIcon,
          const Padding(padding: EdgeInsets.only(right: 8)),
          child,
        ],
      ),
    );
  }
}
