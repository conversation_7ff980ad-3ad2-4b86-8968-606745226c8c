import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../navigation/router.dart';

class WorkRecordAction extends StatelessWidget {
  const WorkRecordAction({
    super.key,
    required this.workType,
  });

  final int workType;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.push("/$workRecordPage", extra: workType);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 5),
        decoration: BoxDecoration(
          color: const Color(0x30FFFFFF),
          borderRadius: BorderRadius.circular(13),
        ),
        child: const Text(
          "生成记录",
          style: TextStyle(
            fontSize: 12,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
