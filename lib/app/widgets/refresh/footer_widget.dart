import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'refresh_container.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: load_bottom_widget
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/23 14:23
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/23 14:23
/// @UpdateRemark: 更新说明
class FooterWidget extends StatefulWidget {
  const FooterWidget({super.key, this.state = LoadState.idle});

  final LoadState state;

  @override
  FooterWidgetState createState() => FooterWidgetState();
}

class FooterWidgetState extends State<FooterWidget> {
  @override
  Widget build(BuildContext context) {
    switch (widget.state) {
      case LoadState.idle:
        return Container();
      case LoadState.loading:
        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: const SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
            ),
          ),
        );
      case LoadState.noMore:
        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Text(
            '到底了～',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF666666),
            ),
          ),
        );
      case LoadState.fail:
        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: const Text('load fail'),
        );
    }
  }
}
