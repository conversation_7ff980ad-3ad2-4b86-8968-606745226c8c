import 'package:flutter/material.dart';

import 'footer_widget.dart';

/// Copyright (C), 2021-2023, <PERSON><PERSON>
/// @ProjectName: vmcard
/// @Package:
/// @ClassName: load_state
/// @Description:
/// @Author: frankylee
/// @CreateDate: 2023/5/23 11:45
/// @UpdateUser: frankylee
/// @UpdateData: 2023/5/23 11:45
/// @UpdateRemark: 更新说明
enum LoadState {
  idle,
  loading,
  noMore,
  fail,
}

class CustomListView<T> extends StatefulWidget {
  const CustomListView({
    super.key,
    this.onRefresh,
    this.onLoadMore,
    required this.data,
    required this.renderItem,
    this.separator,
    this.footerState = LoadState.idle,
    this.header,
    this.footer,
    this.empty,
  });

  final Future<void> Function()? onRefresh;
  final Future<void> Function()? onLoadMore;
  final List<T>? data;
  final Widget Function(BuildContext context, int index) renderItem;
  final Widget Function(BuildContext context, int index)? separator;
  final LoadState? footerState;

  final Widget? header;
  final Widget? footer;

  final Widget? empty;

  @override
  CustomListViewState createState() => CustomListViewState();
}

class CustomListViewState extends State<CustomListView> {
  late ScrollController _scrollController;

  bool _loadMore = false;

  Future _refresh() async {
    _loadMore = true;
    await widget.onRefresh?.call();
    _loadMore = false;
  }

  void _load() async {
    if (widget.footerState == LoadState.noMore ||
        widget.footerState == LoadState.loading ||
        widget.footerState == null) {
      return;
    }
    _loadMore = true;
    await widget.onLoadMore?.call();
    _loadMore = false;
  }

  @override
  void initState() {
    _scrollController = ScrollController()
      ..addListener(() {
        if (_scrollController.position.extentAfter < 300 && !_loadMore) {
          _load();
        }
      });
    super.initState();
  }

  /// custom scrollview
  Widget _buildScrollView() {
    Widget child;
    Widget footer;
    if (widget.data != null && widget.data!.isNotEmpty) {
      child = SliverList.separated(
        itemCount: widget.data?.length ?? 0,
        itemBuilder: (BuildContext context, int index) {
          return widget.renderItem(context, index);
        },
        separatorBuilder: (BuildContext context, int index) {
          if (widget.separator != null) {
            return widget.separator!(context, index);
          }
          return Container();
        },
      );
      footer = FooterWidget(
        state: widget.footerState ?? LoadState.idle,
      );
    } else {
      child = SliverToBoxAdapter(
        child: widget.empty ?? Container(),
      );
      footer = Container();
    }
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: _scrollController,
      slivers: [
        SliverToBoxAdapter(
          child: widget.header,
        ),
        child,
        SliverToBoxAdapter(
          child: widget.footer,
        ),
        SliverToBoxAdapter(
          child: footer,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.onRefresh != null) {
      return RefreshIndicator(
        color: Colors.blueAccent,
        onRefresh: _refresh,
        child: _buildScrollView(),
      );
    }
    return _buildScrollView();
  }
}
