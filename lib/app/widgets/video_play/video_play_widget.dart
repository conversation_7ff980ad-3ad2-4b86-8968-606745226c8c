import 'dart:async';
import 'dart:ui' as ui;
import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

import '../../../config/icon_address.dart';

class VideoPlayWidget extends StatefulWidget {
  const VideoPlayWidget({
    super.key,
    required this.videoUrl,
    this.firstFrameImage,
    this.aspectRatio,
  });

  final String videoUrl; // 视频地址
  final String? firstFrameImage; // 视频封面图或者第一帧图片
  final double? aspectRatio; // 视频宽高比

  @override
  VideoPlayWidgetState createState() => VideoPlayWidgetState();
}

class VideoPlayWidgetState extends State<VideoPlayWidget> {
  late VideoPlayerController _controller;
  double _aspectRatio = 16 / 9;
  bool _isPlaying = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    debugPrint("VideoPlayWidgetState: ${widget.videoUrl}");

    // 从第一帧图片获取宽高比
    if (widget.aspectRatio == null) {
      if (widget.firstFrameImage != null) {
        final provider =
            CachedNetworkImageProvider(widget.firstFrameImage ?? "");
        _getImageAspectRatio(provider).then((ratio) {
          if (mounted) {
            if (ratio != null) {
              setState(() => _aspectRatio = ratio);
            }
          }
        });
      }
    }

    var uri = Uri.parse(widget.videoUrl);
    _controller = VideoPlayerController.networkUrl(uri)
      ..initialize().then(
        (_) {
          if (mounted) {
            setState(() {
              _isInitialized = true;
              _controller.play();
            });
          }
        },
      ).catchError(
        (e) {
          debugPrint('catchError: $e');
        },
      );
    _controller.addListener(handle);
  }

  void handle() {
    setState(() {
      _isPlaying = _controller.value.isPlaying;
    });
  }

  // 通过第一帧视频图片获取比例
  Future<double?> _getImageAspectRatio(ImageProvider imageProvider) async {
    final completer = Completer<ui.Image>();
    final stream = imageProvider.resolve(const ImageConfiguration());
    late final ImageStreamListener listener;
    listener = ImageStreamListener((ImageInfo info, bool _) {
      completer.complete(info.image);
      stream.removeListener(listener);
    });
    stream.addListener(listener);

    final image = await completer.future;
    return image.width / image.height;
  }

  void _togglePlay() {
    if (!_isInitialized) return;

    if (_controller.value.isPlaying) {
      _controller.pause();
      setState(() {
        _isPlaying = false;
      });
    } else {
      _controller.play();
      setState(() {
        _isPlaying = true;
      });
    }
  }

  @override
  void dispose() {
    _controller.removeListener(handle);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 视频比例
        var asp = widget.aspectRatio ?? _aspectRatio;

        // 计算可用空间的最大宽高
        final maxWidth = constraints.maxWidth;
        final maxHeight = constraints.maxHeight;

        // 计算适合可用空间的最佳尺寸
        double width = maxWidth;
        double height = width / asp;

        // 如果计算的高度超过可用高度，则调整宽度
        if (height > maxHeight) {
          height = maxHeight;
          width = height * asp;
        }

        return Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              width: width,
              height: height,
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 600),
                transitionBuilder: (Widget child, Animation<double> animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: FadeTransition(opacity: animation, child: child),
                  );
                },
                child: _isInitialized
                    ? VideoPlayer(_controller)
                    : Stack(
                        children: [
                          CachedNetworkImage(
                            imageUrl: widget.firstFrameImage ?? "",
                          ),
                          Positioned.fill(
                            child: BackdropFilter(
                              filter: ImageFilter.blur(
                                sigmaX: 10.0,
                                sigmaY: 10.0,
                              ),
                              child: Container(
                                color: Colors.white.withValues(alpha: 0.1),
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
            ),
            if (!_isInitialized)
              const CircularProgressIndicator(
                color: Colors.green,
                strokeWidth: 1,
              ),
            if (!_isPlaying)
              GestureDetector(
                onTap: _togglePlay,
                child: Image.asset(
                  recordWorkingPlay,
                  width: 48,
                  height: 48,
                  fit: BoxFit.contain,
                ),
              ),

            // 点击视频暂停/播放
            if (_isPlaying && _isInitialized)
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: _togglePlay,
                child: Container(color: Colors.transparent),
              ),
          ],
        );
      },
    );
  }
}
