import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:text_generation_video/app/navigation/router.dart';
import 'package:text_generation_video/app/provider/member/compute_power_provider.dart';
import 'package:text_generation_video/app/provider/member/member_provider.dart';

import '../../../config/icon_address.dart';

class ConsumptionDisplayWidget extends ConsumerWidget {
  const ConsumptionDisplayWidget({
    super.key,
    this.consumption,
    this.type,
  }) : assert(consumption != null || type != null, "类型为空时，则需要传入对应的算力消耗值");

  // 算力消耗值
  final int? consumption;

  // type对应功能，这里会请求消耗的算力值
  // 如果有传入消耗值则不请求
  final int? type;

  Widget _currentPower(BuildContext context, WidgetRef ref) {
    var currentPower = ref.watch(userPowerBalanceProvider);
    var member = ref.watch(memberInfoProvider);
    var powerText = "开通会员获取灵感值";
    if (member?.hasExpire == true) {
      //过期
      if (currentPower?.powerBalance != null &&
          currentPower!.powerBalance! > 0) {
        powerText = "剩余${currentPower.powerBalance ?? 0}灵感值";
      } else {
        powerText = "续费会员获取灵感值";
      }
    } else if (member?.hasExpire == false) {
      //会员
      powerText = "剩余${currentPower?.powerBalance ?? 0}灵感值";
    }
    return InkWell(
      onTap: () {
        if (member == null || member.hasExpire == true) {
          // 非会员或者会员过期
          context.push("/$memberPage");
        } else {
          context.push("/$computePowerPage");
        }
      },
      child: Row(
        children: [
          Text(
            powerText,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFFFFFFFF),
            ),
          ),
          const SizedBox(width: 6),
          const Icon(
            Icons.arrow_forward_ios_rounded,
            size: 10,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Widget consumptionWidget;
    if (consumption != null) {
      consumptionWidget = Text(
        "本次消耗$consumption灵感值",
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFFFFFFFF),
        ),
      );
    } else {
      consumptionWidget = ref.watch(fetchConsumerPowerProvider(type)).when(
        data: (data) {
          if (data == null) return const SizedBox();
          return Text(
            "本次消耗${data.powerNum}灵感值",
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFFFFFFFF),
            ),
          );
        },
        error: (o, s) {
          return const SizedBox();
        },
        loading: () {
          return const SizedBox();
        },
      );
    }
    return Padding(
      padding: const EdgeInsets.only(top: 12, bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Image.asset(
                consumingInspirationIcon,
                width: 13,
                height: 14,
              ),
              const SizedBox(width: 6),
              consumptionWidget,
            ],
          ),
          _currentPower(context, ref),
        ],
      ),
    );
  }
}
