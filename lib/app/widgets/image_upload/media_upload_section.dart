import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';

import 'upload_status_widget.dart';

/// 通用上传面板，复用图片/视频上传区域的样式与交互。
class MediaUploadSection extends ConsumerWidget {
  const MediaUploadSection({
    super.key,
    required this.modification,
    required this.onSelect,
    required this.label,
    this.subLabel = "",
    this.iconAsset,
    this.uploadingChild,
    this.sidePanel,
    this.height = 450,
    this.mediaType = UploadMediaType.image,
    this.onRetry,
    this.onClear,
  });

  final Modification modification;
  final VoidCallback onSelect;
  final String label;
  final String subLabel;
  final String? iconAsset;
  final Widget? uploadingChild;
  final Widget? sidePanel;
  final double height;
  final UploadMediaType mediaType;
  final VoidCallback? onRetry;
  final VoidCallback? onClear;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.fromLTRB(14, 14, 14, 0),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFF565656), width: 0.6),
      ),
      width: double.infinity,
      height: height,
      padding: const EdgeInsets.all(6),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: const Color(0xFF191619),
              ),
              child: UploadStatusWidget(
                modification: modification,
                mediaType: mediaType,
                defaultChild: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: onSelect,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (iconAsset != null) Image.asset(iconAsset!, width: 26),
                      if (iconAsset != null) const SizedBox(height: 9),
                      Text(
                        label,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF8A8D93),
                        ),
                      ),
                      if (subLabel.isNotEmpty) const SizedBox(height: 4),
                      Text(
                        subLabel,
                        style: const TextStyle(
                          fontSize: 10,
                          color: Color(0xFF8A8D93),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      if (uploadingChild == null) const SizedBox(height: 4),
                    ],
                  ),
                ),
                uploadingChild: uploadingChild,
                onClear: onClear,
                onRetry: onRetry,
              ),
            ),
          ),
          if (sidePanel != null) ...[
            const SizedBox(width: 6),
            sidePanel!,
          ],
        ],
      ),
    );
  }
}
