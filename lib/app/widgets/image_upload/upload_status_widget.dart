import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:video_player/video_player.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

enum UploadMediaType { image, video }

class UploadStatusWidget extends ConsumerWidget {
  const UploadStatusWidget({
    super.key,
    required this.modification,
    this.defaultChild,
    this.uploadingChild,
    this.successChild,
    this.failChild,
    this.mediaType,
    this.onClear,
    this.onRetry,
  });

  final Modification modification;
  final Widget? defaultChild;
  final Widget? uploadingChild;
  final Widget? successChild;
  final Widget? failChild;
  final UploadMediaType? mediaType;
  final VoidCallback? onClear;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Widget child = const SizedBox();
    if (modification.state == 0) {
      // 默认
      child = defaultChild ??
          Center(
            child: InkWell(
              onTap: () {
                RouterUtil.checkLogin(
                  context,
                  call: () {
                    ref
                        .read(photoModificationCurrentProvider.notifier)
                        .selectImg();
                  },
                );
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(modificationSelectIcon, width: 26),
                  const SizedBox(height: 9),
                  const Text(
                    "添加图片",
                    style: TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
                  )
                ],
              ),
            ),
          );
    } else if (modification.state == 1) {
      // 上传中
      child = Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Uploading(),
            const SizedBox(height: 12),
            uploadingChild ?? const Text(
              "上传中",
              style: TextStyle(fontSize: 12, color: Color(0xFFFFFFFF)),
            )
          ],
        ),
      );
    } else if (modification.state == 2) {
      // 上传成功
      child = successChild ?? _buildSuccessPreview();
    } else if (modification.state == 3) {
      // 上传失败
      child = failChild ??
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GradientButton(
                    onPress: () {
                      final retry = onRetry ?? () {
                        RouterUtil.checkLogin(
                          context,
                          call: () {
                            ref
                                .read(photoModificationCurrentProvider.notifier)
                                .selectImg();
                          },
                        );
                      };
                      retry();
                    },
                    shadow: false,
                    padding:
                        const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
                    gradient: const LinearGradient(
                      colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                    ),
                    child: const Text(
                      "重新上传",
                      style: TextStyle(
                        fontSize: 10,
                        color: Color(0xFF18161A),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
    }

    return LayoutBuilder(
      builder: (context, c) {
        debugPrint("NoRecordWidget-maxW: ${c.maxWidth}");
        debugPrint("NoRecordWidget-maxH: ${c.maxHeight}");
        return Stack(
          children: [
            SizedBox(
              width: c.maxWidth,
              height: c.maxHeight,
              child: child,
            ),
            if (modification.state == 2)
              Positioned(
                top: 10,
                right: 16,
                child: InkWell(
                  onTap: () {
                    final clear = onClear ?? () {
                      ref.read(photoModificationCurrentProvider.notifier).clean();
                    };
                    clear();
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child:
                        const Icon(Icons.close, color: Colors.white, size: 18),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildSuccessPreview() {
    final type = mediaType ?? _inferMediaType();
    if (type == UploadMediaType.video) {
      return _UploadVideoPreview(
        remoteUrl: modification.remoteUrl,
        localUrl: modification.localUrl,
      );
    }

    final url = modification.remoteUrl ?? modification.localUrl ?? "";
    if (url.isEmpty) {
      return const SizedBox();
    }
    // 根据URL类型选择显示方式
    if (url.startsWith('http')) {
      return CachedNetworkImage(imageUrl: url);
    } else {
      return Image.file(File(url));
    }
  }

  UploadMediaType _inferMediaType() {
    String? path = modification.remoteUrl ?? modification.localUrl;
    if (path == null) return UploadMediaType.image;
    final lower = path.toLowerCase();
    const videoExtensions = [
      '.mp4',
      '.mov',
      '.m4v',
      '.avi',
      '.flv',
      '.wmv',
      '.mkv',
    ];
    if (videoExtensions.any((ext) => lower.contains(ext))) {
      return UploadMediaType.video;
    }
    return UploadMediaType.image;
  }
}

/// 视频预览
class _UploadVideoPreview extends StatefulWidget {
  const _UploadVideoPreview({
    required this.remoteUrl,
    required this.localUrl,
  });

  final String? remoteUrl;
  final String? localUrl;

  @override
  State<_UploadVideoPreview> createState() => _UploadVideoPreviewState();
}

class _UploadVideoPreviewState extends State<_UploadVideoPreview> {
  VideoPlayerController? _controller;
  Future<void>? _initializeFuture;

  @override
  void initState() {
    super.initState();
    _initController();
  }

  @override
  void didUpdateWidget(covariant _UploadVideoPreview oldWidget) {
    super.didUpdateWidget(oldWidget);
    final oldSource = oldWidget.remoteUrl ?? oldWidget.localUrl;
    final newSource = widget.remoteUrl ?? widget.localUrl;
    if (oldSource != newSource) {
      _disposeController();
      _initController();
    }
  }

  @override
  void dispose() {
    _disposeController();
    super.dispose();
  }

  void _disposeController() {
    _controller?.removeListener(_onVideoChanged);
    _controller?.dispose();
    _controller = null;
    _initializeFuture = null;
  }

  void _initController() {
    final source = widget.remoteUrl?.isNotEmpty == true
        ? widget.remoteUrl
        : widget.localUrl;
    if (source == null || source.isEmpty) {
      return;
    }

    try {
      if (_isNetworkSource(source)) {
        final uri = Uri.parse(source);
        _controller = VideoPlayerController.networkUrl(uri);
      } else if (!kIsWeb) {
        _controller = VideoPlayerController.file(File(source));
      } else {
        final uri = Uri.parse(source);
        _controller = VideoPlayerController.networkUrl(uri);
      }

      _initializeFuture = _controller?.initialize().then((_) {
        if (!mounted) return;
        _controller?.setLooping(true);
        // _controller?.play();
        setState(() {});
      });
      _controller?.addListener(_onVideoChanged);
    } catch (e) {
      debugPrint('Video preview init error: $e');
    }
  }

  bool _isNetworkSource(String path) {
    final uri = Uri.tryParse(path);
    if (uri == null) return false;
    if (!uri.hasScheme) return false;
    return uri.scheme == 'http' || uri.scheme == 'https';
  }

  void _onVideoChanged() {
    if (!mounted) return;
    setState(() {});
  }

  void _togglePlay() {
    if (_controller == null) return;
    if (!_controller!.value.isInitialized) return;
    if (_controller!.value.isPlaying) {
      _controller!.pause();
    } else {
      _controller!.play();
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    if (_controller == null) {
      return const Center(
        child: Icon(
          Icons.videocam,
          color: Colors.white54,
          size: 36,
        ),
      );
    }

    return FutureBuilder<void>(
      future: _initializeFuture,
      builder: (context, snapshot) {
        final initialized = _controller?.value.isInitialized ?? false;
        final aspectRatio = initialized
            ? (_controller?.value.aspectRatio ?? 16 / 9)
            : 16 / 9;

        return Stack(
          alignment: Alignment.center,
          children: [
            AspectRatio(
              aspectRatio: aspectRatio <= 0 ? 16 / 9 : aspectRatio,
              child: initialized
                  ? VideoPlayer(_controller!)
                  : const ColoredBox(color: Color(0xFF1A1A1C)),
            ),
            if (!initialized)
              const CircularProgressIndicator(strokeWidth: 2),
            if (initialized && !(_controller?.value.isPlaying ?? false))
              GestureDetector(
                onTap: _togglePlay,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: .4),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
            Positioned.fill(
              child: Material(
                color: Colors.transparent,
                child: GestureDetector(
                  onTap: _togglePlay,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}


// 图片上传动画
class Uploading extends StatefulWidget {
  const Uploading({super.key});

  @override
  UploadingState createState() => UploadingState();
}
class UploadingState extends State<Uploading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600), // 一圈时间
      vsync: this,
    )..repeat(); // 无限循环旋转
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: Image.asset(uploadLoading, width: 38),
    );
  }
}
