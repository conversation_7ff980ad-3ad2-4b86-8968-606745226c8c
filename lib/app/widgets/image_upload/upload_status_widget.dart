import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/config/icon_address.dart';
import 'package:text_generation_video/utils/router_util.dart';
import 'package:ui_widgets/ui_widgets.dart';

class UploadStatusWidget extends ConsumerWidget {
  const UploadStatusWidget({
    super.key,
    required this.modification,
    this.defaultChild,
    this.uploadingChild,
    this.successChild,
    this.failChild,
  });

  final Modification modification;
  final Widget? defaultChild;
  final Widget? uploadingChild;
  final Widget? successChild;
  final Widget? failChild;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    Widget child = const SizedBox();
    if (modification.state == 0) {
      // 默认
      child = defaultChild ??
          Center(
            child: InkWell(
              onTap: () {
                RouterUtil.checkLogin(
                  context,
                  call: () {
                    ref
                        .read(photoModificationCurrentProvider.notifier)
                        .selectImg();
                  },
                );
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Image.asset(modificationSelectIcon, width: 26),
                  const SizedBox(height: 9),
                  const Text(
                    "添加图片",
                    style: TextStyle(fontSize: 12, color: Color(0xFF8A8D93)),
                  )
                ],
              ),
            ),
          );
    } else if (modification.state == 1) {
      // 上传中
      child = Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Uploading(),
            const SizedBox(height: 12),
            uploadingChild ?? const Text(
              "上传中",
              style: TextStyle(fontSize: 12, color: Color(0xFFFFFFFF)),
            )
          ],
        ),
      );
    } else if (modification.state == 2) {
      // 上传成功
      child = successChild ??
          CachedNetworkImage(imageUrl: modification.remoteUrl ?? "");
    } else if (modification.state == 3) {
      // 上传失败
      child = failChild ??
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GradientButton(
                    onPress: () {
                      RouterUtil.checkLogin(
                        context,
                        call: () {
                          ref
                              .read(photoModificationCurrentProvider.notifier)
                              .selectImg();
                        },
                      );
                    },
                    shadow: false,
                    padding:
                        const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
                    gradient: const LinearGradient(
                      colors: [Color(0xFF30E6B8), Color(0xFF30E6B8)],
                    ),
                    child: const Text(
                      "重新上传",
                      style: TextStyle(
                        fontSize: 10,
                        color: Color(0xFF18161A),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
    }

    return LayoutBuilder(
      builder: (context, c) {
        debugPrint("NoRecordWidget-maxW: ${c.maxWidth}");
        debugPrint("NoRecordWidget-maxH: ${c.maxHeight}");
        return Stack(
          children: [
            SizedBox(
              width: c.maxWidth,
              height: c.maxHeight,
              child: child,
            ),
            if (modification.state == 2)
              Positioned(
                top: 10,
                right: 16,
                child: InkWell(
                  onTap: () {
                    ref.read(photoModificationCurrentProvider.notifier).clean();
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child:
                        const Icon(Icons.close, color: Colors.white, size: 18),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

// 图片上传动画
class Uploading extends StatefulWidget {
  const Uploading({super.key});

  @override
  UploadingState createState() => UploadingState();
}

class UploadingState extends State<Uploading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600), // 一圈时间
      vsync: this,
    )..repeat(); // 无限循环旋转
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _controller,
      child: Image.asset(uploadLoading, width: 38),
    );
  }
}
