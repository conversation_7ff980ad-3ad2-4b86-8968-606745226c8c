import 'package:flutter/material.dart';
import 'package:text_generation_video/app/provider/photo_repair/photo_modification_provider.dart';
import 'package:text_generation_video/app/widgets/image_upload/upload_status_widget.dart';
import 'package:text_generation_video/config/icon_address.dart';

class MultiImageUploadPanel extends StatelessWidget {
  const MultiImageUploadPanel({
    super.key,
    required this.items,
    required this.onPick,
    required this.onRemove,
    required this.onRetry,
    this.crossAxisCount = 2,
    this.spacing = 10,
    this.aspectRatio = 1,
    this.addLabel = '上传图片',
  });

  final List<Modification> items;
  final void Function(int index) onPick;
  final void Function(int index) onRemove;
  final void Function(int index) onRetry;
  final int crossAxisCount;
  final double spacing;
  final double aspectRatio;
  final String addLabel;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      itemCount: items.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        mainAxisSpacing: spacing,
        crossAxisSpacing: spacing,
        childAspectRatio: aspectRatio,
      ),
      itemBuilder: (context, index) {
        return _UploadTile(
          index: index,
          modification: items[index],
          onPick: onPick,
          onRemove: onRemove,
          onRetry: onRetry,
          addLabel: addLabel,
        );
      },
    );
  }
}

class _UploadTile extends StatelessWidget {
  const _UploadTile({
    required this.index,
    required this.modification,
    required this.onPick,
    required this.onRemove,
    required this.onRetry,
    required this.addLabel,
  });

  final int index;
  final Modification modification;
  final void Function(int index) onPick;
  final void Function(int index) onRemove;
  final void Function(int index) onRetry;
  final String addLabel;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF2D2C2F),
        borderRadius: BorderRadius.circular(14),
        border: Border.all(color: const Color(0xFF565656), width: 0.6),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(11),
        child: UploadStatusWidget(
          modification: modification,
          mediaType: UploadMediaType.image,
          defaultChild: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () => onPick(index),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(modificationSelectIcon, width: 26),
                const SizedBox(height: 8),
                Text(
                  "$addLabel${index + 1}",
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF8A8D93),
                  ),
                ),
              ],
            ),
          ),
          uploadingChild: const Text(
            '上传中',
            style: TextStyle(fontSize: 12, color: Colors.white),
          ),
          onClear: () => onRemove(index),
          onRetry: () => onRetry(index),
        ),
      ),
    );
  }
}
