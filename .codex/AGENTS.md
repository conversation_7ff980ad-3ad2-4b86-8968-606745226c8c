# Flutter 项目规范与 MCP 集成指南

## 项目结构与模块组织

- **`lib/main.dart`**: 应用入口点，负责初始化全局 Provider 并配置路由。
- **`lib/app/`**: 存放核心功能流程（页面、视图模型、路由）；新增屏幕时需遵循现有路由命名规范。
- **`lib/common/`**: 共享 UI 组件和混入（mixins）；新增通用组件前优先考虑复用此目录下的内容。
- **`lib/config/`**: 环境常量、远程端点及功能开关；不同构建风味（flavor）的覆盖配置存放于此。
- **`lib/utils/`**: 纯工具函数（如格式化器、存储适配器）；避免在此层存放长生命周期状态。
- **`assets/images/`**: 静态资源目录，通过 `pubspec.yaml` 引用；添加资源后需执行 `flutter pub get`。
- **`test/`**: 测试目录，结构镜像 `lib/`；部件（widget）测试按功能模块分组存放以保持清晰性。
- **平台目录（`android/`、`ios/` 等）**: 仅在需要平台特定代码时修改。

---

## 构建、测试与开发命令

- **`flutter pub get`**: 解析依赖并更新生成的插件注册。
- **`flutter pub run build_runner build --delete-conflicting-outputs`**: 模型或 Provider 变更后重新生成 Riverpod 和 JSON 适配器。
- **`dart run custom_lint`**: 执行自定义 Lint 规则；审查前需修复或抑制所有发现项。
- **`flutter analyze`**: 运行静态检查；CI 要求报告无异常。
- **`flutter test`**: 执行单元和部件测试；本地验证覆盖率时可添加 `--coverage` 参数。
- **`flutter run -d <device>`**: 在已连接设备或模拟器上启动应用进行冒烟测试。

---

## 编码风格与命名规范

- 遵循 `flutter_lints`：2 空格缩进，部件树中使用尾部逗号，提交前执行 `dart format`。
- **PascalCase**: 部件和 Provider 命名。
- **lowerCamelCase**: 方法和变量命名。
- **SCREAMING_SNAKE_CASE**: 仅用于 `config/` 中的常量。
- Riverpod Provider 命名格式为 `<功能><能力>Provider`，并放置于对应部件旁以提高可发现性。
- 保持部件文件聚焦；被多个屏幕复用时拆解至 `lib/common/`。
- 优先选用 `AsyncNotifierProvider` 和 `NotifierProvider`，避免使用 `StateProvider`、`StateNotifierProvider` 和 `ChangeNotifierProvider`。

---

## 配置与安全

- 禁止硬编码 API 密钥；通过安全存储加载，并在 `lib/config/` 下文档化设置步骤。
- 仅在引入新能力时更新平台特定的 plist/manifest 文件，并在 PR 清单中记录变更。

---
🗣️ 语言规则

1. 只允许使用中文回答 - 所有思考、分析、解释和回答都必须使用中文
2. 中文优先 - 优先使用中文术语、表达方式和命名规范
3. 中文注释 - 生成的代码注释和文档都应使用中文
4. 中文思维 - 思考过程和逻辑分析都使用中文进行